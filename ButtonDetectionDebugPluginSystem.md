# Button Detection Debug Plugin System

## Core Features

- Save intermediate HSV masks to files

- Visualization plugin for debug analysis

- Backward compatible debug mode

- Organized debug output structure

- Integration with existing test scripts

## Tech Stack

{
  "language": "Python",
  "libraries": [
    "OpenCV",
    "NumPy",
    "OS/pathlib"
  ],
  "architecture": "Plugin-based debugging system"
}

## Design

Non-intrusive debug system that saves intermediate results (red/green masks, HSV images, processed images) to timestamped directories without modifying the detect_buttons function's return value. Includes an interactive visualization plugin for analyzing saved debug data.

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] Create debug output directory structure for saving intermediate results

[X] Modify detect_buttons function to optionally save HSV masks and intermediate images

[X] Implement file naming convention with timestamps for organized debug output

[X] Create visualization plugin script that loads and displays saved intermediate results

[X] Update detect_all_buttons.py to enable debug mode when needed

[X] Test the complete debugging workflow and verify backward compatibility
