import cv2
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from scipy import stats
import os

class ImageStatisticsAnalyzer:
    """Analyze statistical differences between two sets of button images."""
    
    def __init__(self, mustpass_dir, topass_dir):
        self.mustpass_dir = Path(mustpass_dir)
        self.topass_dir = Path(topass_dir)
        self.results = {}
        
    def extract_image_features(self, image_path):
        """Extract various statistical features from an image."""
        image = cv2.imread(str(image_path))
        if image is None:
            return None
            
        # Convert to different color spaces
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        
        features = {}
        
        # Basic brightness statistics
        features['mean_brightness'] = np.mean(gray)
        features['std_brightness'] = np.std(gray)
        features['min_brightness'] = np.min(gray)
        features['max_brightness'] = np.max(gray)
        features['median_brightness'] = np.median(gray)
        
        # Brightness percentiles
        features['brightness_25th'] = np.percentile(gray, 25)
        features['brightness_75th'] = np.percentile(gray, 75)
        features['brightness_range'] = features['max_brightness'] - features['min_brightness']
        
        # Color channel statistics (BGR)
        for i, channel in enumerate(['blue', 'green', 'red']):
            channel_data = image[:, :, i]
            features[f'{channel}_mean'] = np.mean(channel_data)
            features[f'{channel}_std'] = np.std(channel_data)
            
        # HSV statistics
        features['hue_mean'] = np.mean(hsv[:, :, 0])
        features['saturation_mean'] = np.mean(hsv[:, :, 1])
        features['value_mean'] = np.mean(hsv[:, :, 2])
        features['hue_std'] = np.std(hsv[:, :, 0])
        features['saturation_std'] = np.std(hsv[:, :, 1])
        features['value_std'] = np.std(hsv[:, :, 2])
        
        # LAB color space statistics
        features['l_mean'] = np.mean(lab[:, :, 0])  # Lightness
        features['a_mean'] = np.mean(lab[:, :, 1])  # Green-Red
        features['b_mean'] = np.mean(lab[:, :, 2])  # Blue-Yellow
        
        # Texture features using Laplacian variance
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        features['texture_variance'] = laplacian.var()
        
        # Edge density
        edges = cv2.Canny(gray, 50, 150)
        features['edge_density'] = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        # Contrast measures
        features['rms_contrast'] = np.sqrt(np.mean((gray - np.mean(gray)) ** 2))
        features['michelson_contrast'] = (features['max_brightness'] - features['min_brightness']) / (features['max_brightness'] + features['min_brightness']) if (features['max_brightness'] + features['min_brightness']) > 0 else 0
        
        # Histogram features
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist = hist.flatten() / hist.sum()  # Normalize
        features['histogram_entropy'] = -np.sum(hist * np.log2(hist + 1e-10))
        features['histogram_skewness'] = stats.skew(hist)
        features['histogram_kurtosis'] = stats.kurtosis(hist)
        
        return features
    
    def analyze_datasets(self):
        """Analyze both datasets and compute statistics."""
        print("Analyzing mustPass images...")
        mustpass_files = list(self.mustpass_dir.glob('*.png'))
        mustpass_features = []
        
        for img_path in mustpass_files:
            features = self.extract_image_features(img_path)
            if features:
                features['category'] = 'mustPass'
                features['filename'] = img_path.name
                mustpass_features.append(features)
        
        print("Analyzing toPass images...")
        topass_files = list(self.topass_dir.glob('*.png'))
        topass_features = []
        
        for img_path in topass_files:
            features = self.extract_image_features(img_path)
            if features:
                features['category'] = 'toPass'
                features['filename'] = img_path.name
                topass_features.append(features)
        
        # Combine all features
        all_features = mustpass_features + topass_features
        self.df = pd.DataFrame(all_features)
        
        print(f"Analyzed {len(mustpass_features)} mustPass images and {len(topass_features)} toPass images")
        return self.df
    
    def compute_statistical_differences(self):
        """Compute statistical differences between the two categories."""
        if not hasattr(self, 'df'):
            raise ValueError("Must run analyze_datasets() first")
        
        # Get feature columns (exclude category and filename)
        feature_cols = [col for col in self.df.columns if col not in ['category', 'filename']]
        
        results = []
        
        for feature in feature_cols:
            mustpass_data = self.df[self.df['category'] == 'mustPass'][feature]
            topass_data = self.df[self.df['category'] == 'toPass'][feature]
            
            # Basic statistics
            mustpass_mean = mustpass_data.mean()
            topass_mean = topass_data.mean()
            mustpass_std = mustpass_data.std()
            topass_std = topass_data.std()
            
            # Statistical tests
            t_stat, p_value = stats.ttest_ind(mustpass_data, topass_data)
            effect_size = abs(mustpass_mean - topass_mean) / np.sqrt((mustpass_std**2 + topass_std**2) / 2)  # Cohen's d
            
            results.append({
                'feature': feature,
                'mustpass_mean': mustpass_mean,
                'mustpass_std': mustpass_std,
                'topass_mean': topass_mean,
                'topass_std': topass_std,
                'mean_difference': mustpass_mean - topass_mean,
                'relative_difference': (mustpass_mean - topass_mean) / topass_mean * 100 if topass_mean != 0 else 0,
                't_statistic': t_stat,
                'p_value': p_value,
                'effect_size': effect_size,
                'significant': p_value < 0.05
            })
        
        self.stats_df = pd.DataFrame(results)
        return self.stats_df
    
    def create_visualizations(self, output_dir='./data/button-debug-saves/leftButton/analysis'):
        """Create comprehensive visualizations of the differences."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. Top discriminative features
        top_features = self.stats_df.nlargest(10, 'effect_size')
        
        plt.figure(figsize=(12, 8))
        plt.subplot(2, 2, 1)
        sns.barplot(data=top_features, x='effect_size', y='feature')
        plt.title('Top 10 Most Discriminative Features (Effect Size)')
        plt.xlabel('Cohen\'s d (Effect Size)')
        
        # 2. P-values of significant features
        significant_features = self.stats_df[self.stats_df['significant']].nsmallest(10, 'p_value')
        
        plt.subplot(2, 2, 2)
        sns.barplot(data=significant_features, x='p_value', y='feature')
        plt.title('Most Significant Features (Lowest P-values)')
        plt.xlabel('P-value')
        plt.axvline(x=0.05, color='red', linestyle='--', alpha=0.7, label='α=0.05')
        plt.legend()
        
        # 3. Mean differences
        plt.subplot(2, 2, 3)
        top_diff = self.stats_df.nlargest(10, 'relative_difference')
        sns.barplot(data=top_diff, x='relative_difference', y='feature')
        plt.title('Largest Relative Differences (%)')
        plt.xlabel('Relative Difference (%)')
        
        # 4. Distribution comparison for top feature
        top_feature = top_features.iloc[0]['feature']
        plt.subplot(2, 2, 4)
        sns.histplot(data=self.df, x=top_feature, hue='category', alpha=0.7, bins=20)
        plt.title(f'Distribution Comparison: {top_feature}')
        
        plt.tight_layout()
        plt.savefig(output_dir / 'statistical_analysis_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # Create detailed comparison plots for top 6 features
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, (_, row) in enumerate(top_features.head(6).iterrows()):
            feature = row['feature']
            ax = axes[i]
            
            # Box plot
            sns.boxplot(data=self.df, x='category', y=feature, ax=ax)
            ax.set_title(f'{feature}\n(Effect Size: {row["effect_size"]:.3f}, p={row["p_value"]:.4f})')
            
            # Add mean values as text
            mustpass_mean = row['mustpass_mean']
            topass_mean = row['topass_mean']
            ax.text(0, mustpass_mean, f'μ={mustpass_mean:.2f}', ha='center', va='bottom', fontweight='bold')
            ax.text(1, topass_mean, f'μ={topass_mean:.2f}', ha='center', va='bottom', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(output_dir / 'top_features_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # Create correlation heatmap
        feature_cols = [col for col in self.df.columns if col not in ['category', 'filename']]
        correlation_matrix = self.df[feature_cols].corr()
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0, 
                   square=True, cbar_kws={'shrink': 0.8})
        plt.title('Feature Correlation Matrix')
        plt.tight_layout()
        plt.savefig(output_dir / 'feature_correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Visualizations saved to {output_dir}")
    
    def generate_report(self, output_dir='./data/button-debug-saves/leftButton/analysis'):
        """Generate a comprehensive text report."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        report_path = output_dir / 'analysis_report.txt'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("BUTTON IMAGE STATISTICAL ANALYSIS REPORT\n")
            f.write("=" * 80 + "\n\n")
            
            # Dataset summary
            f.write("DATASET SUMMARY:\n")
            f.write("-" * 40 + "\n")
            mustpass_count = len(self.df[self.df['category'] == 'mustPass'])
            topass_count = len(self.df[self.df['category'] == 'toPass'])
            f.write(f"mustPass images: {mustpass_count}\n")
            f.write(f"toPass images: {topass_count}\n")
            f.write(f"Total features analyzed: {len(self.stats_df)}\n\n")
            
            # Most discriminative features
            f.write("TOP 10 MOST DISCRIMINATIVE FEATURES:\n")
            f.write("-" * 40 + "\n")
            top_features = self.stats_df.nlargest(10, 'effect_size')
            
            for i, (_, row) in enumerate(top_features.iterrows(), 1):
                f.write(f"{i:2d}. {row['feature']:<25} ")
                f.write(f"Effect Size: {row['effect_size']:6.3f} ")
                f.write(f"P-value: {row['p_value']:8.6f} ")
                f.write(f"({'Significant' if row['significant'] else 'Not Significant'})\n")
                f.write(f"    mustPass: {row['mustpass_mean']:8.2f} ± {row['mustpass_std']:6.2f}\n")
                f.write(f"    toPass:   {row['topass_mean']:8.2f} ± {row['topass_std']:6.2f}\n")
                f.write(f"    Difference: {row['mean_difference']:8.2f} ({row['relative_difference']:+6.1f}%)\n\n")
            
            # Classification recommendations
            f.write("CLASSIFICATION RECOMMENDATIONS:\n")
            f.write("-" * 40 + "\n")
            
            # Find the best single feature for classification
            best_feature = top_features.iloc[0]
            threshold = (best_feature['mustpass_mean'] + best_feature['topass_mean']) / 2
            
            f.write(f"Best single feature for classification: {best_feature['feature']}\n")
            f.write(f"Suggested threshold: {threshold:.2f}\n")
            f.write(f"Classification rule: \n")
            if best_feature['mustpass_mean'] > best_feature['topass_mean']:
                f.write(f"  - If {best_feature['feature']} > {threshold:.2f} → likely mustPass\n")
                f.write(f"  - If {best_feature['feature']} ≤ {threshold:.2f} → likely toPass\n")
            else:
                f.write(f"  - If {best_feature['feature']} < {threshold:.2f} → likely mustPass\n")
                f.write(f"  - If {best_feature['feature']} ≥ {threshold:.2f} → likely toPass\n")
            
            f.write(f"\nAlternative features to consider:\n")
            for i, (_, row) in enumerate(top_features.iloc[1:4].iterrows(), 2):
                f.write(f"{i}. {row['feature']} (Effect Size: {row['effect_size']:.3f})\n")
        
        # Save detailed statistics to CSV
        self.stats_df.to_csv(output_dir / 'detailed_statistics.csv', index=False)
        self.df.to_csv(output_dir / 'raw_features.csv', index=False)
        
        print(f"Report saved to {report_path}")
        print(f"Detailed statistics saved to {output_dir / 'detailed_statistics.csv'}")
        print(f"Raw features saved to {output_dir / 'raw_features.csv'}")

def main():
    """Main function to run the analysis."""
    # Initialize analyzer
    analyzer = ImageStatisticsAnalyzer(
        mustpass_dir='./data/button-debug-saves/leftButton/mustPass',
        topass_dir='./data/button-debug-saves/leftButton/toPass'
    )
    
    # Run analysis
    print("Starting image analysis...")
    df = analyzer.analyze_datasets()
    
    print("Computing statistical differences...")
    stats_df = analyzer.compute_statistical_differences()
    
    print("Creating visualizations...")
    analyzer.create_visualizations()
    
    print("Generating report...")
    analyzer.generate_report()
    
    print("\nAnalysis complete!")
    print("Check the './data/button-debug-saves/leftButton/analysis' directory for results.")
    
    # Print quick summary
    print("\nQUICK SUMMARY:")
    print("-" * 50)
    top_3 = stats_df.nlargest(3, 'effect_size')
    for i, (_, row) in enumerate(top_3.iterrows(), 1):
        print(f"{i}. {row['feature']}: Effect Size = {row['effect_size']:.3f}")
        print(f"   mustPass: {row['mustpass_mean']:.2f}, toPass: {row['topass_mean']:.2f}")
        print(f"   Difference: {row['relative_difference']:+.1f}%")

if __name__ == "__main__":
    main()