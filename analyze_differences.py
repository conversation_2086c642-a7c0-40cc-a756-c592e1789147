import cv2
import numpy as np
import glob
import os

def analyze_image_characteristics(image_path, label):
    """Analyze the characteristics of an image"""
    image = cv2.imread(image_path)
    if image is None:
        return
    
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    h, s, v = cv2.split(hsv)
    
    print(f"\n{label}: {os.path.basename(image_path)}")
    print(f"  Image size: {image.shape}")
    print(f"  V channel - Mean: {np.mean(v):.1f}, Max: {np.max(v)}, Min: {np.min(v)}")
    print(f"  S channel - Mean: {np.mean(s):.1f}, Max: {np.max(s)}, Min: {np.min(s)}")
    print(f"  High brightness pixels (V>230): {np.sum(v > 230)} ({np.sum(v > 230)/v.size*100:.1f}%)")
    print(f"  Low saturation pixels (S<40): {np.sum(s < 40)} ({np.sum(s < 40)/s.size*100:.1f}%)")
    print(f"  Bright+Low sat pixels: {np.sum((v > 230) & (s < 40))} ({np.sum((v > 230) & (s < 40))/s.size*100:.1f}%)")

def simple_color_detection_test(image_path, label):
    """Test simple color detection on an image"""
    image = cv2.imread(image_path)
    if image is None:
        return
    
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Original red detection ranges
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # Original green detection range
    lower_green = np.array([40, 50, 50])
    upper_green = np.array([80, 255, 255])
    
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)
    green_mask = cv2.inRange(hsv, lower_green, upper_green)
    
    red_pixels = np.sum(red_mask > 0)
    green_pixels = np.sum(green_mask > 0)
    
    print(f"  Color detection - Red pixels: {red_pixels}, Green pixels: {green_pixels}")
    
    # Try relaxed thresholds
    lower_red1_relaxed = np.array([0, 30, 30])
    upper_red1_relaxed = np.array([15, 255, 255])
    lower_red2_relaxed = np.array([155, 30, 30])
    upper_red2_relaxed = np.array([180, 255, 255])
    lower_green_relaxed = np.array([35, 30, 30])
    upper_green_relaxed = np.array([85, 255, 255])
    
    red_mask1_rel = cv2.inRange(hsv, lower_red1_relaxed, upper_red1_relaxed)
    red_mask2_rel = cv2.inRange(hsv, lower_red2_relaxed, upper_red2_relaxed)
    red_mask_rel = cv2.bitwise_or(red_mask1_rel, red_mask2_rel)
    green_mask_rel = cv2.inRange(hsv, lower_green_relaxed, upper_green_relaxed)
    
    red_pixels_rel = np.sum(red_mask_rel > 0)
    green_pixels_rel = np.sum(green_mask_rel > 0)
    
    print(f"  Relaxed detection - Red pixels: {red_pixels_rel}, Green pixels: {green_pixels_rel}")

def main():
    print("Analyzing Differences Between mustPass and toPass Images")
    print("=" * 70)
    
    # Analyze mustPass images
    mustPass_dir = "./data/button-debug-saves/allButtons/mustPass"
    mustPass_images = glob.glob(os.path.join(mustPass_dir, "*.jpg"))
    
    print("\n=== MUSTPASS IMAGES ANALYSIS ===")
    for img_path in mustPass_images[:2]:  # Analyze first 2
        analyze_image_characteristics(img_path, "mustPass")
        simple_color_detection_test(img_path, "mustPass")
    
    # Analyze toPass images
    toPass_dir = "./data/button-debug-saves/allButtons/toPass"
    toPass_images = glob.glob(os.path.join(toPass_dir, "*.jpg"))
    
    print("\n=== TOPASS IMAGES ANALYSIS ===")
    for img_path in toPass_images:
        analyze_image_characteristics(img_path, "toPass")
        simple_color_detection_test(img_path, "toPass")
    
    print("\n=== RECOMMENDATIONS ===")
    print("Based on the analysis above, we can determine:")
    print("1. How much brighter the toPass images are")
    print("2. Whether color detection ranges need adjustment")
    print("3. What preprocessing might be most effective")

if __name__ == "__main__":
    main()