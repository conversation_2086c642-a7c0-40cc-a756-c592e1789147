from RM_API2.Python.Robotic_Arm.rm_robot_interface import *
import numpy as np
from scipy.spatial.transform import Rotation as R
from sklearn.decomposition import PCA
import os
import glob
import time
import copy

# Import custom exceptions
try:
    from .exceptions import (
        ButtonDetectionError, Button<PERSON><PERSON>roachError, ButtonOperationError,
        ArmStateError, ArmMovementError, EmergencyStopError,
        ButtonStateError, ButtonConfigError, GripperStateError,
        GripperControlError, ArmTrajectoryTimeoutError, CameraError,
        MemoryError, UnknownError
    )
except ImportError:
    # Fallback if exceptions module is not available
    class ButtonDetectionError(Exception): pass
    class ButtonApproachError(Exception): pass
    class ButtonOperationError(Exception): pass
    class ArmStateError(Exception): pass
    class ArmMovementError(Exception): pass
    class EmergencyStopError(Exception): pass
    class ButtonStateError(Exception): pass
    class ButtonConfigError(Exception): pass
    class GripperStateError(Exception): pass
    class GripperControlError(Exception): pass
    class ArmTrajectoryTimeoutError(Exception): pass
    class CameraError(Exception): pass
    class MemoryError(Exception): pass
    class UnknownError(Exception): pass

# 在文件顶部（import之后）添加
observation_checkpoint = None

def set_observation_checkpoint(joint_list):
    global observation_checkpoint
    observation_checkpoint = list(joint_list) if joint_list is not None else None

def get_observation_checkpoint():
    global observation_checkpoint
    return observation_checkpoint

knob_checkpoint = None

def set_knob_checkpoint(angle):
    global knob_checkpoint
    if angle is not None:
        knob_checkpoint = angle

def get_knob_checkpoint():
    global knob_checkpoint
    return knob_checkpoint



arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
force_type = rm_force_type_e.RM_MODEL_RM_B_E
algo_handle = Algo(arm_model, force_type)

# 新
camera_to_effector_rotation_matrix = np.array([
    [-0.03914598, -0.99869253, -0.03287572],
    [ 0.99923066, -0.0392032 ,  0.00109761],
    [-0.00238501, -0.03280746,  0.99945884]
])
camera_to_effector_translation_vector = np.array([
    0.08695189,
    -0.04231192,
    0.00636331
])


T_camera_to_effector = np.eye(4)
T_camera_to_effector[:3, :3] = camera_to_effector_rotation_matrix
T_camera_to_effector[:3, 3] = camera_to_effector_translation_vector


def decompose_transform(matrix):
    """
    将矩阵转化为位姿
    """

    translation = matrix[:3, 3]
    rotation = matrix[:3, :3]

    # Convert rotation matrix to euler angles (rx, ry, rz)
    sy = np.sqrt(rotation[0, 0] * rotation[0, 0] + rotation[1, 0] * rotation[1, 0])
    singular = sy < 1e-6

    if not singular:
        rx = np.arctan2(rotation[2, 1], rotation[2, 2])
        ry = np.arctan2(-rotation[2, 0], sy)
        rz = np.arctan2(rotation[1, 0], rotation[0, 0])
    else:
        rx = np.arctan2(-rotation[1, 2], rotation[1, 1])
        ry = np.arctan2(-rotation[2, 0], sy)
        rz = 0

    return np.array([translation[0], translation[1], translation[2], rx, ry, rz])


def build_object_coordinate_system(button_1, button_2, button_3, button_4, robust_normal=False, point_cloud=None):
    """
    基于4个button点建立物体坐标系
    
    参数顺序: bottom_left, bottom_right, top_left, top_right
    
    参数:
    button_1: bottom_left 按钮坐标
    button_2: bottom_right 按钮坐标  
    button_3: top_left 按钮坐标
    button_4: top_right 按钮坐标
    robust_normal: 是否使用鲁棒法向量计算方法
    point_cloud: 原始点云数据 (numpy array, 用于robust_normal计算)
    
    返回:
    x_axis, y_axis, z_axis: 归一化的坐标轴向量
    """
    # 1. 将button点转换为numpy数组
    buttons = np.array([button_1, button_2, button_3, button_4])
    
    # 明确按钮的实际含义
    bottom_left = button_1    # buttons[0]
    bottom_right = button_2   # buttons[1]
    top_left = button_3       # buttons[2]
    top_right = button_4      # buttons[3]
    
    # 2. 计算法向量作为z轴
    if robust_normal and point_cloud is not None:
        # 使用基于点云的鲁棒法向量计算方法
        # print(f"使用点云数据计算鲁棒法向量，点云大小: {len(point_cloud)}")
        
        # 基于buttons平面筛选点云
        button_center = np.mean(buttons, axis=0)
        button_pca = PCA(n_components=3)
        button_pca.fit(buttons)
        button_normal = button_pca.components_[2]
        
        # 筛选接近buttons平面的点
        plane_depth_range = 0.02  # 2cm范围
        distances_to_plane = np.abs(np.dot(point_cloud - button_center, button_normal))
        plane_mask = distances_to_plane <= plane_depth_range
        filtered_points = point_cloud[plane_mask]
        
        # print(f"平面筛选后点云大小: {len(filtered_points)}")
        
        # IQR去噪
        if len(filtered_points) > 100:
            # 投影到buttons坐标系
            coord_axes = button_pca.components_
            centered_points = filtered_points - button_center
            projected_coords = np.dot(centered_points, coord_axes.T)
            
            # 3轴IQR去噪
            iqr_multiplier = 1.5
            valid_mask = np.ones(len(filtered_points), dtype=bool)
            for axis in range(3):
                coords = projected_coords[:, axis]
                q1, q3 = np.percentile(coords, [25, 75])
                iqr = q3 - q1
                lower_bound = q1 - iqr_multiplier * iqr
                upper_bound = q3 + iqr_multiplier * iqr
                valid_mask &= (coords >= lower_bound) & (coords <= upper_bound)
            
            denoised_points = filtered_points[valid_mask]
        else:
            denoised_points = filtered_points
        
        # print(f"去噪后点云大小: {len(denoised_points)}")
        
        # 计算鲁棒法向量 (加权PCA)
        if len(denoised_points) >= 3:
            center = np.mean(denoised_points, axis=0)
            centered_points = denoised_points - center
            
            # 加权计算
            distances = np.linalg.norm(centered_points, axis=1)
            max_dist = np.max(distances)
            if max_dist > 0:
                weights = 1.0 - (distances / max_dist) ** 2
                weights = np.maximum(weights, 0.1)
            else:
                weights = np.ones(len(denoised_points))
            
            # 加权协方差矩阵
            weighted_points = centered_points * weights[:, np.newaxis]
            cov_matrix = np.dot(weighted_points.T, centered_points) / len(denoised_points)
            eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
            z_axis = eigenvectors[:, 0]  # 最小特征值对应的特征向量作为法向量
            z_axis = z_axis / np.linalg.norm(z_axis)
            # print(f"使用点云鲁棒法向量计算: {z_axis}")
        else:
            # print("去噪后点云不足，回退到传统PCA方法")
            pca = PCA(n_components=3)
            pca.fit(buttons)
            z_axis = pca.components_[2]
            
    elif robust_normal and point_cloud is None:
        # print("警告：未提供点云数据，回退到基于按钮的鲁棒法向量计算")
        # 使用基于按钮的鲁棒法向量计算方法 (加权PCA)
        center = np.mean(buttons, axis=0)
        centered_points = buttons - center
        
        # 加权计算
        distances = np.linalg.norm(centered_points, axis=1)
        max_dist = np.max(distances)
        if max_dist > 0:
            weights = 1.0 - (distances / max_dist) ** 2
            weights = np.maximum(weights, 0.1)
        else:
            weights = np.ones(len(buttons))
        
        # 加权协方差矩阵
        weighted_points = centered_points * weights[:, np.newaxis]
        cov_matrix = np.dot(weighted_points.T, centered_points) / len(buttons)
        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
        z_axis = eigenvectors[:, 0]  # 最小特征值对应的特征向量作为法向量
        z_axis = z_axis / np.linalg.norm(z_axis)
        # print(f"使用按钮鲁棒法向量计算: {z_axis}")
    else:
        # 使用传统PCA方法
        pca = PCA(n_components=3)
        pca.fit(buttons)
        z_axis = pca.components_[2]  # 最小主成分作为法向量
        # print(f"使用传统PCA法向量计算: {z_axis}")
    
    # 3. 确保z轴的z分量为正
    if z_axis[2] < 0:
        z_axis = -z_axis
    
    # 4. 使用透视几何鲁棒地计算x轴和y轴
    # print(f"使用透视几何计算坐标轴...")
    
    # 将4个按钮点转换为numpy数组以便处理
    bl = np.array(bottom_left)   # bottom_left
    br = np.array(bottom_right)  # bottom_right  
    tl = np.array(top_left)      # top_left
    tr = np.array(top_right)     # top_right
    
    # 计算多个候选的bottom-to-top方向向量（确保x轴方向）
    candidate_x_vectors = [
        (tl - bl),                       # 左边：bottom_left -> top_left
        (tr - br),                       # 右边：bottom_right -> top_right
        ((tl + tr)/2 - (bl + br)/2),     # 中心线：bottom_center -> top_center
    ]
    
    # 计算多个候选的left-to-right方向向量（用于y轴参考）
    candidate_y_vectors = [
        (br - bl),                       # 下边：bottom_left -> bottom_right
        (tr - tl),                       # 上边：top_left -> top_right
        ((br + tr)/2 - (bl + tl)/2),     # 中心线：left_center -> right_center
    ]
    
    # print(f"候选向量分析:")
    # print(f"  bottom-to-top候选向量: {len(candidate_x_vectors)}个")
    # print(f"  left-to-right候选向量: {len(candidate_y_vectors)}个")
    
    # 将所有候选向量投影到按钮平面上（去除z轴分量）
    projected_x_vectors = []
    projected_y_vectors = []
    
    for vec in candidate_x_vectors:
        # 投影到平面：vec - (vec·z_axis)*z_axis
        projected = vec - np.dot(vec, z_axis) * z_axis
        if np.linalg.norm(projected) > 1e-6:  # 避免零向量
            projected = projected / np.linalg.norm(projected)
            projected_x_vectors.append(projected)
            # print(f"    x候选: {vec} -> 投影: {projected}")
    
    for vec in candidate_y_vectors:
        projected = vec - np.dot(vec, z_axis) * z_axis
        if np.linalg.norm(projected) > 1e-6:
            projected = projected / np.linalg.norm(projected)
            projected_y_vectors.append(projected)
            # print(f"    y候选: {vec} -> 投影: {projected}")
    
    # 计算x轴：使用bottom-to-top方向的平均值
    if len(projected_x_vectors) >= 1:
        # 确保所有向量方向一致（都指向top方向）
        reference_x = projected_x_vectors[0]
        aligned_x_vectors = []
        
        for vec in projected_x_vectors:
            # 如果与参考向量方向相反，则翻转
            if np.dot(vec, reference_x) < 0:
                vec = -vec
            aligned_x_vectors.append(vec)
        
        # 计算平均方向作为x轴
        x_axis = np.mean(aligned_x_vectors, axis=0)
        x_axis = x_axis / np.linalg.norm(x_axis)
        
        # print(f"  x轴(bottom-to-top)平均方向: {x_axis}")
    else:
        raise ButtonDetectionError("Cannot calculate valid x-axis direction")
    
    # 计算y轴：使用叉积确保右手坐标系 (y = z × x)
    y_axis = np.cross(z_axis, x_axis)
    
    # 检查y轴是否有效
    if np.linalg.norm(y_axis) < 1e-6:
        raise ButtonDetectionError("Calculated y-axis vector is invalid")
    
    y_axis = y_axis / np.linalg.norm(y_axis)
    
    # 验证y轴方向是否合理（应该大致指向left-to-right方向）
    if len(projected_y_vectors) >= 1:
        reference_y = projected_y_vectors[0]
        y_alignment = np.dot(y_axis, reference_y)
        # print(f"  y轴方向验证: 与期望方向的对齐度 = {y_alignment:.3f}")
        
        # 如果y轴方向与期望相反，翻转x轴来调整
        if y_alignment < -0.5:  # 如果严重反向
            # print("  y轴方向不符合预期，翻转x轴方向")
            x_axis = -x_axis
            y_axis = np.cross(z_axis, x_axis)
            y_axis = y_axis / np.linalg.norm(y_axis)
    
    # 最终归一化
    x_axis = x_axis / np.linalg.norm(x_axis)
    y_axis = y_axis / np.linalg.norm(y_axis)
    z_axis = z_axis / np.linalg.norm(z_axis)
    
    # 验证坐标系的数学正确性
    # print(f"坐标系验证:")
    
    # 正交性检查 (应该接近0)
    dot_xy = np.dot(x_axis, y_axis)
    dot_xz = np.dot(x_axis, z_axis)
    dot_yz = np.dot(y_axis, z_axis)
    # print(f"  正交性: x·y = {dot_xy:.6f}, x·z = {dot_xz:.6f}, y·z = {dot_yz:.6f}")
    
    # 单位向量检查 (应该接近1)
    norm_x = np.linalg.norm(x_axis)
    norm_y = np.linalg.norm(y_axis)
    norm_z = np.linalg.norm(z_axis)
    # print(f"  单位向量: |x| = {norm_x:.6f}, |y| = {norm_y:.6f}, |z| = {norm_z:.6f}")
    
    # 右手定则检查 (x × y 应该平行于 z)
    cross_xy = np.cross(x_axis, y_axis)
    right_hand_check = np.dot(cross_xy, z_axis)
    # print(f"  右手定则: (x×y)·z = {right_hand_check:.6f} (应该接近1)")
    
    # print(f"最终坐标轴方向:")
    # print(f"  x轴 (透视几何鲁棒计算): {x_axis}")
    # print(f"  y轴 (透视几何鲁棒计算): {y_axis}")
    # print(f"  z轴 (法向量, 指向相机): {z_axis}")
    
    return x_axis, y_axis, z_axis


def compute_inverse_transformation_matrix(x_axis, y_axis, z_axis, origin):
    """
    计算从物体坐标系到观察坐标系的齐次变换矩阵
    
    参数:
    x_axis, y_axis, z_axis: 物体坐标系的三个轴向量（在观察坐标系下表示）
    origin: 物体坐标系的原点（在观察坐标系下表示）
    
    返回:
    T_inv: 4x4齐次变换矩阵
    """
    # 构建旋转矩阵（物体坐标系的基向量作为列向量）
    R_inv = np.array([x_axis, y_axis, z_axis]).T
    
    # 平移向量就是原点坐标
    t_inv = np.array(origin)
    
    # 构建齐次变换矩阵
    T_inv = np.eye(4)
    T_inv[:3, :3] = R_inv
    T_inv[:3, 3] = t_inv
    
    return T_inv


def compute_obj_to_base(current_pose, T_obj_to_camera):
    """
    计算从物体坐标系到机械臂基坐标系的变换
    """
    position = current_pose[:3]
    orientation = R.from_euler('xyz', current_pose[3:], degrees=False).as_matrix()
    T_effector_to_base = np.eye(4)
    T_effector_to_base[:3, :3] = orientation
    T_effector_to_base[:3, 3] = position
    T_obj_to_base = T_effector_to_base @ T_camera_to_effector @ T_obj_to_camera
    result = decompose_transform(T_obj_to_base)
    return T_obj_to_base, result

def check_arm_orientation(target_H):
    """
    从齐次变换矩阵中提取坐标系A在坐标系B中的x、y、z轴向量
    
    参数:
    target_H: 4x4齐次变换矩阵，表示从坐标系A到坐标系B的变换
    
    返回:
    x_axis_in_B: A坐标系的x轴在B坐标系中的单位向量
    y_axis_in_B: A坐标系的y轴在B坐标系中的单位向量  
    z_axis_in_B: A坐标系的z轴在B坐标系中的单位向量
    origin_in_B: A坐标系的原点在B坐标系中的坐标
    """
    # 检查输入矩阵的形状
    if target_H.shape != (4, 4):
        raise ButtonConfigError("Transformation matrix must be a 4x4 homogeneous transformation matrix")
    
    # 提取旋转矩阵（左上角3x3部分）
    rotation_matrix = target_H[:3, :3]
    
    # 提取平移向量（右上角3x1部分）
    translation_vector = target_H[:3, 3]
    
    # 旋转矩阵的每一列就是对应的坐标轴在目标坐标系中的表示
    x_axis_in_base = rotation_matrix[:, 0]  # 第一列：x轴
    # y_axis_in_base = rotation_matrix[:, 1]  # 第二列：y轴
    # z_axis_in_base = rotation_matrix[:, 2]  # 第三列：z轴
    
    # 原点位置就是平移向量
    # origin_in_base = translation_vector
    
    return x_axis_in_base

def approach_button(robot, current_pose, current_joint, buttons, hold_distance, camera_up=True, robust_normal=False, point_cloud=None, method='movej', v=30, r=50, connect=0, block=1, target='centers', offset_x=0.0, offset_y=0.0, knob_coord=None, estimate_knob=True, button_handler=None):
    """
    机械臂接近按钮
    
    参数:
    robot: 机械臂对象
    current_pose: 当前位姿
    current_joint: 当前关节角度
    buttons: 按钮坐标列表 (至少4个点，顺序: bottom_left, bottom_right, top_left, top_right)
           注意：此函数接受相机坐标系下的坐标，内部会进行坐标转换
    hold_distance: 保持距离
    camera_up: 相机是否朝上
    robust_normal: 是否使用鲁棒法向量计算
    point_cloud: 原始点云数据 (numpy array, 用于robust_normal计算)
    method: 运动方法 ['movej', 'movel', 'movej_p'] (默认: 'movej')
    v: 速度参数 (默认: 30)
    r: 半径参数 (默认: 50)
    connect: 连接参数 (默认: 0)
    block: 阻塞参数 (默认: 1)
    target: 目标按钮 ['centers', 'left', 'right', 'knob'] (默认: 'centers')
    offset_x: X轴偏移量 (默认: 0.0)
    offset_y: Y轴偏移量 (默认: 0.0)
    knob_coord: 旋钮坐标 (3D坐标，相机坐标系，可选)
    estimate_knob: 当knob_coord为None时是否估算旋钮位置来优化视野 (默认: True)
    """
    if not hasattr(button_handler, "move_arm"):
        raise ButtonConfigError("button_handler object must implement move_arm method to ensure movement safety")
    try:
        if len(buttons) < 4:
            raise ButtonDetectionError(f"Need at least 4 button coordinates, got {len(buttons)}")

        # 验证运动方法
        valid_methods = ['movej', 'movel', 'movej_p']
        if method not in valid_methods:
            raise ButtonApproachError(f"Invalid movement method: {method}, supported: {valid_methods}")

        # 验证目标参数
        valid_targets = ['centers', 'left', 'right', 'knob']
        if target not in valid_targets:
            raise ButtonApproachError(f"Invalid target parameter: {target}, supported: {valid_targets}")

        # 如果目标是knob但没有提供knob坐标，则抛出错误
        if target == 'knob' and knob_coord is None:
            raise ButtonDetectionError("Target is knob but no knob coordinates provided")
        
        # 确保buttons是相机坐标系下的坐标（与rtBC.py中的调用保持一致）
        # print(f"接收到按钮坐标（相机坐标系）:")
        for i, button in enumerate(buttons):
            print(f"  buttons[{i}]: {button}")
        if knob_coord is not None:
            print(f"Received knob coordinates (camera coordinate system): {knob_coord}")
        
        # 建立物体坐标系 (使用相机坐标系下的坐标进行几何计算)
        # print(f"Starting object coordinate system construction using camera coordinates...")
        x_axis, y_axis, z_axis = build_object_coordinate_system(
            buttons[0], buttons[1], buttons[2], buttons[3], robust_normal, point_cloud
        )
        # print(f"Object coordinate system construction completed")
        
        # 根据target参数确定接近点（在相机坐标系下计算）
        if target == 'centers':
            # 使用4个按钮的中心，如果有旋钮则使用5个点的中心
            if knob_coord is not None:
                all_coords = buttons[0:4] + [knob_coord]*8  # 给旋钮坐标8倍权重
                approaching_point = np.mean(all_coords, axis=0)
                # print(f"目标: 12个点（4个按钮+8倍旋钮）的加权中心")
            else:
                if estimate_knob:
                    # 使用投影几何估算knob位置
                    bottom_left = np.array(buttons[0])   # bottom_left
                    bottom_right = np.array(buttons[1])  # bottom_right  
                    top_left = np.array(buttons[2])      # top_left
                    top_right = np.array(buttons[3])     # top_right
                    
                    # 方法1：利用投影几何的对角线交点
                    # 在透视投影下，原本矩形的对角线交点仍然是几何中心
                    def line_intersection(p1, p2, p3, p4):
                        """计算两条直线的交点 (p1-p2 和 p3-p4)"""
                        x1, y1, z1 = p1
                        x2, y2, z2 = p2
                        x3, y3, z3 = p3
                        x4, y4, z4 = p4
                        
                        # 将3D问题投影到最佳拟合平面上求解
                        # 使用参数方程: P = P1 + t*(P2-P1), Q = P3 + s*(P4-P3)
                        # 在最短距离处求近似交点
                        
                        v1 = p2 - p1  # 方向向量1
                        v2 = p4 - p3  # 方向向量2
                        w0 = p1 - p3  # 连接向量
                        
                        a = np.dot(v1, v1)
                        b = np.dot(v1, v2)
                        c = np.dot(v2, v2)
                        d = np.dot(v1, w0)
                        e = np.dot(v2, w0)
                        
                        denom = a * c - b * b
                        if abs(denom) < 1e-10:
                            # 平行线，返回中点
                            return (p1 + p2 + p3 + p4) / 4
                        
                        t = (b * e - c * d) / denom
                        s = (a * e - b * d) / denom
                        
                        # 两条线上最近点的中点作为交点
                        point1 = p1 + t * v1
                        point2 = p3 + s * v2
                        intersection = (point1 + point2) / 2
                        
                        return intersection
                    
                    # 计算对角线交点（透视不变的几何中心）
                    quad_center = line_intersection(bottom_left, top_right, bottom_right, top_left)
                    
                    # 方法2：计算透视校正的下边中点
                    # 利用平行四边形性质，下边在透视投影下的"真实中点"
                    bottom_center = (bottom_left + bottom_right) / 2
                    top_center = (top_left + top_right) / 2
                    
                    # 方法3：计算透视校正的垂直方向和距离
                    # 使用对边中点连线作为垂直方向参考
                    left_center = (bottom_left + top_left) / 2
                    right_center = (bottom_right + top_right) / 2
                    
                    # 垂直方向：上下边中点连线方向
                    vertical_direction = (bottom_center - top_center)
                    vertical_direction = vertical_direction / np.linalg.norm(vertical_direction)
                    
                    # 计算"垂直距离"：使用几何中心到下边的距离
                    center_to_bottom_dist = np.linalg.norm(quad_center - bottom_center)
                    
                    # 估算knob位置：从下边中点向下延伸相同距离
                    estimated_knob = bottom_center + center_to_bottom_dist * vertical_direction
                    
                    # 使用4个buttons + 估算的knob计算加权中心
                    all_coords = buttons[0:4] + [estimated_knob] * 8  # 给估算knob 6倍权重
                    approaching_point = np.mean(all_coords, axis=0)
                    
                    # print(f"目标: 4个按钮 + 投影几何估算旋钮的加权中心")
                    # print(f"四边形几何中心: {quad_center}")
                    # print(f"下边中点: {bottom_center}")
                    # print(f"垂直方向: {vertical_direction}")
                    # print(f"中心到下边距离: {center_to_bottom_dist:.4f}")
                    # print(f"估算旋钮位置: {estimated_knob}")
                else:
                    # 不估算knob，使用简单的4个按钮中心
                    approaching_point = np.mean(buttons[0:4], axis=0)
                    # print(f"目标: 4个按钮的中心")
        elif target == 'left':
            approaching_point = np.array(buttons[0])  # bottom_left
            # print(f"目标: 左下按钮 (bottom_left)")
        elif target == 'right':
            approaching_point = np.array(buttons[1])  # bottom_right
            # print(f"目标: 右下按钮 (bottom_right)")
        elif target == 'knob':
            approaching_point = np.array(knob_coord)
            # print(f"目标: 旋钮")
        
        # 计算目标位置（考虑hold_distance和offset）
        # 这里在相机坐标系下计算偏移
        hold_coord = (np.array(approaching_point) - 
                     hold_distance * z_axis - 
                     offset_x * x_axis - 
                     offset_y * y_axis)
        
        # print(f"接近点（相机坐标系）: {approaching_point}")
        # print(f"偏移量: offset_x={offset_x:.3f}, offset_y={offset_y:.3f}")
        # print(f"最终目标坐标（相机坐标系）: {hold_coord}")
        
        # 计算变换矩阵（从相机坐标系到机械臂坐标系）
        T_obj_to_camera = compute_inverse_transformation_matrix(x_axis, y_axis, z_axis, hold_coord)
        T_obj_to_base, target_pose = compute_obj_to_base(current_pose, T_obj_to_camera)

        # print(f"Target pose for approach: {target_pose}")
        # print(f"Using movement method: {method} with parameters v={v}, r={r}, connect={connect}, block={block}")
        
        # 根据运动方法执行不同的操作
        if method == 'movej':
            # 计算逆运动学
            params = rm_inverse_kinematics_params_t(current_joint, target_pose, 1)
            # print(f'Pose solution: {target_pose}')
            q_out = algo_handle.rm_algo_inverse_kinematics(params)
            
            if q_out[0] != 0:
                raise ButtonApproachError("No solution for movement trajectory")
            
            joint_solution = q_out[1]
            # print(f"Joint solution: {joint_solution}")

            if check_arm_orientation(T_obj_to_base)[2]<0 and camera_up:
                # print(f"Camera will be upside down, rotating 180 degrees")
                if np.abs(joint_solution[5] + 180 - current_joint[5]) < np.abs(joint_solution[5] - 180 - current_joint[5]):
                    joint_solution[5] = joint_solution[5] + 180
                else:
                    joint_solution[5] = joint_solution[5] - 180
                # print(f"Joint solution after rotation: {joint_solution}")
            

            
            # if target == 'centers':
            #     # 从任意位置到达centers时，先回到正向，再逐关节运动
            #     init_joints = [-3.146, 13.908, -33.719, -7.169, 25.607, -11.949]
            #     robot.rm_movej(init_joints, 10, 80, 0, 1)

            #     # 次序执行关节运动
            #     for i in [0,3,4,1,2,5]:
            #         step_pose = robot.rm_get_current_arm_state()[1]['joint']
            #         step_pose[i] = joint_solution[i]
            #         if i in [1,2]:
            #             tmp_v = int(v/3)
            #         else:
            #             tmp_v = v
            #         ret_code = robot.rm_movej(step_pose, tmp_v, r, connect, block)
            #         if ret_code != 0:
            #             raise ArmMovementError(f"joint{i} movement failed, error code: {ret_code}")
            # else:
            # robot.rm_set_arm_run_mode(0)
            # import time
            # time.sleep(1)
            # 直接执行关节空间运动
            if button_handler is not None:
                print('using wrapped move_arm to move to target_pose')
                ret_code = button_handler.move_arm('rm_movej', (joint_solution, v, r, connect, block))
                if ret_code == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during approach")
                elif ret_code != 0:
                    raise ArmMovementError(f"Joint movement failed, error code: {ret_code}")
            else:
                print('using direct robot call to move to target_pose')
                ret_code = robot.rm_movej(joint_solution, v, r, connect, block)
                if ret_code != 0:
                    raise ArmMovementError(f"Joint movement failed, error code: {ret_code}")


        elif method == 'movel':
            # 根据位姿运动，未优化关节角度
            # print("根据位姿运动，未优化关节角度")
            if button_handler is not None:
                print('using wrapped move_arm to move to target_pose')
                ret_code = button_handler.move_arm('rm_movel', (target_pose, int(v/2), r, connect, block))
                if ret_code == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during approach")
            else:
                print('using direct robot call to move to target_pose')
                ret_code = robot.rm_movel(target_pose, int(v/2), r, connect, block)

        elif method == 'movej_p':
            # 根据位姿运动，未优化关节角度
            # print("根据位姿运动，未优化关节角度")
            if button_handler is not None:
                print('using wrapped move_arm to move to target_pose')
                ret_code = button_handler.move_arm('rm_movej_p', (target_pose, v, r, connect, block))
                if ret_code == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during approach")
            else:
                print('using direct robot call to move to target_pose')
                ret_code = robot.rm_movej_p(target_pose, v, r, connect, block)

        if ret_code != 0:
            raise ArmMovementError(f"Arm movement failed, error code: {ret_code}")
            
        # print("Successfully approached button")
        
    except Exception as e:
        # print(f"Approach button failed: {e}")
        raise


def click_button(robot, click_depth, button_handler=None):
    """
    机械臂点击按钮

    参数:
    robot: 机械臂对象
    click_depth: 点击深度 (沿当前末端执行器方向移动的距离)
    button_handler: ButtonHandler实例，用于急停检查
    """
    import time
    
    # print(f"Starting button click, click depth: {click_depth*1000:.1f}mm")
    
    try:
        # 1. 获取当前位姿p0
        ret_msg = robot.rm_get_current_arm_state()
        if ret_msg[0] != 0:
            raise ArmStateError(f"Cannot get current arm state, error code: {ret_msg[0]}")
        
        current_state = ret_msg[1]
        p0 = current_state['pose']
        
        # print(f"当前位姿 p0: [{p0[0]:.4f}, {p0[1]:.4f}, {p0[2]:.4f}, {p0[3]:.4f}, {p0[4]:.4f}, {p0[5]:.4f}]")
        
        # # 2. 取消防碰撞
        # collision_result = robot.rm_set_collision_state(0)
        # if robot.rm_get_collision_stage()[0] != 0 or robot.rm_get_collision_stage()[1] != 0:
        #     print(f"Warning: Failed to disable collision detection, error code: {collision_result}")
        # else:
        #     print("Collision detection disabled")
        
        # 确保机械爪已开
        robot.rm_set_gripper_release(1000,1,10)

        # 3. 使用完整的旋转矩阵计算末端执行器的Z轴方向向量
        # 从欧拉角构建完整的旋转矩阵（使用scipy）
        rx, ry, rz = p0[3], p0[4], p0[5]  # 欧拉角 (弧度)
        
        # 使用scipy构建旋转矩阵（XYZ欧拉角顺序）
        rotation_matrix = R.from_euler('xyz', [rx, ry, rz], degrees=False).as_matrix()
        
        # 从旋转矩阵的第三列提取Z轴向量（末端执行器朝向）
        z_axis = rotation_matrix[:, 2]
        
        # print(f"完整旋转矩阵:")
        # print(f"  {rotation_matrix[0]}")
        # print(f"  {rotation_matrix[1]}")
        # print(f"  {rotation_matrix[2]}")
        # print(f"末端执行器Z轴方向: {z_axis}")
        
        # 计算移动向量（沿末端执行器Z轴方向移动click_depth距离）
        move_vector = z_axis * click_depth
        
        # 计算目标位姿（只改变位置，保持欧拉角不变）
        target_pose = [
            p0[0] + move_vector[0],
            p0[1] + move_vector[1], 
            p0[2] + move_vector[2],
            p0[3],  # 保持欧拉角不变
            p0[4],
            p0[5]
        ]
        
        # print(f"移动向量: {move_vector[0]*1000:.2f}, {move_vector[1]*1000:.2f}, {move_vector[2]*1000:.2f} mm")
        # print(f"目标位姿: {target_pose}")
        
        # 4. 沿当前方向向前移动click_depth
        # print("正在向前移动进行点击...")
        if button_handler is not None:
            print('using wrapped move_arm to move to target_pose')
            # Use button_handler.move_arm for emergency stop checking
            result = button_handler.move_arm('rm_movel', (target_pose, 20, 0, 0, 1))
            if result == 4001:  # EMERGENCY_STOP_ERROR
                raise EmergencyStopError("Emergency stop triggered during button click")
            elif result != 0:
                raise ButtonOperationError(f"Forward movement failed, error code: {result}")
        else:
            # Fallback to direct robot call
            print('using direct robot call to move to target_pose')
            result = robot.rm_movel(target_pose, 20, 0, 0, 1)
            if result != 0:
                raise ButtonOperationError(f"Forward movement failed, error code: {result}")

        # print("已移动到按钮接触位置")

        # 5. 等待1秒
        # print("保持接触1秒...")
        # time.sleep(0.3)

        # Emergency stop check during wait
        if button_handler.emergency_stopped:
            raise EmergencyStopError("Emergency stop triggered during button click")

        # 6. 移动回p0
        # print("正在返回初始位置...")
        if button_handler is not None:
            # Use button_handler.move_arm for emergency stop checking
            print('using wrapped move_arm to move to target_pose')
            result = button_handler.move_arm('rm_movel', (p0, 20, 0, 0, 1))
            if result == 4001:  # EMERGENCY_STOP_ERROR
                raise EmergencyStopError("Emergency stop triggered during button click")
            elif result != 0:
                raise ButtonOperationError(f"Failed to return to initial pose, error code: {result}")
        else:
            # Fallback to direct robot call
            print('using direct robot call to move to target_pose')
            result = robot.rm_movel(p0, 20, 0, 0, 1)
            if result != 0:
                raise ButtonOperationError(f"Failed to return to initial pose, error code: {result}")
        
        # print("✅ Button click completed, returned to initial position")
        
        # # 7. 恢复防碰撞设置
        # collision_result = robot.rm_set_collision_state(4)
        # if robot.rm_get_collision_stage()[0] != 0 or robot.rm_get_collision_stage()[1] != 4:
        #     print(f"Warning: Failed to restore collision detection, error code: {collision_result}")
        # else:
        #     print("Collision detection settings restored")
        
    except Exception as e:
        # print(f"❌ 按钮点击失败: {e}")
        # 确保在异常情况下也恢复防碰撞设置
        try:
            robot.rm_set_collision_state(4)
            print("Exception handling: Collision detection settings restored")
        except:
            pass
        raise


def turn_knob(robot, knob_angle, turn_depth, v=30, r=50, connect=0, block=1, button_handler=None):
    """
    机械臂旋转旋钮操作

    参数:
    robot: 机械臂对象
    knob_angle: 旋钮当前角度 (从检测数据获取)
    turn_depth: 向前推进距离
    button_handler: ButtonHandler实例，用于急停检查
    v, r, connect, block: 运动参数
    """
    import time
    
    # print(f"Starting knob rotation operation, current angle: {knob_angle:.1f}°, push depth: {turn_depth*1000:.1f}mm")
    
    try:
        # 1. 记住当前joints数据（'k'步骤后的状态）
        ret_msg = robot.rm_get_current_arm_state()
        if ret_msg[0] != 0:
            raise ArmStateError(f"Cannot get current arm state, error code: {ret_msg[0]}")
        
        knob_joints = ret_msg[1]['joint'].copy()  # 保存'k'步骤的joints
        # print(f"保存旋钮位置关节角度: {knob_joints}")
        
        # 2. 基于angle数据计算机械臂旋转参数
        if knob_angle < -30:
            rotation_angle_forward = -45
            # print(f"角度 {knob_angle:.1f}° < -30°，设置前向旋转角度: +45°")
        elif knob_angle > 30:
            rotation_angle_forward = 45
            # print(f"角度 {knob_angle:.1f}° > 30°，设置前向旋转角度: -45°")
        elif -10 <= knob_angle <= 10:
            rotation_angle_forward = 0
            # print(f"角度 {knob_angle:.1f}° 在 -10°~10° 范围内，设置前向旋转角度: 0°")
        else:
            rotation_angle_forward = 0
            # print(f"角度 {knob_angle:.1f}° 在其他范围，设置前向旋转角度: 0°")
        
        # 3. 执行第一次旋转
        if rotation_angle_forward != 0:
            step_pose = robot.rm_get_current_arm_state()[1]['joint'].copy()
            step_pose[5] += rotation_angle_forward
            # print(f"执行第一次旋转，关节6旋转 {rotation_angle_forward}°")

            if button_handler is not None:
                print('using wrapped move_arm to move to target_pose')
                result = button_handler.move_arm('rm_movej', (step_pose, v, r, connect, block))
                if result == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during knob rotation")
                elif result != 0:
                    raise ButtonOperationError(f"First rotation failed, error code: {result}")
            else:
                print('using direct robot call to move to target_pose')
                result = robot.rm_movej(step_pose, v, r, connect, block)
                if result != 0:
                    raise ButtonOperationError(f"First rotation failed, error code: {result}")
        else:
            print("Forward rotation angle is 0°, skipping first rotation")
        
        # 4. 获取当前位姿并向前推进
        ret_msg = robot.rm_get_current_arm_state()
        if ret_msg[0] != 0:
            raise ArmStateError(f"Cannot get current arm state, error code: {ret_msg[0]}")
        
        current_state = ret_msg[1]
        start_pose = current_state['pose'].copy()  # 记住起点
        
        # print(f"当前起点位姿: [{start_pose[0]:.4f}, {start_pose[1]:.4f}, {start_pose[2]:.4f}, {start_pose[3]:.4f}, {start_pose[4]:.4f}, {start_pose[5]:.4f}]")
        
        # 使用完整的旋转矩阵计算末端执行器的Z轴方向向量
        rx, ry, rz = start_pose[3], start_pose[4], start_pose[5]  # 欧拉角 (弧度)
        rotation_matrix = R.from_euler('xyz', [rx, ry, rz], degrees=False).as_matrix()
        z_axis = rotation_matrix[:, 2]
        
        # 确保机械爪已开
        robot.rm_set_gripper_release(1000,1,10)
        # 预先完成大部分夹角
        robot.rm_set_gripper_position(240,1,10)

        # 计算向前推进的目标位姿
        move_vector = z_axis * turn_depth
        push_pose = [
            start_pose[0] + move_vector[0],
            start_pose[1] + move_vector[1], 
            start_pose[2] + move_vector[2],
            start_pose[3],  # 保持欧拉角不变
            start_pose[4],
            start_pose[5]
        ]
        
        # print(f"向前推进 {turn_depth*1000:.1f}mm 到位姿: {push_pose}")
        if button_handler is not None:
            print('using wrapped move_arm to move to target_pose')
            result = button_handler.move_arm('rm_movel', (push_pose, 20, 0, 0, 1))
            if result == 4001:  # EMERGENCY_STOP_ERROR
                raise EmergencyStopError("Emergency stop triggered during knob rotation")
            elif result != 0:
                raise ButtonOperationError(f"Forward push failed, error code: {result}")
        else:
            print('using direct robot call to move to target_pose')
            result = robot.rm_movel(push_pose, 20, 0, 0, 1)
            if result != 0:
                raise ButtonOperationError(f"Forward push failed, error code: {result}")
        
        # 5. 取消防碰撞并夹住
        # print("取消防碰撞...")
        # collision_result = robot.rm_set_collision_state(0)
        # if robot.rm_get_collision_stage()[0] != 0 or robot.rm_get_collision_stage()[1] != 0:
        #     print(f"警告：取消防碰撞失败，错误码: {collision_result}")
        # else:
        #     print("已取消防碰撞")
        
        # print("夹住旋钮...")
        robot.rm_set_gripper_pick(500, 1000, True, 10)
        time.sleep(0.3)  # 优化：从1秒减少到0.6秒，夹爪操作已使用阻塞模式
        
        # 6. 计算回转角度并执行第二次旋转
        if -10 <= knob_angle <= 10:
            rotation_angle_back = 45
            # print(f"角度 {knob_angle:.1f}°，设置回转角度: +45°")
        elif knob_angle < -30:
            rotation_angle_back = 90
        elif knob_angle > 30:
            rotation_angle_back = -90
            # print(f"角度 {knob_angle:.1f}°，设置回转角度: -45°")
        else:
            rotation_angle_back = 0
            # print(f"角度 {knob_angle:.1f}°，设置回转角度: 0°")
        
        if rotation_angle_back != 0:
            step_pose = robot.rm_get_current_arm_state()[1]['joint'].copy()
            step_pose[5] += rotation_angle_back
            # print(f"执行第二次旋转，关节6旋转 {rotation_angle_back}°")

            if button_handler is not None:
                print('using wrapped move_arm to move to target_pose')
                result = button_handler.move_arm('rm_movej', (step_pose, v, r, connect, block))
                if result == 4001:  # EMERGENCY_STOP_ERROR
                    raise EmergencyStopError("Emergency stop triggered during knob rotation")
                elif result != 0:
                    raise ButtonOperationError(f"Second rotation failed, error code: {result}")
            else:
                print('using direct robot call to move to target_pose')
                result = robot.rm_movej(step_pose, v, r, connect, block)
                if result != 0:
                    raise ButtonOperationError(f"Second rotation failed, error code: {result}")
        else:
            print("Return rotation angle is 0°, skipping second rotation")
        
        # 7. 部分松开夹爪
        robot.rm_set_gripper_position(240,1,10)
        # print("松开夹爪...")
        
        time.sleep(0.3)  # 优化：从0.5秒减少到0.3秒，夹爪操作已使用阻塞模式
        
        # # print("返回起点位姿...")
        # result = robot.rm_movel(start_pose, 20, 0, 0, 1)
        # if result != 0:
        #     raise ButtonOperationError(f"返回起点失败，错误码: {result}")
        
        # 8. 回到'k'时的joints姿态
        # print("恢复到旋钮接近位置...")
        if button_handler is not None:
            print('using wrapped move_arm to move to target_pose')
            result = button_handler.move_arm('rm_movej', (knob_joints, v, r, connect, block))
            if result == 4001:  # EMERGENCY_STOP_ERROR
                raise EmergencyStopError("Emergency stop triggered during knob rotation")
            elif result != 0:
                raise ButtonOperationError(f"Failed to restore to knob position, error code: {result}")
        else:
            print('using direct robot call to move to target_pose')
            result = robot.rm_movej(knob_joints, v, r, connect, block)
            if result != 0:
                raise ButtonOperationError(f"Failed to restore to knob position, error code: {result}")
        
        # 完全松开夹爪
        robot.rm_set_gripper_release(1000, 1, 10)
        
        # # 恢复防碰撞设置
        # collision_result = robot.rm_set_collision_state(4)
        # if robot.rm_get_collision_stage()[0] != 0 or robot.rm_get_collision_stage()[1] != 4:
        #     print(f"警告：恢复防碰撞失败，错误码: {collision_result}")
        # else:
        #     print("已恢复防碰撞设置")
        
        # print("✅ Knob operation completed")
        
    except Exception as e:
        # print(f"❌ 旋钮操作失败: {e}")
        # 确保在异常情况下也恢复防碰撞设置和松开夹爪
        try:
            robot.rm_set_gripper_release(1000, 1, 10)
            robot.rm_set_collision_state(4)
            print("Exception handling: Gripper released and collision detection settings restored")
        except:
            pass
        raise



# def implement_button_detection_with_camera(pipeline, depth_intrin, color_intrin, arm_interface=None, current_pose=None):
def implement_button_detection_with_camera(camera_instance, depth_intrin, color_intrin, arm_interface=None, current_pose=None):
    """
    实现按钮检测，评估检测可靠性，并记录检测到的buttons和knob的基坐标系坐标到原点的距离

    Args:
        # pipeline: RealSense pipeline对象
        camera_instance: 相机实例
        depth_intrin: 深度相机内参
        color_intrin: 彩色相机内参
        arm_interface: 机械臂接口对象（用于坐标转换）
        current_pose: 当前机械臂位姿（用于坐标转换）

    Returns:
        tuple: (button_detected, detection_confidence, detected_objects)
            button_detected: bool, 是否检测到按钮
            detection_confidence: float, 检测置信度
            detected_objects: dict, 检测到的对象信息
    """
    import pyrealsense2 as rs
    from .button_detection import detect_buttons_by_status
    from .coord_ops import robust_depth_estimation

    try:
        # 获取当前帧
        # frames = pipeline.wait_for_frames()

        # # 对齐深度到彩色帧
        # align = rs.align(rs.stream.color)
        # aligned_frames = align.process(frames)

        # color_frame = aligned_frames.get_color_frame()
        # depth_frame = aligned_frames.get_depth_frame()

        # if not color_frame or not depth_frame:
        #     return False, 0.0, 0.0, {}

        # 转换为numpy数组
        # color_image = np.asanyarray(color_frame.get_data())
        # depth_image = np.asanyarray(depth_frame.get_data())
        color_image = camera_instance.color_image.copy() if camera_instance.color_image is not None else None
        depth_image = camera_instance.depth_image.copy() if camera_instance.depth_image is not None else None

        # 使用observing状态进行完整检测
        detection_result = detect_buttons_by_status(
            color_image,
            status='observing',
            verbose=False,
            display_process=False
        )

        # 解析检测结果
        if isinstance(detection_result, dict):
            # 单目标检测结果
            return False, 0.0, {}
        else:
            # 完整检测结果
            display_img, top_row, bottom_row, knob, handle_angle, mode_code = detection_result

            def process_detected_object(pixel_coords, object_name, extra_info=None):
                """
                处理检测到的对象（按钮或旋钮），计算坐标和距离

                Args:
                    pixel_coords: (x, y, radius) 像素坐标
                    object_name: 对象名称
                    extra_info: 额外信息（如旋钮角度）

                Returns:
                    dict: 处理后的对象信息，如果处理失败返回None
                """
                bx, by, br = pixel_coords

                # 使用鲁棒深度估算
                depth_val = robust_depth_estimation(depth_image, bx, by, br, status='observing')

                if depth_val is None or depth_val <= 0:
                    return None

                # 转换为相机坐标系3D坐标
                point_3d_camera = rs.rs2_deproject_pixel_to_point(
                    color_intrin, (float(bx), float(by)), depth_val / 1000.0
                )

                # 计算相机坐标系距离
                distance_camera = np.sqrt(point_3d_camera[0]**2 + point_3d_camera[1]**2 + point_3d_camera[2]**2)

                # 转换为基坐标系坐标
                from .coord_ops import camera_to_base_transform
                point_3d_base = camera_to_base_transform(point_3d_camera, current_pose, arm_interface)

                # 计算基坐标系距离
                if point_3d_base is not None:
                    distance_base = np.sqrt(point_3d_base[0]**2 + point_3d_base[1]**2 + point_3d_base[2]**2)
                    base_transform_success = True
                else:
                    # 如果基坐标系转换失败，使用相机坐标系作为备选
                    distance_base = distance_camera
                    point_3d_base = point_3d_camera
                    base_transform_success = False

                # 计算置信度
                confidence = calculate_button_confidence(bx, by, br, color_image.shape)

                # 构建对象信息
                obj_info = {
                    'pixel_coords': pixel_coords,
                    'camera_coords': point_3d_camera,
                    'base_coords': point_3d_base,
                    'distance_to_origin_camera': distance_camera,
                    'distance_to_origin_base': distance_base,
                    'distance_to_origin': distance_base,  # 优先使用基坐标系距离
                    'confidence': confidence,
                    'base_transform_success': base_transform_success
                }

                # 添加额外信息
                if extra_info:
                    obj_info.update(extra_info)

                return obj_info

            detected_objects = {}
            button_coords_camera = []  # 存储所有按钮的相机坐标系3D坐标
            button_coords_base = []    # 存储所有按钮的基坐标系3D坐标
            button_confidences = []    # 存储所有按钮的置信度

            # 处理检测到的按钮
            if top_row and len(top_row) >= 2:
                for i, button in enumerate(top_row):
                    obj_info = process_detected_object(button, f'top_button_{i}')
                    if obj_info is not None:
                        detected_objects[f'top_button_{i}'] = obj_info
                        button_coords_camera.append(obj_info['camera_coords'])
                        button_coords_base.append(obj_info['base_coords'])
                        button_confidences.append(obj_info['confidence'])

            if bottom_row and len(bottom_row) >= 2:
                for i, button in enumerate(bottom_row):
                    obj_info = process_detected_object(button, f'bottom_button_{i}')
                    if obj_info is not None:
                        detected_objects[f'bottom_button_{i}'] = obj_info
                        button_coords_camera.append(obj_info['camera_coords'])
                        button_coords_base.append(obj_info['base_coords'])
                        button_confidences.append(obj_info['confidence'])

            # 检查是否至少检测到4个按钮
            if len(button_coords_base) < 4:
                return False, 0.0, {}

            # 处理检测到的旋钮（可选）
            knob_coords_camera = []
            knob_coords_base = []
            knob_confidences = []
            if knob:
                obj_info = process_detected_object(knob, 'knob', {'handle_angle': handle_angle})
                if obj_info is not None:
                    detected_objects['knob'] = obj_info
                    knob_coords_camera.append(obj_info['camera_coords'])
                    knob_coords_base.append(obj_info['base_coords'])
                    knob_confidences.append(obj_info['confidence'])

            # 计算所有检测对象的坐标均值和置信度
            all_coords_camera = button_coords_camera + knob_coords_camera
            all_coords_base = button_coords_base + knob_coords_base
            all_confidences = button_confidences + knob_confidences

            # 计算相机坐标系坐标均值
            mean_coords_camera = np.mean(all_coords_camera, axis=0)
            mean_distance_camera = np.sqrt(mean_coords_camera[0]**2 + mean_coords_camera[1]**2 + mean_coords_camera[2]**2)

            # 计算基坐标系坐标均值（优先使用）
            mean_coords_base = np.mean(all_coords_base, axis=0)
            mean_distance_base = np.sqrt(mean_coords_base[0]**2 + mean_coords_base[1]**2 + mean_coords_base[2]**2)

            # 计算平均置信度
            avg_confidence = np.mean(all_confidences)

            # 检查基坐标系转换成功率
            base_transform_success_count = sum(1 for obj in detected_objects.values() if obj.get('base_transform_success', False))
            total_objects = len(detected_objects)
            base_transform_success_rate = base_transform_success_count / total_objects if total_objects > 0 else 0

            # 构建详细的结果信息
            result_summary = {
                'total_objects': total_objects,
                'buttons_count': len(button_coords_base),
                'knob_count': len(knob_coords_base),
                'base_transform_success_rate': base_transform_success_rate,
                'mean_coords_camera': mean_coords_camera.tolist(),
                'mean_coords_base': mean_coords_base.tolist(),
                'mean_distance_camera': mean_distance_camera,
                'mean_distance_base': mean_distance_base,
                'using_base_coordinates': True  # 标识优先使用基坐标系
            }

            # 将结果摘要添加到检测对象中
            detected_objects['_summary'] = result_summary

            return True, avg_confidence, detected_objects

    except Exception as e:
        print(f"Error during button detection: {e}")
        return False, 0.0, {}


def calculate_button_confidence(bx, by, br, image_shape):
    """
    计算按钮检测的置信度

    Args:
        bx, by, br: 按钮的像素坐标和半径
        image_shape: 图像形状 (height, width, channels)

    Returns:
        float: 置信度分数 (0-1)
    """
    height, width = image_shape[:2]

    # 位置得分：距离图像中心越近得分越高
    center_x = width // 2
    center_y = height // 2
    max_dist = np.sqrt(center_x**2 + center_y**2)
    actual_dist = np.sqrt((bx - center_x)**2 + (by - center_y)**2)
    position_score = max(0, 1 - actual_dist / max_dist)

    # 大小得分：半径在合理范围内得分越高
    size_score = 1.0 if 8 <= br <= 40 else 0.5

    # 边界得分：确保按钮不在图像边缘
    margin = br * 2
    boundary_score = 1.0
    if bx < margin or bx > width - margin or by < margin or by > height - margin:
        boundary_score = 0.7

    # 综合置信度
    confidence = (position_score * 0.5 + size_score * 0.3 + boundary_score * 0.2)
    return confidence


def find_best_view(button_handler, start_pose, search_range, smallest_working_distance, largest_working_distance):
    robot = button_handler.arm
    camera_instance = button_handler.camera_instance
    depth_intrin = camera_instance.depth_intrin
    color_intrin = camera_instance.color_intrin

    # 环绕360度，找出能检测到按钮的位姿
    detection_views = []
    detection_confidences = []
    detection_distances_base = []
    detection_distances_camera = []
    detection_objects_list = []

    angle_list = np.arange(float(search_range[0]), float(search_range[1]) + 1e-6, 10.0)
    for idx, angle in enumerate(angle_list):
        i = idx + 1
        # 检查急停状态
        if button_handler.emergency_stopped:
            print(f"检测位姿 {i}/{len(angle_list)}: 急停触发，中止搜索")
            raise EmergencyStopError("Emergency stop triggered during target search")

        overlook_step = start_pose.copy()
        overlook_step[3] = angle
        result = None
        try:
            # 优化：适度提高搜索速度，从v=10提升到v=15
            if button_handler is not None:
                print('using wrapped move_arm to move to overlook_step')
                result = button_handler.move_arm('rm_movej', (overlook_step, 30, 80, 0, 1))
            else:
                print('using direct robot call to move to overlook_step')
                result = robot.rm_movej(overlook_step, 30, 80, 0, 1)
            if result == 4001:
                raise EmergencyStopError("Emergency stop triggered during overlook step move")
            elif result != 0:
                raise ArmMovementError(f"Detection pose {i}/{len(angle_list)} failed, error code: {result}")
        except Exception as e:
            print(f"检测位姿 {i}/{len(angle_list)} 时发生错误: {e}")
            # 如果是急停相关错误，立即退出循环
            if "Emergency stop" in str(e):
                raise
            continue
        print(f"检测位姿 {i}/{len(angle_list)}: 角度 {angle}°")

        # 再次检查急停状态（在移动后）
        if button_handler.emergency_stopped:
            print(f"检测位姿 {i}/{len(angle_list)}: 移动后检测到急停，中止搜索")
            raise EmergencyStopError("Emergency stop triggered during target search")

        # 等待机械臂稳定 - 优化：使用智能等待机制
        import time
        if not button_handler.smart_wait_for_arm_stable(max_wait_time=0.5):
            if button_handler.emergency_stopped:
                print(f"检测位姿 {i}/{len(angle_list)}: 等待期间检测到急停，中止搜索")
                raise EmergencyStopError("Emergency stop triggered during target search")
            # 如果智能等待超时，使用短暂的固定等待作为备用
            time.sleep(0.1)

        # 获取当前机械臂位姿用于坐标转换
        try:
            ret_msg = robot.rm_get_current_arm_state()
            if ret_msg[0] == 0:
                current_pose = ret_msg[1]['pose']
                current_joint = ret_msg[1]['joint']
            else:
                current_pose = None
                current_joint = None
        except:
            current_pose = None
            current_joint = None

        button_detected, detection_confidence, detected_objects = \
            implement_button_detection_with_camera(camera_instance, depth_intrin, color_intrin, robot, current_pose)

        if button_detected:
            # 打印当前joint数据
            if current_joint is not None:
                print(f"  检测到目标，当前joint: {current_joint}")
            # 简化的距离检查提示
            mean_distance_base = detected_objects.get('_summary', {}).get('mean_distance_base', 0.0)
            mean_distance_camera = detected_objects.get('_summary', {}).get('mean_distance_camera', 0.0)
            base_distance_ok = smallest_working_distance < mean_distance_base < largest_working_distance
            camera_distance_ok = smallest_working_distance < mean_distance_camera < largest_working_distance
            base_distance_status = "OK" if base_distance_ok else "WARN"
            camera_distance_status = "OK" if camera_distance_ok else "WARN"

            # 获取相机坐标系距离（如果有）
            summary = detected_objects.get('_summary', {}) if isinstance(detected_objects, dict) else {}
            mean_distance_camera = summary.get('mean_distance_camera', None)
            camera_distance_str = ""
            if mean_distance_camera is not None:
                camera_distance_ok = smallest_working_distance < mean_distance_camera < largest_working_distance
                camera_distance_status = "OK" if camera_distance_ok else "WARN"
                camera_distance_str = f", 相机坐标系距离: {mean_distance_camera:.3f}m ({camera_distance_status})"

            detection_views.append(overlook_step)
            detection_confidences.append(detection_confidence)
            detection_distances_base.append(mean_distance_base)
            detection_distances_camera.append(mean_distance_camera)
            detection_objects_list.append(detected_objects)
            print(f"  检测到目标，置信度: {detection_confidence:.3f}, 基坐标系距离: {mean_distance_base:.3f}m ({base_distance_status}){camera_distance_str}")
        else:
            print(f"  未检测到目标")

    if detection_views:
        # 选择置信度最高的位姿
        best_index = np.argmax(detection_confidences)
        best_view = detection_views[best_index]
        best_confidence = detection_confidences[best_index]
        best_distance_base = detection_distances_base[best_index]
        best_distance_camera = detection_distances_camera[best_index]
        best_objects = detection_objects_list[best_index]

        print(f"搜索成功，选择最佳位姿，置信度: {best_confidence:.3f}")
        result = None
        optimized_view = copy.deepcopy(best_view)
        if optimized_view[3] > 60:
            optimized_view[0]+=20
            optimized_view[3]-=25
        else:
            optimized_view[0]-=20
            optimized_view[3]+=25
        try:
            if button_handler is not None:
                print('using wrapped move_arm to move to best_view')
                result = button_handler.move_arm('rm_movej', (optimized_view, 20, 80, 0, 1))
            else:
                print('using direct robot call to move to best_view')
                result = robot.rm_movej(optimized_view, 20, 80, 0, 1)
            if result == 4001:
                raise EmergencyStopError("Emergency stop triggered during best joint move")
            elif result != 0:
                raise ArmMovementError(f"Failed to move to best joint, error code: {result}")
        except Exception as e:
            print(f"移动到最佳位姿时发生错误: {e}")
            raise
        
        time.sleep(1)

        # 获取当前机械臂位姿用于坐标转换
        try:
            ret_msg = robot.rm_get_current_arm_state()
            if ret_msg[0] == 0:
                current_pose = ret_msg[1]['pose']
            else:
                current_pose = None
        except:
            print('debug | cannot retrieve current_pose after moving to best pose')
            current_pose = None

        max_attempts = 5
        button_detected = False
        for attempt in range(max_attempts):
            button_detected, detection_confidence, detected_objects = \
                implement_button_detection_with_camera(camera_instance, depth_intrin, color_intrin, robot, current_pose)
            if button_detected:
                break
            time.sleep(0.1)  # Short delay between attempts

        
        if button_detected:
            # 扩展距离检查：检查目标到基坐标系和相机坐标系原点的距离
            summary = detected_objects.get('_summary', {})
            mean_distance_base = summary.get('mean_distance_base', 0.0)
            mean_distance_camera = summary.get('mean_distance_camera', 0.0)
            exact_best_joint = robot.rm_get_current_arm_state()[1]['joint']
            print('更新精确最优位姿：', exact_best_joint)

            # 检查基坐标系和相机坐标系距离是否都在工作范围内
            base_distance_ok = smallest_working_distance < mean_distance_base < largest_working_distance
            camera_distance_ok = smallest_working_distance < mean_distance_camera < largest_working_distance

            print(f"重新检测成功！置信度: {detection_confidence:.3f}")
            print(f"  基坐标系距离: {mean_distance_base:.3f}m (范围: {smallest_working_distance:.3f}-{largest_working_distance:.3f}m) [{'OK' if base_distance_ok else 'WARN'}]")
            print(f"  相机坐标系距离: {mean_distance_camera:.3f}m (范围: {smallest_working_distance:.3f}-{largest_working_distance:.3f}m) [{'OK' if camera_distance_ok else 'WARN'}]")

            if not (base_distance_ok and camera_distance_ok):
                # print("警告：距离不在工作范围内，但继续执行。")
                raise ButtonApproachError(f"beyond working distance {smallest_working_distance}m-{largest_working_distance}m")

            return True, {
                "detection_confidence": detection_confidence,
                "mean_distance_base": mean_distance_base,
                "mean_distance_camera": mean_distance_camera,
                "detected_objects": detected_objects,
                "best_joint": exact_best_joint,
                "search_results": {
                    "total_positions_tested": len(angle_list),
                    "positions_with_detection": len(detection_views),
                    "all_confidences": detection_confidences,
                    "all_distances_base": detection_distances_base,
                    "all_distances_camera": detection_distances_camera
                }
            }
        else:
            print('重新检测失败，使用搜索时记录的最佳位姿')
            return True, {
                "detection_confidence": best_confidence,
                "mean_distance_base": best_distance_base,
                "mean_distance_camera": best_distance_camera,
                "detected_objects": best_objects,
                "best_view": best_view,
                "search_results": {
                    "total_positions_tested": len(angle_list),
                    "positions_with_detection": len(detection_views),
                    "all_confidences": detection_confidences,
                    "all_distances_base": detection_distances_base,
                    "all_distances_camera": detection_distances_camera
                }
            }
    else:
        return False, {
            "error_message": "No buttons detected in search range",
            "search_results": {
                "total_positions_tested": len(angle_list),
                "positions_with_detection": len(detection_views),
                "all_confidences": detection_confidences,
                "all_distances_base": detection_distances_base,
                "all_distances_camera": detection_distances_camera
            }
        }


def search_targets(button_handler, smallest_working_distance=0.4, largest_working_distance=1.0, known_orientation=None, joint5_angle=-101.048, **kwargs):
    """
    搜索目标按钮和旋钮，通过机械臂旋转找到最佳检测位姿

    Args:
        robot: 机械臂对象
        smallest_working_distance: 最小工作距离（米）
        largest_working_distance: 最大工作距离（米）
        known_orientation: 若为None则自动搜索，否则直接转到该位姿
        joint5_angle: 第5关节角度（度）
        **kwargs: 其他参数，可包含pipeline, depth_intrin, color_intrin, button_handler

    Returns:
        tuple: (success, result_dict)
            success: bool, 是否找到目标
            result_dict: dict, 包含检测结果信息
    """
    if not hasattr(button_handler, "move_arm"):
        raise ButtonConfigError("button_handler object must implement move_arm method to ensure movement safety")

    robot = button_handler.arm
    camera_instance = button_handler.camera_instance
    depth_intrin = camera_instance.depth_intrin
    color_intrin = camera_instance.color_intrin

    print('回到初始位姿')
    # 回到初始位姿
    init_joints = [-3.146, 13.908, -33.719, -7.169, 25.607, -11.949]
    result = None
    try:
        if button_handler is not None:
            print('using wrapped move_arm to move to initial pose')
            result = button_handler.move_arm('rm_movej', (init_joints, 20, 80, 0, 1))
        else:
            print('using direct robot call to move to initial pose')
            result = robot.rm_movej(init_joints, 20, 80, 0, 1)
        if result == 4001:
            raise EmergencyStopError("Emergency stop triggered during initial pose move")
        elif result != 0:
            raise ArmMovementError(f"Failed to return to initial pose, error code: {result}")
    except Exception as e:
        print(f"回到初始位姿时发生错误: {e}")
        raise
    print("已回到初始位姿")

    # 如果known_orientation为有效的6维float数组，则直接转到该位姿并测量
    if known_orientation is not None:
        if (isinstance(known_orientation, (list, tuple, np.ndarray)) and len(known_orientation) == 6 and
            all(isinstance(x, (float, int, np.floating, np.integer)) for x in known_orientation)):
            print(f"直接转到指定位姿: {known_orientation}")
            optimized_orientation = copy.deepcopy(known_orientation)
            if optimized_orientation[3] > 60:
                optimized_orientation[0]+=20
                optimized_orientation[3]-=25
            else:
                optimized_orientation[0]-=20
                optimized_orientation[3]+=25
            if button_handler is not None:
                print('using wrapped move_arm to move to known_orientation')
                result = button_handler.move_arm('rm_movej', (list(optimized_orientation), 20, 80, 0, 1))
            else:
                print('using direct robot call to move to known_orientation')
                result = robot.rm_movej(list(optimized_orientation), 20, 80, 0, 1)
            if result == 4001:
                raise EmergencyStopError("Emergency stop triggered during known_orientation move")
            elif result != 0:
                raise ArmMovementError(f"Failed to move to known_orientation, error code: {result}")
            import time
            time.sleep(1)
            # 获取当前机械臂位姿用于坐标转换
            try:
                ret_msg = robot.rm_get_current_arm_state()
                if ret_msg[0] == 0:
                    current_pose = ret_msg[1]['pose']
                else:
                    current_pose = list(optimized_orientation)
            except:
                current_pose = list(optimized_orientation)
            max_attempts = 5
            for attempt in range(max_attempts):
                button_detected, detection_confidence, detected_objects = \
                    implement_button_detection_with_camera(camera_instance, depth_intrin, color_intrin, robot, current_pose)
                if button_detected:
                    summary = detected_objects.get('_summary', {})
                    mean_distance_base = summary.get('mean_distance_base', 0.0)
                    mean_distance_camera = summary.get('mean_distance_camera', 0.0)
                    base_distance_ok = smallest_working_distance < mean_distance_base < largest_working_distance
                    camera_distance_ok = smallest_working_distance < mean_distance_camera < largest_working_distance
                    print(f"测量成功！置信度: {detection_confidence:.3f}")
                    print(f"  基坐标系距离: {mean_distance_base:.3f}m (范围: {smallest_working_distance:.3f}-{largest_working_distance:.3f}m) [{'OK' if base_distance_ok else 'WARN'}]")
                    print(f"  相机坐标系距离: {mean_distance_camera:.3f}m (范围: {smallest_working_distance:.3f}-{largest_working_distance:.3f}m) [{'OK' if camera_distance_ok else 'WARN'}]")
                    if not (base_distance_ok and camera_distance_ok):
                        # print("警告：距离不在工作范围内，但继续执行。")
                        raise ButtonApproachError(f"beyond working distance {smallest_working_distance}m-{largest_working_distance}m")
                    return True, {
                        "detection_confidence": detection_confidence,
                        "mean_distance_base": mean_distance_base,
                        "mean_distance_camera": mean_distance_camera,
                        "detected_objects": detected_objects,
                        "best_pose": list(optimized_orientation),
                        "search_results": {
                            "total_positions_tested": 1,
                            "positions_with_detection": 1 if button_detected else 0,
                            "all_confidences": [detection_confidence],
                            "all_distances_base": [mean_distance_base],
                            "all_distances_camera": [mean_distance_camera]
                        }
                    }
            else:
                error_msg = "No buttons detected at provided pose"
                print(f"{error_msg}")
                print('预设位姿检测失败，小范围快速搜索')
                quick_search_result = find_best_view(button_handler, list(known_orientation), (known_orientation[3]-20, known_orientation[3]+20), smallest_working_distance, largest_working_distance)
                if quick_search_result[0]:
                    return quick_search_result
                else:
                    print('小范围搜索失败，进行大范围完整搜索')
    else:
        print('未指定有效观察位姿，进行大范围完整搜索')

    overlook_start = [3.184000015258789, 48.915000915527344, -45.81100082397461, -175.0, joint5_angle, -0.494]
    try:
        if button_handler is not None:
            print('using wrapped move_arm to move to overlook_start')
            result = button_handler.move_arm('rm_movej', (overlook_start, 20, 80, 0, 1))
        else:
            print('using direct robot call to move to overlook_start')
            result = robot.rm_movej(overlook_start, 20, 80, 0, 1)
        if result == 4001:
            raise EmergencyStopError("Emergency stop triggered during overlook start move")
        elif result != 0:
            raise ArmMovementError(f"Failed to adjust to initial observation pose, error code: {result}")
    except Exception as e:
        print(f"调整到初始观察位姿时发生错误: {e}")
        raise
    print("已调整到初始观察位姿")
    complete_search_result = find_best_view(button_handler, overlook_start, (-175, 85), smallest_working_distance, largest_working_distance)
    if complete_search_result[0]:
        return complete_search_result
    else:
        error_msg = "No target detected in all test poses"
        print(f"{error_msg}")
        raise ButtonDetectionError(error_msg)    


def restore_pose(robot):
    """
    恢复机械臂到最近一次approach操作前的位置
    """
    from buttonControl.button_action import get_observation_checkpoint  # 确保即使本文件内也能用
    try:
        joint_angles = get_observation_checkpoint()
        if joint_angles is None:
            raise ButtonStateError("observation_checkpoint not set, cannot restore pose")
        if len(joint_angles) != 6:
            raise ButtonConfigError(f"Joint angle array length error, expected 6, got {len(joint_angles)}")
        print(f"Restoring from observation_checkpoint: {joint_angles}")
        ret_code = robot.rm_movej(joint_angles, 10, 100, 0, 1)
        if ret_code != 0:
            raise ArmMovementError(f"Arm movement failed, error code: {ret_code}")
        return True
    except Exception as e:
        raise


    





