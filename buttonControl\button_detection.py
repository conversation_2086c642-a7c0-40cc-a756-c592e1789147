import cv2
import numpy as np
import socket
import math

# import matplotlib.pyplot as plt
# import matplotlib

# matplotlib.use("TkAgg")
import os
from .debug_saver import debug_saver


def calibrate_circle_with_mask(circle, color_mask, gray_image, avg_r, display_process=False):
    x, y, r = circle
    roi_size = int(r * 1.25)  # 扩大搜索区域
    roi_mask = np.zeros_like(color_mask)
    cv2.circle(roi_mask, (x, y), roi_size, 255, -1)
    roi_mask = cv2.bitwise_and(roi_mask, color_mask)
    contours, _ = cv2.findContours(roi_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if contours:
        # 找到最大的轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        if cv2.contourArea(largest_contour) > 10:
            (cx_contour, cy_contour), radius = cv2.minEnclosingCircle(largest_contour)
            calibrated_r = max(int(avg_r * 0.75), min(int(avg_r * 1.3), radius))

            calibrated_x = cx_contour
            calibrated_y = cy_contour
            return (int(calibrated_x), int(calibrated_y), int(calibrated_r))

    return circle


def detect_colored_buttons_from_masks(red_mask, green_mask, gray_image, hsv, display_process=False):
    """
    Detect buttons directly from red and green masks using contours
    
    Args:
        red_mask: Red color mask
        green_mask: Green color mask
        gray_image: Grayscale image
        display_process: Whether to display processing steps
        
    Returns:
        red_buttons: List of detected red buttons [(x, y, r), ...]
        green_buttons: List of detected green buttons [(x, y, r), ...]
    """
    red_buttons = []
    green_buttons = []

    # Detect red buttons
    red_contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in red_contours:
        area = cv2.contourArea(contour)
        if area < 50:  # Filter out small contours
            continue

        (x, y), radius = cv2.minEnclosingCircle(contour)
        x, y, radius = int(x), int(y), int(radius)

        # Check how well the contour matches a circle
        circle_mask = np.zeros_like(red_mask)
        cv2.circle(circle_mask, np.int_([x, y]), int(radius), 255, -1)
        contour_mask = np.zeros_like(red_mask)
        cv2.drawContours(contour_mask, [contour], -1, (255), thickness=cv2.FILLED)
        intersection = cv2.countNonZero(cv2.bitwise_and(contour_mask, circle_mask))

        if intersection / cv2.countNonZero(circle_mask) > 0.35:
            circle_mask = np.zeros_like(red_mask)
            cv2.circle(circle_mask, np.int_([x, y]), int(radius * 1.5), 255, -1)
            x0 = max(0, x - radius * 3)
            y0 = max(0, y - radius * 3)
            x1 = min(circle_mask.shape[1], x + radius * 3)
            y1 = min(circle_mask.shape[0], y + radius * 3)
            cm1 = circle_mask[y0:y1, x0:x1]
            hsv1 = hsv[y0:y1, x0:x1, 1]
            hsv_pts = hsv1[cm1 == 0]
            if np.mean(hsv_pts) < 40:
                # Filter by radius range
                if 5 <= radius <= 48:
                    red_buttons.append((x, y, radius))
                    if display_process:
                        cv2.circle(gray_image, (x, y), radius, (100, 100, 100), 2)

    # Detect green buttons
    green_contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in green_contours:
        area = cv2.contourArea(contour)
        if area < 50:  # Filter out small contours
            continue

        (x, y), radius = cv2.minEnclosingCircle(contour)
        x, y, radius = int(x), int(y), int(radius)

        # Check how well the contour matches a circle
        circle_mask = np.zeros_like(red_mask)
        cv2.circle(circle_mask, np.int_([x, y]), int(radius), 255, -1)
        contour_mask = np.zeros_like(red_mask)
        cv2.drawContours(contour_mask, [contour], -1, (255), thickness=cv2.FILLED)
        intersection = cv2.countNonZero(cv2.bitwise_and(contour_mask, circle_mask))

        if intersection / cv2.countNonZero(circle_mask) > 0.35:
            circle_mask = np.zeros_like(red_mask)
            cv2.circle(circle_mask, np.int_([x, y]), int(radius * 1.5), 255, -1)
            x0 = max(0, x - radius * 3)
            y0 = max(0, y - radius * 3)
            x1 = min(circle_mask.shape[1], x + radius * 3)
            y1 = min(circle_mask.shape[0], y + radius * 3)
            cm1 = circle_mask[y0:y1, x0:x1]
            hsv1 = hsv[y0:y1, x0:x1, 1]
            hsv_pts = hsv1[cm1 == 0]
            if np.mean(hsv_pts) < 40:
                # Filter by radius range
                if 5 <= radius <= 48:
                    green_buttons.append((x, y, radius))
                    if display_process:
                        cv2.circle(gray_image, (x, y), radius, (100, 100, 100), 2)

    return red_buttons, green_buttons


def calculate_edge_angle(p1, p2):
    """
    Calculate angle between line p1->p2 and horizontal axis
    Angle in degrees, from positive x-axis, clockwise is positive, counterclockwise is negative
    Angle range is [-180, 180]

    Args:
        p1: Start point coordinate (x, y)
        p2: End point coordinate (x, y)

    Returns:
        Angle (degrees)
    """
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]
    if abs(dx) < 1e-6:
        if dy > 0:
            return 90
        else:
            return -90
    angle_rad = math.atan2(dy, dx)
    angle_deg = math.degrees(angle_rad)

    return angle_deg


def detect_buttons(image, verbose=False, display_process=True):
    """
    Detect round buttons and knob handle in the image.
    
    Args:
        image: RGB image from the camera
        verbose: Whether to print additional information
        display_process: Whether to display processing steps
        
    Returns:
        Processed image with detections and analysis results
    """
    # Start debug session if enabled
    debug_saver.start_session()
    
    # Preprocess the image for better color stability
    image = preprocess_image(image)
    
    # Save original and preprocessed images for debugging
    debug_saver.save_intermediate(image, "01_original_image")
    
    display_img = image.copy()

    # Convert to HSV for better color filtering
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    debug_saver.save_intermediate(hsv, "02_hsv_image")

    # Expand color ranges and add more tolerance
    lower_red1 = np.array([0, 50, 20])  # Reduced saturation and value thresholds
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([160, 50, 20])
    upper_red2 = np.array([180, 255, 255])

    lower_green = np.array([25, 50, 20])  # Expanded green range
    upper_green = np.array([95, 255, 255])

    # Create masks for each color with more robust detection
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)

    green_mask = cv2.inRange(hsv, lower_green, upper_green)
    
    # Save color masks for debugging
    debug_saver.save_mask(red_mask1, "03_red_mask1")
    debug_saver.save_mask(red_mask2, "03_red_mask2")
    debug_saver.save_mask(red_mask, "04_red_mask_combined")
    debug_saver.save_mask(green_mask, "04_green_mask")

    # Morphological operations to reduce noise
    kernel = np.ones((3,3), np.uint8)
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    green_mask = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)
    
    # Save processed masks after morphological operations
    debug_saver.save_mask(red_mask, "05_red_mask_processed")
    debug_saver.save_mask(green_mask, "05_green_mask_processed")

    # Adaptive color detection function
    def adaptive_color_detection(button, red_mask, green_mask):
        x, y, r = button
        circle_mask = np.zeros_like(gray)
        cv2.circle(circle_mask, (x, y), r, 255, -1)
        
        red_pixels = cv2.countNonZero(cv2.bitwise_and(red_mask, circle_mask))
        green_pixels = cv2.countNonZero(cv2.bitwise_and(green_mask, circle_mask))
        
        total_area = np.pi * r * r
        red_ratio = red_pixels / total_area
        green_ratio = green_pixels / total_area
        
        # More flexible color classification
        color_threshold = 0.15  # Reduced threshold for color classification
        if red_ratio > color_threshold and green_ratio <= color_threshold:
            return 'red'
        elif green_ratio > color_threshold and red_ratio <= color_threshold:
            return 'green'
        else:
            return 'others'

    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Use contour detection to directly detect red and green buttons
    red_buttons, green_buttons = detect_colored_buttons_from_masks(red_mask, green_mask, gray, hsv, display_process)

    avg_rs = [button[2] for button in red_buttons + green_buttons]

    bottom_left, bottom_right, knob, handle_angle, mode_code = None, None, None, None, None
    other_buttons = []  # For the knob
    top_row, bottom_row = None, None

    # Process when we have at least 2 red or green buttons
    if len(red_buttons) >= 2 or len(green_buttons) >= 2:
        # If we have both red and green buttons
        if len(red_buttons) >= 1 and len(green_buttons) >= 1:
            red_buttons.sort(key=lambda b: b[1])
            green_buttons.sort(key=lambda b: b[1])
            upper_buttons = [red_buttons[0], green_buttons[0]]
            lower_buttons = [red_buttons[-1], green_buttons[-1]]
            for rb in red_buttons[1:-1]:
                upper_buttons.append(rb)
                lower_buttons.append(rb)
            for gb in green_buttons[1:-1]:
                upper_buttons.append(gb)
                lower_buttons.append(gb)

            # Sort all buttons by y-coordinate
            all_buttons = red_buttons + green_buttons
            all_buttons.sort(key=lambda b: b[1])

            # Sort upper and lower buttons by x-coordinate
            upper_buttons.sort(key=lambda b: b[0])
            lower_buttons.sort(key=lambda b: b[0])

            # Function to calculate parallelogram quality score (lower is better)
            def calculate_parallelogram_score(ul0, ur0, ll0, lr0):
                if ul0 == ll0 or ur0 == lr0 or ul0 == lr0 or ur0 == ll0:
                    return float('inf')

                # Calculate quadrilateral edge lengths
                ul, ur, ll, lr = np.float_([ul0, ur0, ll0, lr0])
                top_edge = math.sqrt((ur[0] - ul[0]) ** 2 + (ur[1] - ul[1]) ** 2)
                bottom_edge = math.sqrt((lr[0] - ll[0]) ** 2 + (lr[1] - ll[1]) ** 2)
                left_edge = math.sqrt((ll[0] - ul[0]) ** 2 + (ll[1] - ul[1]) ** 2)
                right_edge = math.sqrt((lr[0] - ur[0]) ** 2 + (lr[1] - ur[1]) ** 2)

                # Edge similarity (parallel sides should be equal)
                edge_similarity = max(0, abs(top_edge - bottom_edge) + abs(left_edge - right_edge) - 6)

                # Vertex radius similarity
                vertex_similarity = max(0, np.var([ul0[-1], ur0[-1], ll0[-1], lr0[-1]]) - 8)

                # Parallelism
                angle_ul_ur = calculate_edge_angle(ul, ur)  # Top edge
                angle_ll_lr = calculate_edge_angle(ll, lr)  # Bottom edge
                angle_ul_ll = calculate_edge_angle(ul, ll)  # Left edge
                angle_ur_lr = calculate_edge_angle(ur, lr)  # Right edge

                # Calculate absolute difference between opposite edge angles
                diff_horizontal = abs(angle_ul_ur - angle_ll_lr)
                diff_vertical = abs(angle_ul_ll - angle_ur_lr)

                # Handle near-360 degree angle differences
                if diff_horizontal > 180:
                    diff_horizontal = abs(360 - diff_horizontal)
                if diff_vertical > 180:
                    diff_vertical = abs(360 - diff_vertical)

                # Calculate parallelism score (smaller is more parallel)
                diagonal_similarity = max(diff_horizontal + diff_vertical - 2, 0)

                # Color pattern check - ensure diagonal vertices have same color
                color_pattern_score = 0
                if ul0 in red_buttons and lr0 in red_buttons and ur0 in green_buttons and ll0 in green_buttons:
                    color_pattern_score = 0  # Perfect match
                elif ul0 in green_buttons and lr0 in green_buttons and ur0 in red_buttons and ll0 in red_buttons:
                    color_pattern_score = 35  # Reversed match
                else:
                    color_pattern_score = 100  # No match, apply large penalty

                # Combined score (lower is better)
                return edge_similarity, diagonal_similarity, color_pattern_score, vertex_similarity

            # Find the best quadrilateral arrangement
            best_quadrilateral = None
            best_score = float('inf')

            # If we have many buttons, consider all possible combinations
            if len(upper_buttons) > 2 or len(lower_buttons) > 2:
                for ul in upper_buttons:
                    for ur in upper_buttons:
                        if ul == ur:
                            continue
                        if ul[0] >= ur[0]:  # Ensure ul is to the left of ur
                            continue

                        for ll in lower_buttons:
                            for lr in lower_buttons:
                                if ll == lr or ul == ll or ul == lr or ur == ll or ur == lr:
                                    continue
                                if ll[0] >= lr[0]:  # Ensure ll is to the left of lr
                                    continue

                                # Check if diagonal vertices have the same color
                                ul_is_red = ul in red_buttons
                                ur_is_red = ur in red_buttons
                                ll_is_red = ll in red_buttons
                                lr_is_red = lr in red_buttons

                                if (ul_is_red == lr_is_red) and (ur_is_red == ll_is_red) and (ul_is_red != ur_is_red):
                                    score = calculate_parallelogram_score(ul, ur, ll, lr)

                                    if best_score == float('inf') or np.sum(score) < np.sum(best_score):
                                        best_score = score
                                        best_quadrilateral = (ul, ur, ll, lr)
            else:
                # If we have few buttons, use default arrangement
                for ub in upper_buttons:
                    if ub in red_buttons:
                        upper_left = ub
                        break
                for ub in upper_buttons[::-1]:
                    if ub not in red_buttons:
                        upper_right = ub
                        break
                for lb in lower_buttons:
                    if lb not in red_buttons:
                        lower_left = lb
                        break
                for lb in lower_buttons[::-1]:
                    if lb in red_buttons:
                        lower_right = lb
                        break
                score = calculate_parallelogram_score(upper_left, upper_right, lower_left, lower_right)
                best_quadrilateral = (upper_left, upper_right, lower_left, lower_right)
                best_score = score

            if verbose:
                # print('best_score:', best_score, np.sum(best_score))
                # print('best_quadrilateral:', best_quadrilateral)
                pass

            if best_quadrilateral and best_score != float('inf') and np.sum(best_score) < 75:
                ul, ur, ll, lr = best_quadrilateral
                # Clear previous button lists
                red_buttons = []
                green_buttons = []

                # Reassign buttons by color
                for button in [ul, ur, ll, lr]:
                    if button in all_buttons:
                        # color = adaptive_color_detection(button, red_mask, green_mask)
                        # if color == 'red':
                        if button in red_buttons or any(b[0] == button[0] and b[1] == button[1] for b in red_buttons):
                            red_buttons.append(button)
                        # elif color == 'green':
                        elif button in green_buttons or any(
                                b[0] == button[0] and b[1] == button[1] for b in green_buttons):
                            green_buttons.append(button)

                # Ensure we have 2 red and 2 green buttons
                if len(red_buttons) != 2 or len(green_buttons) != 2:
                    # Reassign by color mask
                    red_buttons = []
                    green_buttons = []
                    for button in [ul, ur, ll, lr]:
                        circle_mask = np.zeros_like(gray)
                        cv2.circle(circle_mask, (button[0], button[1]), button[2], 255, -1)
                        red_pixels = cv2.countNonZero(cv2.bitwise_and(red_mask, circle_mask))
                        green_pixels = cv2.countNonZero(cv2.bitwise_and(green_mask, circle_mask))
                        total_area = np.pi * button[2] * button[2]
                        red_ratio = red_pixels / total_area
                        green_ratio = green_pixels / total_area

                        if red_ratio > green_ratio:
                            red_buttons.append(button)
                        else:
                            green_buttons.append(button)

                if verbose:
                    # print(f"Selected 4 buttons: Red={len(red_buttons)}, Green={len(green_buttons)}")
                    pass
            else:
                display_img = image.copy()
                red_buttons, green_buttons = [], []
    if len(red_buttons) == 2 and len(green_buttons) == 2:
        other_buttons = []
        p1, p2 = 150, 30
        extend_p1 = 0
        extend_p2 = 0
        avg_r = np.mean(avg_rs)
        try_c = 0

        # Find knob with Hough circles
        while len(other_buttons) >= 15 or len(other_buttons) <= 4:
            try_c += 1
            if try_c > 12:
                break

            circles = cv2.HoughCircles(
                gray,
                cv2.HOUGH_GRADIENT,
                dp=1,
                minDist=int(avg_r * 1.3),
                param1=p1 + extend_p1,
                param2=p2 + extend_p2,
                minRadius=int(avg_r * 0.6),
                maxRadius=int(avg_r * 1.8)
            )

            if circles is None:
                continue

            if len(circles[0]) > 20:
                extend_p1 += 20
                extend_p2 += 2
            else:
                extend_p1 -= 5
                extend_p2 -= 1

            other_buttons = circles[0]
        other_buttons = []
        if circles is not None:
            circles = np.uint16(np.around(circles))

            for circle in circles[0, :]:
                x, y, r = circle
                circle_mask = np.zeros_like(gray)
                cv2.circle(circle_mask, (x, y), r, 255, -1)
                red_pixels = cv2.countNonZero(cv2.bitwise_and(red_mask, circle_mask))
                green_pixels = cv2.countNonZero(cv2.bitwise_and(green_mask, circle_mask))
                total_area = np.pi * r * r
                red_ratio = red_pixels / total_area
                green_ratio = green_pixels / total_area
                # print('x,y,r',x,y,r,'red', red_ratio,'green', green_ratio)
                if red_ratio > 0.2 or green_ratio > 0.2:
                    # Skip colored buttons
                    pass
                else:
                    other_buttons.append((x, y, r))

        all_colored_buttons = red_buttons + green_buttons
        all_colored_buttons.sort(key=lambda b: b[1])  # Sort by y-coordinate

        if len(all_colored_buttons) >= 4:
            # Group buttons into rows based on y-coordinate
            rows = []
            current_row = [all_colored_buttons[0], all_colored_buttons[1]]
            current_row.sort(key=lambda b: b[0])  # Sort by x-coordinate
            rows.append(current_row)
            current_row = [all_colored_buttons[2], all_colored_buttons[3]]
            current_row.sort(key=lambda b: b[0])  # Sort by x-coordinate
            rows.append(current_row)

            # Check if we have at least 2 rows with at least 2 buttons each
            if len(rows) >= 2 and len(rows[0]) >= 2 and len(rows[1]) >= 2:
                # Get the first two rows
                top_row = rows[0][:2]  # First two buttons in the first row
                bottom_row = rows[1][:2]  # First two buttons in the second row

                # Check the colors in the top row (should be red, green from left to right)
                top_colors = []
                for button in top_row:
                    if button in red_buttons:
                        top_colors.append("red")
                    elif button in green_buttons:
                        top_colors.append("green")

                # Check the colors in the bottom row (should be green, red from left to right)
                bottom_colors = []
                for button in bottom_row:
                    if button in red_buttons:
                        bottom_colors.append("red")
                    elif button in green_buttons:
                        bottom_colors.append("green")

                if verbose:
                    # Display the colors for debugging
                    # print(f"Top row: {top_colors}")
                    # print(f"Bottom row: {bottom_colors}")
                    pass

                # Check if the color pattern is valid
                if not ((top_colors == ['red', 'green'] and bottom_colors == ['green', 'red']) or
                        (top_colors == ['green', 'red'] and bottom_colors == ['red', 'green'])):
                    # Invalid color pattern, reset buttons
                    top_row = [None, None]
                    bottom_row = [None, None]

                if len(bottom_row) >= 2:
                    bottom_left = bottom_row[0]
                    bottom_right = bottom_row[1]

                    if display_process:
                        display_img = image.copy()  # Reset display image
                        # Draw red buttons
                        for x, y, r in red_buttons:
                            cv2.circle(display_img, (x, y), r, (0, 0, 255), 3)  # Red
                        # Draw green buttons
                        for x, y, r in green_buttons:
                            cv2.circle(display_img, (x, y), r, (0, 255, 0), 3)  # Green
                        # Draw additional markers
                        cv2.circle(display_img, bottom_left[:2], 5, (0, 255, 255), -1)
                        cv2.circle(display_img, bottom_right[:2], 5, (0, 255, 255), -1)

                    if verbose:
                        # Print the coordinates
                        # print(f"Bottom left button center: {bottom_left}")
                        # print(f"Bottom right button center: {bottom_right}")
                        pass
                avg_r = np.mean(avg_rs)
                if other_buttons:
                    other_buttons.sort(key=lambda b: b[1], reverse=True)
                    # Calculate the midpoint between the left and right colored buttons
                    top_mid_x = (top_row[0][0] + top_row[1][0]) / 2
                    bottom_mid_x = (bottom_row[0][0] + bottom_row[1][0]) / 2
                    mid_x = 2 * bottom_mid_x - top_mid_x
                    top_mid_y = (top_row[0][1] + top_row[1][1]) / 2
                    bottom_mid_y = (bottom_row[0][1] + bottom_row[1][1]) / 2
                    mid_y = 2 * bottom_mid_y - top_mid_y

                    # Find the knob closest to the predicted position
                    other_buttons = [o for o in other_buttons if o[2] >= int(avg_r * 0.6) and o[2] <= int(avg_r * 1.8)]
                    other_buttons.sort(key=lambda b: abs(b[0] - mid_x) + abs(b[1] - mid_y))
                    knob = other_buttons[0] if len(other_buttons) > 0 else None

                    if knob is not None:
                        if verbose:
                            # print(f"knob_distance", abs(knob[0] - mid_x) + abs(knob[1] - mid_y))
                            pass
                        if abs(knob[0] - mid_x) + abs(knob[1] - mid_y) > 60:
                            knob = None

                    if knob is not None:
                        # Refine the knob detection using black mask
                        black_mask = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)[1]
                        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
                        black_mask = cv2.erode(black_mask, kernel, iterations=1)
                        black_mask = cv2.dilate(black_mask, kernel, iterations=1)
                        new_knob = calibrate_circle_with_mask(knob, black_mask, gray, avg_r,
                                                                         display_process)

                        knob_x, knob_y, knob_r = new_knob
                        if verbose:
                            # print(f"-knob center: ({knob_x}, {knob_y}, {knob_r})")
                            pass

                        # Now detect the handle angle
                        button_mask, button_edge_mask = np.zeros_like(gray), np.zeros_like(gray)

                        # Calculate vertical base angle from buttons arrangement
                        vertical_base_angle = math.degrees(
                            math.atan2(-(bottom_mid_x - top_mid_x), (bottom_mid_y - top_mid_y)))
                        vertical_base_angle = vertical_base_angle if vertical_base_angle <= 90 else vertical_base_angle - 180

                        if verbose:
                            # print(f"Vertical base angle: {vertical_base_angle:.2f}°")
                            pass

                        if display_process:
                            # Draw the knob with a different colorknob_x, knob_y, knob_r = new_knob
                            cv2.circle(display_img, (knob_x, knob_y), knob_r, (255, 191, 0), 3)
                            cv2.circle(display_img, (knob_x, knob_y), 2, (0, 255, 255), 3)
                        display_img_base = display_img.copy()

                        cv2.circle(button_mask, (knob_x, knob_y), (knob_r+2), 255, -1)
                        button_roi0 = gray.copy()
                        button_roi0[button_mask == 0] = 255
                        button_roi0 = 255 - button_roi0
                        inner_try = 0
                        p1_i, p2_i = 100, 25
                        while inner_try < 4:
                            inner_try += 1
                            circles = cv2.HoughCircles(
                                button_roi0,
                                cv2.HOUGH_GRADIENT,
                                dp=1,
                                minDist=2,
                                param1=p1_i,
                                param2=p2_i,
                                minRadius=5,
                                maxRadius=knob_r - 1
                            )
                            if circles is None:
                                p2_i -= 2
                            elif len(circles[0]) > 1:
                                p2_i += 2
                            elif len(circles[0]) == 1:
                                break
                        if circles is not None:
                            ccircles = circles.reshape(-1, 3)
                            ccircles = np.uint16(np.around(ccircles))
                            knob_x, knob_y, knob_r = ccircles[0]
                            pass


                        if verbose:
                            # print(f"knob center: ({knob_x}, {knob_y}, {knob_r})")
                            pass

                        # color_image_show = image.copy()
                        # ccircles = circles.reshape(-1, 3)
                        # ccircles = np.uint16(np.around(ccircles))
                        # for i in ccircles:
                        #     cv2.circle(color_image_show, (i[0], i[1]), i[2], (0, 0, 255), 1)  #


                        button_mask, button_edge_mask = np.zeros_like(gray), np.zeros_like(gray)
                        cv2.circle(button_mask, (knob_x, knob_y), (knob_r), 255, -1)
                        button_roi = cv2.bitwise_and(gray, gray, mask=button_mask)

                        thresh2 = cv2.threshold(button_roi, 125, 255, type=cv2.THRESH_BINARY)# + cv2.THRESH_OTSU)
                        contours, _ = cv2.findContours(thresh2[1], cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
                        handle_angle = detect_handle_angle(contours, knob_x, knob_y, knob_r,
                                                           display_img, verbose)
                        if handle_angle is not None:
                            # Draw the handle angle
                            angle_rad = math.radians(handle_angle)
                            handle_angle -= vertical_base_angle
                            if handle_angle >= 65 or handle_angle <= -65:
                                display_img = display_img_base
                                handle_angle = None
                        # if handle_angle is None:
                        #     r_c = knob_r
                        #     while r_c >= knob_r * 0.2:
                        #         button_edge_mask1 = np.zeros_like(gray)
                        #         cv2.circle(button_edge_mask1, (knob_x, knob_y), r_c, 255, 1)
                        #         intersect_mask = cv2.bitwise_and(thresh2[1], button_edge_mask1)
                        #         inter_count = cv2.countNonZero(intersect_mask)
                        #         edge_count = cv2.countNonZero(button_edge_mask1)
                        #         inter_ratio = inter_count / edge_count
                        #         # if verbose:
                        #         #     print(knob_r, r_c, inter_ratio)
                        #         if inter_ratio < 0.2:
                        #             r_c += 3
                        #             break
                        #         r_c -= 1
                        #         # plt.figure()
                        #         # plt.title(r_c)
                        #         # plt.imshow(intersect_mask)
                        #     handle_angle = None
                        #     rc_list = [r_c, r_c + 1, r_c - 1, r_c + 2, r_c - 2]
                        #     # try_handle_count = 0
                        #     # try_handle_max = 6
                        #     # while handle_angle is None:
                        #     #     try_handle_count += 1
                        #     #     if try_handle_count > try_handle_max:
                        #     #         break
                        #     for try_handle_count in range(len(rc_list)):
                        #         thickness=2
                        #         # for thickness in [2, 1, 3]:
                        #         cv2.circle(button_edge_mask, (knob_x, knob_y),
                        #                    max(1, rc_list[try_handle_count]), 255, thickness)
                        #         thresh_2 = cv2.bitwise_and(thresh2[1], cv2.bitwise_not(button_edge_mask))
                        #         contours, _ = cv2.findContours(thresh_2, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
                        #         handle_angle = detect_handle_angle(contours, knob_x, knob_y, knob_r,
                        #                                            display_img, verbose)
                        #         if handle_angle is not None:
                        #             # Draw the handle angle
                        #             angle_rad = math.radians(handle_angle)
                        #             handle_angle -= vertical_base_angle
                        #             if handle_angle >= 65 or handle_angle <= -65:
                        #                 display_img = display_img_base
                        #                 handle_angle = None
                        #         if handle_angle is not None:
                        #             if verbose:
                        #                 print('thickness:', thickness, "rc:", try_handle_count)
                        #             break
                        # # Create masks for handle detection
                        # cv2.circle(button_mask, (knob_x, knob_y), knob_r, 255, -1)
                        #
                        # Adjust edge thickness based on button size
                        if knob_r >= 16:
                            cv2.circle(button_edge_mask, (knob_x, knob_y), knob_r, 255, 6)
                        elif knob_r >= 10:
                            cv2.circle(button_edge_mask, (knob_x, knob_y), knob_r, 255, 4)
                        elif knob_r >= 8:
                            cv2.circle(button_edge_mask, (knob_x, knob_y), knob_r, 255, 3)
                        elif knob_r >= 5:
                            cv2.circle(button_edge_mask, (knob_x, knob_y), knob_r, 255, 2)

                        # Apply masks to the grayscale image
                        button_roi = cv2.bitwise_and(gray, gray, mask=button_mask)
                        thresh2 = cv2.threshold(button_roi, 125, 255, cv2.THRESH_BINARY)
                        thresh_2 = cv2.bitwise_and(thresh2[1], cv2.bitwise_not(button_edge_mask))
                        contours, _ = cv2.findContours(thresh_2, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
                        display_img_base = display_img.copy()
                        handle_angle = detect_handle_angle(contours, knob_x, knob_y, knob_r, display_img,
                                                           verbose)
                        if handle_angle is not None:
                            # Draw the handle angle
                            angle_rad = math.radians(handle_angle)
                            handle_angle -= vertical_base_angle
                            if handle_angle >= 65 or handle_angle <= -65:
                                display_img = display_img_base
                                handle_angle = None
                        #
                        if handle_angle is None:
                            thresh2 = cv2.threshold(button_roi, 125, 255, type=cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                            thresh_2 = cv2.bitwise_and(thresh2[1], cv2.bitwise_not(button_edge_mask))
                            contours, _ = cv2.findContours(thresh_2, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
                            handle_angle = detect_handle_angle(contours, knob_x, knob_y, knob_r,
                                                               display_img, verbose)
                            if handle_angle is not None:
                                # Draw the handle angle
                                angle_rad = math.radians(handle_angle)
                                handle_angle -= vertical_base_angle
                                if handle_angle >= 65 or handle_angle <= -65:
                                    display_img = display_img_base
                                    handle_angle = None

                        if handle_angle is None:
                            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
                            # Erode and dilate to enhance features
                            eroded_image = cv2.dilate(thresh_2, kernel, iterations=1)
                            thresh_2 = cv2.erode(eroded_image, kernel, iterations=1)
                            contours, _ = cv2.findContours(thresh_2, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
                            handle_angle = detect_handle_angle(contours, knob_x, knob_y, knob_r,
                                                               display_img, verbose)
                            if handle_angle is not None:
                                # Draw the handle angle
                                angle_rad = math.radians(handle_angle)
                                handle_angle -= vertical_base_angle
                                if handle_angle >= 65 or handle_angle <= -65:
                                    display_img = display_img_base
                                    handle_angle = None

                        if handle_angle is not None:
                            # # Calculate handle angle relative to vertical base
                            # angle_rad = math.radians(handle_angle)
                            # handle_angle -= vertical_base_angle
                            #
                            # # Validate handle angle
                            # if handle_angle >= 65 or handle_angle <= -65:
                            #     display_img = display_img_base
                            #     handle_angle = None
                            # else:
                            if True:
                                knob_x, knob_y, knob_r = new_knob
                                # Draw the handle direction line
                                end_x = int(knob_x + knob_r * math.sin(angle_rad))
                                end_y = int(knob_y - knob_r * math.cos(angle_rad))

                                if verbose:
                                    # print(f"Handle angle: {handle_angle:.2f}°")
                                    pass

                                if display_process:
                                    # Add line and text for the angle
                                    cv2.line(display_img, (knob_x, knob_y), (end_x, end_y), (0, 255, 255), 2)
                                    cv2.putText(display_img, f"{handle_angle:.1f}`",
                                                (knob_x + 15, knob_y),
                                                cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)

    else:
        pass
        # if display_process:
        #     for x, y, r in other_buttons:
        #         # Draw gray circles for other buttons (potential knob)
        #         cv2.circle(display_img, (x, y), r, (128, 128, 128), 3)
        #         cv2.circle(display_img, (x, y), 2, (128, 128, 128), 3)

    # Validate handle angle range
    if handle_angle is not None and (handle_angle >= 65 or handle_angle <= -65):
        handle_angle = None

    # Determine mode code based on handle angle
    if handle_angle is not None:
        if handle_angle > 30:
            mode_code = 1  # Manual mode
        elif handle_angle < -30:
            mode_code = -1  # Auto mode
        else:
            mode_code = 0  # Stop mode
    else:
        mode_code = None

    # Save final detection results and end debug session
    debug_saver.save_processed(display_img, "99_final_result")
    debug_saver.save_data({
        "top_row": top_row,
        "bottom_row": bottom_row, 
        "knob": knob,
        "handle_angle": handle_angle,
        "mode_code": mode_code
    }, "detection_results")
    debug_saver.end_session()
    
    return display_img, top_row, bottom_row, knob, handle_angle, mode_code


def detect_handle_angle(contours, center_x, center_y, knob_r, image, verbose=False):
    """
    Detect the angle of the handle on the knob.

    Args:
        contours: Contours detected in the button ROI
        center_x, center_y: Coordinates of the button center
        knob_r: Radius of the knob
        image: Image to draw on for visualization
        verbose: Whether to print additional information

    Returns:
        The angle of the handle in degrees (0° = vertical, positive = clockwise)
    """
    if not contours:
        return None

    # Find elongated contours that could be the handle
    handle_candidates = []

    for contour in contours:
        # Skip tiny contours
        if cv2.contourArea(contour) < max(5, knob_r * knob_r * 0.08):
            continue

        # Fit an ellipse to the contour
        if len(contour) >= 5:  # Need at least 5 points to fit an ellipse
            try:
                # ellipse = cv2.fitEllipse(contour)
                ellipse = cv2.minAreaRect(contour)
                (x, y), (width, height), angle = ellipse

                # Check if the ellipse is elongated (handle-like)
                aspect_ratio = max(width, height) / min(width, height)

                # Check if the ellipse is near the center of the button
                dist_to_center = np.sqrt((x - center_x) ** 2 + (y - center_y) ** 2)

                box = cv2.boxPoints(ellipse)
                box = np.int0(box)
                box_mask = np.zeros((image.shape[:2]), dtype=np.uint8)
                cv2.drawContours(box_mask, [box], -1, (255), thickness=cv2.FILLED)
                box_pixels = cv2.countNonZero(box_mask)
                contour_mask = np.zeros((image.shape[:2]), dtype=np.uint8)
                cv2.drawContours(contour_mask, [contour], -1, (255), thickness=cv2.FILLED)
                contour_pixels = cv2.countNonZero(np.bitwise_and(box_mask, contour_mask))
                intersection_ratio = contour_pixels / box_pixels
                circularity = (4 * np.pi * cv2.contourArea(contour)) / (cv2.arcLength(contour, True)) ** 2 if cv2.arcLength(contour, True) > 0 else 1
                if aspect_ratio > 1.25 and dist_to_center <= knob_r * 1.25 and intersection_ratio >= 0.5 and circularity > 0.3:
                # if aspect_ratio > 2.0 and dist_to_center < 30:
                    handle_candidates.append((ellipse, aspect_ratio, cv2.contourArea(contour), circularity))
            except:
                pass

    if not handle_candidates:
        return None

    # Sort by aspect ratio and area (more elongated and larger first)
    handle_candidates.sort(key=lambda x: x[1] * 2 + x[2], reverse=True)

    # Get the angle of the best candidate
    best_ellipse = handle_candidates[0][0]

    if verbose:
        # print('*ellipse area:', handle_candidates[0][2], handle_candidates[0][2] / (knob_r * knob_r), 'circularity:', handle_candidates[0][3])
        pass

    (x, y), (width, height), angle = best_ellipse
    cv2.ellipse(image, best_ellipse, (0, 165, 255), 1)

    # Adjust the angle based on ellipse orientation
    adjusted_angle = angle  # OpenCV's ellipse angle is measured from the horizontal

    # Normalize to -90 to +90 range
    if adjusted_angle > 90:
        adjusted_angle -= 180

    # If width > height, the angle needs to be adjusted by 90 degrees
    if width > height:
        adjusted_angle = (angle + 90) % 180

        # Normalize to -90 to +90 range again
        if adjusted_angle > 90:
            adjusted_angle -= 180

    return adjusted_angle


if __name__ == "__main__":
    pass


def crop_image_for_target(image, target_type, crop_params=None):
    """
    根据目标类型裁剪图像，只保留目标区域
    
    Args:
        image: 输入图像 (BGR格式)
        target_type: 目标类型 ('left_button', 'right_button', 'knob_left', 'knob_right', 'knob_center')
        crop_params: 自定义裁剪参数，如果为None则使用默认参数
        
    Returns:
        cropped_image: 裁剪后的图像
        crop_info: 裁剪信息字典，包含offset等用于坐标转换的信息
    """
    height, width = image.shape[:2]
    image_copy = image.copy()
    
    # 创建白色填充
    white = np.array([255, 255, 255], dtype=np.uint8)
    
    # 默认裁剪参数
    default_params = {
        'left_button': {
            'mid_y_ratio': 0.7,  # height // 2
            'mid_x_ratio': 0.5,  # width // 2
            'keep_region': 'bottom_right'  # 保留右下角区域
        },
        'right_button': {
            'mid_y_ratio': 0.6,  # height // 5 * 3
            'mid_x_ratio': 0.33, # width // 3
            'keep_region': 'bottom_right'  # 保留右下角区域
        },
        'knob_left': {
            'mid_y_ratio': 0.7,  # height // 2
            'mid_x_ratio': 0.5,  # width // 2
            'keep_region': 'bottom_right'  # 保留右下角区域（修改为与left_button一致）
        },
        'knob_right': {
            'mid_y_ratio': 0.7,  # height // 2
            'mid_x_ratio': 0.5,  # width // 2
            'keep_region': 'bottom_right'  # 保留右下角区域（修改为与left_button一致）
        },
        'knob_center': {
            'mid_y_ratio': 0.7,  # height // 2
            'mid_x_ratio': 0.5,  # width // 2
            'keep_region': 'bottom_right'  # 保留右下角区域（修改为与left_button一致）
        }
    }
    
    # 使用自定义参数或默认参数
    if crop_params is None:
        if target_type not in default_params:
            print(f"Warning: Unknown target_type '{target_type}', using full image")
            return image_copy, {'offset_x': 0, 'offset_y': 0, 'scale_x': 1.0, 'scale_y': 1.0}
        params = default_params[target_type]
    else:
        params = crop_params
    
    # 计算分割点
    mid_y = int(height * params['mid_y_ratio'])
    mid_x = int(width * params['mid_x_ratio'])
    
    # 根据保留区域设置白色遮罩
    if params['keep_region'] == 'bottom_right':
        # 保留右下角，遮盖其他区域
        image_copy[:mid_y, :] = white          # 上半部分
        image_copy[mid_y:, :mid_x] = white     # 下半部分的左侧
        crop_info = {'offset_x': mid_x, 'offset_y': mid_y, 'scale_x': 1.0, 'scale_y': 1.0}
    elif params['keep_region'] == 'bottom_center':
        # 保留下方中心区域，遮盖其他区域
        image_copy[:mid_y, :] = white          # 上半部分
        quarter_x = width // 4
        image_copy[mid_y:, :quarter_x] = white # 下半部分的左侧
        image_copy[mid_y:, width-quarter_x:] = white # 下半部分的右侧
        crop_info = {'offset_x': quarter_x, 'offset_y': mid_y, 'scale_x': 1.0, 'scale_y': 1.0}
    else:
        print(f"Warning: Unknown keep_region '{params['keep_region']}', using full image")
        crop_info = {'offset_x': 0, 'offset_y': 0, 'scale_x': 1.0, 'scale_y': 1.0}
    
    return image_copy, crop_info


def detect_single_target(image, target_type, crop_params=None, verbose=False, display_process=True):
    """
    检测单个目标（按钮或旋钮）
    
    Args:
        image: RGB图像
        target_type: 目标类型 ('left_button', 'right_button', 'knob_left', 'knob_right', 'knob_center')
        crop_params: 自定义裁剪参数
        verbose: 是否打印详细信息
        display_process: 是否显示处理过程
        
    Returns:
        target_result: 检测到的目标信息
        confidence_score: 置信度分数
        display_img: 显示图像
        crop_info: 裁剪信息
    """
    # Preprocess the image to improve contrast
    image = preprocess_image(image)

    # 裁剪图像
    cropped_image, crop_info = crop_image_for_target(image, target_type, crop_params)
    
    if verbose:
        # print(f"Detecting single target: {target_type}")
        # print(f"Crop info: {crop_info}")
        pass
    
    # Convert to HSV for better color filtering (使用裁剪后的图像)
    hsv = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2HSV)

    # Expand color ranges and add more tolerance
    lower_red1 = np.array([0, 50, 20])  # Reduced saturation and value thresholds
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([160, 50, 20])
    upper_red2 = np.array([180, 255, 255])

    lower_green = np.array([25, 50, 20])  # Expanded green range
    upper_green = np.array([95, 255, 255])

    # Create masks for each color with more robust detection (基于裁剪图像)
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)

    green_mask = cv2.inRange(hsv, lower_green, upper_green)

    # Morphological operations to reduce noise
    kernel = np.ones((3,3), np.uint8)
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    green_mask = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)

    
    # 转换为灰度图
    gray = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2GRAY)
    
    target_result = None
    confidence_score = 0.0
    display_img = cropped_image.copy()
    
    if target_type in ['left_button', 'right_button']:
        # 检测彩色按钮
        red_buttons, green_buttons = detect_colored_buttons_from_masks(
            red_mask, green_mask, gray, hsv, display_process=False
        )
        
        all_buttons = red_buttons + green_buttons
        
        # 筛选半径 >= 30 的按钮
        filtered_buttons = [btn for btn in all_buttons if btn[2] >= 28 and btn[2] <= 40]
        
        if filtered_buttons:
            # 根据目标类型选择最佳按钮
            if target_type == 'left_button':
                # 选择最左下角的按钮
                sorted_buttons = sorted(filtered_buttons, key=lambda b: (-b[1], b[0]))
            else:  # right_button
                # 选择最右下角的按钮
                sorted_buttons = sorted(filtered_buttons, key=lambda b: (-b[1], -b[0]))
            
            target_result = sorted_buttons[0]
            
            # 计算置信度 (基于按钮大小和位置合理性)
            x, y, r = target_result
            
            # 位置得分：距离裁剪区域中心越近得分越高
            # 计算实际保留区域（ROI）的中心，而不是整个图像的中心
            roi_width = cropped_image.shape[1] - crop_info['offset_x']
            roi_height = cropped_image.shape[0] - crop_info['offset_y']
            center_x = crop_info['offset_x'] + roi_width // 2
            center_y = crop_info['offset_y'] + roi_height // 2
            max_dist = np.sqrt(roi_width**2 + roi_height**2)
            actual_dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
            position_score = max(0, 1 - actual_dist / max_dist)
            
            # 大小得分：半径在合理范围内得分越高
            size_score = 1.0 if 28 <= r <= 40 else 0.5
            
            confidence_score = (position_score * 0.7 + size_score * 0.3)
            
            # 坐标已经是原图坐标（因为没有真正裁剪图像，只是遮罩）
            # 不需要坐标转换
            target_result = (x, y, r)
            
            if display_process:
                # 绘制检测结果
                color = (0, 0, 255) if target_result in red_buttons else (0, 255, 0)
                cv2.circle(display_img, (x, y), r, color, 3)
                cv2.circle(display_img, (x, y), 3, (255, 191, 0), -1)
                cv2.putText(display_img, f'{target_type}', (x-30, y-r-10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 191, 0), 2)
                cv2.putText(display_img, f'conf:{confidence_score:.2f}', (x-30, y+r+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 191, 0), 1)
        
        if verbose:
            # print(f"Detected {len(all_buttons)} buttons in cropped region")
            # if target_result:
            #     print(f"Selected target: {target_result}, confidence: {confidence_score:.3f}")
            pass
    
    elif target_type in ['knob_left', 'knob_right', 'knob_center']:
        # 检测旋钮 (使用HoughCircles检测灰色/黑色圆形)
        # 首先排除彩色区域
        combined_color_mask = cv2.bitwise_or(red_mask, green_mask)
        gray_filtered = gray.copy()
        gray_filtered[combined_color_mask > 0] = 255  # 将彩色区域设为白色
        
        # 使用HoughCircles检测圆形
        circles = cv2.HoughCircles(
            gray_filtered,
            cv2.HOUGH_GRADIENT,
            dp=1,
            minDist=30,
            param1=100,
            param2=30,
            minRadius=8,
            maxRadius=50
        )
        
        if circles is not None:
            circles = np.uint16(np.around(circles))
            circle_candidates = []
            
            for circle in circles[0, :]:
                x, y, r = circle
                
                # 检查是否为非彩色圆形
                circle_mask = np.zeros_like(gray)
                cv2.circle(circle_mask, (x, y), r, 255, -1)
                red_pixels = cv2.countNonZero(cv2.bitwise_and(red_mask, circle_mask))
                green_pixels = cv2.countNonZero(cv2.bitwise_and(green_mask, circle_mask))
                total_area = np.pi * r * r
                
                red_ratio = red_pixels / total_area
                green_ratio = green_pixels / total_area
                
                if red_ratio < 0.2 and green_ratio < 0.2:  # 非彩色
                    # 计算置信度
                    # 计算实际保留区域（ROI）的中心，而不是整个图像的中心
                    roi_width = cropped_image.shape[1] - crop_info['offset_x']
                    roi_height = cropped_image.shape[0] - crop_info['offset_y']
                    center_x = crop_info['offset_x'] + roi_width // 2
                    center_y = crop_info['offset_y'] + roi_height // 2
                    max_dist = np.sqrt(roi_width**2 + roi_height**2)
                    actual_dist = np.sqrt((x - center_x)**2 + (y - center_y)**2)
                    position_score = max(0, 1 - actual_dist / max_dist)
                    size_score = 1.0 if 10 <= r <= 35 else 0.5
                    candidate_confidence = position_score * 0.8 + size_score * 0.2
                    
                    circle_candidates.append((x, y, r, candidate_confidence))
            
            if circle_candidates:
                # 选择置信度最高的圆形作为旋钮
                circle_candidates.sort(key=lambda c: c[3], reverse=True)
                x, y, r, confidence_score = circle_candidates[0]
                
                # 坐标已经是原图坐标（因为没有真正裁剪图像，只是遮罩）
                # 不需要坐标转换
                target_result = (x, y, r)
                
                if display_process:
                    cv2.circle(display_img, (x, y), r, (128, 128, 128), 3)
                    cv2.circle(display_img, (x, y), 3, (255, 191, 0), -1)
                    cv2.putText(display_img, target_type, (x-20, y-r-10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 191, 0), 2)
                    cv2.putText(display_img, f'conf:{confidence_score:.2f}', (x-30, y+r+20), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 191, 0), 1)
        
        if verbose:
            # print(f"Detected {len(circles[0]) if circles is not None else 0} circles in cropped region")
            # if target_result:
            #     print(f"Selected knob: {target_result}, confidence: {confidence_score:.3f}")
            pass
    
    # Save final detection results and end debug session for detect_buttons_by_status
    debug_saver.save_processed(display_img, "99_final_result_by_status")
    debug_saver.save_data({
        "target_result": target_result,
        "confidence_score": confidence_score,
        "crop_info": crop_info
    }, "detection_results_by_status")
    debug_saver.end_session()
    
    return target_result, confidence_score, display_img, crop_info


def detect_buttons_by_status(image, status='uncertain', verbose=False, display_process=True, **kwargs):
    """
    根据不同的status检测按钮/旋钮
    
    Args:
        image: RGB图像 (BGR格式从OpenCV)
        status: 当前机器人状态 ('uncertain', 'observing', 'touching_left_button', 'touching_right_button', 'touching_knob_left', 'touching_knob_right', 'touching_knob_center', etc.)
        verbose: 是否打印详细信息
        display_process: 是否显示处理过程
        **kwargs: 额外参数，如自定义裁剪参数等
        
    Returns:
        根据status返回不同的结果：
        - 非touching_*状态：返回完整检测结果 (display_img, top_row, bottom_row, knob, handle_angle, mode_code)
        - touching_*状态：返回单个目标检测结果 {'target': target_info, 'confidence': score, 'display_img': img, 'crop_info': info}
    """
    if verbose:
        # print(f"detect_buttons_by_status called with status: {status}")
        pass
    
    # 状态到目标类型的映射
    status_to_target = {
        'touching_left_button': 'left_button',
        'touching_right_button': 'right_button', 
        'touching_knob_left': 'knob_left',
        'touching_knob_right': 'knob_right',
        'touching_knob_center': 'knob_center',
    }
    
    # 检查是否为单目标检测状态
    if status in status_to_target:
        # print(f'debug | status {status} in status_to_target')
        target_type = status_to_target[status]
        
        # 获取自定义裁剪参数（如果有）
        crop_params = kwargs.get('crop_params', None)
        
        if verbose:
            # print(f"Single target detection mode: {target_type}")
            pass
        
        # 执行单目标检测
        target_result, confidence_score, display_img, crop_info = detect_single_target(
            image, target_type, crop_params, verbose, display_process
        )
        
        # 返回单目标检测结果
        return {
            'target': target_result,
            'confidence': confidence_score,
            'display_img': display_img,
            'crop_info': crop_info,
            'status': status,
            'target_type': target_type
        }
    
    else:
        # 非单目标状态，使用完整检测
        if verbose:
            # print("Full detection mode (multiple buttons and knob)")
            pass
        
        # 调用原始的完整检测函数
        return detect_buttons(image, verbose, display_process)


def convert_coordinates_to_original(coords, crop_info):
    """
    将裁剪图像中的坐标转换回原图坐标
    
    Args:
        coords: 裁剪图像中的坐标 (x, y) 或 (x, y, r)
        crop_info: 裁剪信息字典
        
    Returns:
        original_coords: 原图坐标
    """
    if coords is None:
        return None
    
    if len(coords) == 2:
        x, y = coords
        original_x = x + crop_info['offset_x']
        original_y = y + crop_info['offset_y']
        return (original_x, original_y)
    elif len(coords) == 3:
        x, y, r = coords
        original_x = x + crop_info['offset_x']
        original_y = y + crop_info['offset_y']
        return (original_x, original_y, r)
    else:
        return coords

def preprocess_image(image, gamma=1.2, noise_kernel_size=1):
    """
    Preprocess image to improve color detection robustness
    
    Args:
        image: Input RGB image
        gamma: Gamma correction factor (default: 1.2)
        noise_kernel_size: Size of noise reduction kernel (default: 3)
        use_adaptive_thresh: Whether to apply adaptive thresholding (default: False)
    
    Returns:
        Preprocessed image with improved color stability
    """
    # # 高斯去噪
    # denoised_image = cv2.GaussianBlur(image, (noise_kernel_size, noise_kernel_size), 0)
    
    # # 伽马校正
    # gamma_corrected = np.array(255 * (denoised_image / 255) ** gamma, dtype=np.uint8)
    
    # Convert to float for processing
    img_float = image.astype(np.float32) / 255.0
    
    # White balance using gray world assumption
    img_mean_r = np.mean(img_float[:,:,0])
    img_mean_g = np.mean(img_float[:,:,1])
    img_mean_b = np.mean(img_float[:,:,2])
    
    # Normalize channels
    img_float[:,:,0] = img_float[:,:,0] * (img_mean_g / img_mean_r)
    img_float[:,:,2] = img_float[:,:,2] * (img_mean_g / img_mean_b)
    
    # Clip values to valid range
    img_float = np.clip(img_float, 0, 1)
    
    # Convert back to uint8
    balanced_image = (img_float * 255).astype(np.uint8)
    
    # Optional: Enhance contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    lab = cv2.cvtColor(balanced_image, cv2.COLOR_BGR2LAB)
    lab_planes = list(cv2.split(lab))  # Convert tuple to list
    
    # 自适应直方图均衡化
    lab_planes[0] = clahe.apply(lab_planes[0])
    lab = cv2.merge(lab_planes)
    enhanced_image = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
    
    return enhanced_image
