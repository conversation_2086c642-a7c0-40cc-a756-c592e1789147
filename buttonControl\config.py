"""
Configuration management module for RoboArm project.
Centralized parameter management and interactive GUI for all robot operations.
"""

import json
import os
# import tkinter as tk
# from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, Tuple, Any, Optional


class RobotConfig:
    """
    Centralized configuration management for robot operations.
    Handles distance parameters, offsets, movement parameters, and other settings.
    """
    
    def __init__(self, config_file: str = None):
        # If no config file specified, use default path in buttonControl directory
        if config_file is None:
            config_file = os.path.join(os.path.dirname(__file__), "robot_config.json")
        self.config_file = config_file
        
        # Default distance parameters (in meters)
        self.observation_distance = 0.4      # First step approach distance
        self.touch_distance = 0.25           # Second step approach distance
        self.click_depth = 0.09              # Actual click depth
        self.turn_depth = 0.06              # Knob turn depth
        self.smallest_working_distance = 0.5  # Minimum working distance for search
        self.largest_working_distance = 1.0   # Maximum working distance for search
        
        # Default button and knob offsets (x, y) in button coordinate system
        self.left_button_offsets = (-0.01, 0.045)   # Offset for left button approach
        self.right_button_offsets = (-0.011, -0.03)  # Offset for right button approach
        self.knob_offsets = (-0.005, 0.005)           # Offset for knob approach
        
        # Default movement parameters
        self.movement_v = 30           # 速度参数
        self.movement_r = 50           # 半径参数  
        self.movement_connect = 0      # 连接参数
        self.movement_block = 1        # 阻塞参数
        
        # Default robot settings
        self.robot_ip = "************"
        self.robot_port = 8080
        self.arm_model = "RM_MODEL_RM_63_III_E"
        self.force_type = "RM_MODEL_RM_B_E"

        # Default search parameters
        self.joint5_angle = -101.048

        # Default auto-reset delay (in seconds)
        self.auto_reset_delay = 2.0

        # Default camera settings
        self.camera_width = 640
        self.camera_height = 480
        self.camera_fps = 30
        
        # Default processing settings
        self.use_robust_normal = True     # 是否使用鲁棒法向量计算
        self.multi_frame_count = 10        # Multi-frame collection count
        self.target_button = 'left'        # Default target button
        
        # Default depth estimation settings (hollow circle parameters)
        self.outer_large_radius_ratio = 2.0   # Outer radius for hollow circle (multiplier of button radius)
        self.outer_small_radius_ratio = 1.5   # Inner radius for hollow circle (multiplier of button radius)
        self.inner_radius_ratio = 0.6         # Inner radius for fallback shrink circle (multiplier of button radius)
        
        # Default fine-tuning settings
        self.left_button_ideal_xyz_cam = [0.086747, 0.078263, 0.237]    # Left button center ideal position (camera coordinates: x, y, z in meters)
        self.right_button_ideal_xyz_cam = [0.004522, 0.080774, 0.241]   # Right button center ideal position (camera coordinates: x, y, z in meters)
        self.knob_center_ideal_xyz_cam = [0.04416216, 0.0780184, 0.243]
        self.knob_left_ideal_xyz_cam = [0.04883879, 0.07846441, 0.242]
        self.knob_right_ideal_xyz_cam = [0.04350585, 0.07683502, 0.24240116]
        # self.knob_ideal_xyz_cam = [0.048945, 0.082361, 0.241]  # 已废弃
        self.finetune_threshold = 0.001                    # Fine-tuning error threshold in meters (below this threshold, fine-tuning is considered complete)
        self.finetune_max_iterations = 3                  # Maximum number of fine-tuning iterations (stop if exceeded)
        
        # Default zero pose (same as pole_processor.py)
        self.zero_pose = [2.103, 75.533, -163.534, -7.251, 163.381, -188.2]

        # Add known_orientation (mock default)
        self.known_orientation = [-3.146, 13.908, -33.719, -7.169, -25.607, -11.949]

        # Load configuration from file if it exists
        self.load_config()
    
    def update_distances(self, **kwargs) -> Dict[str, float]:
        """
        Update distance parameters
        
        Args:
            observation_distance: First step approach distance
            touch_distance: Second step approach distance 
            click_depth: Actual click depth
            turn_depth: Knob turn depth
            
        Returns:
            Dictionary of updated distance parameters
        """
        updated = {}
        
        if 'observation_distance' in kwargs:
            self.observation_distance = kwargs['observation_distance']
            updated['observation_distance'] = self.observation_distance
            
        if 'touch_distance' in kwargs:
            self.touch_distance = kwargs['touch_distance']
            updated['touch_distance'] = self.touch_distance
            
        if 'click_depth' in kwargs:
            self.click_depth = kwargs['click_depth']
            updated['click_depth'] = self.click_depth
            
        if 'turn_depth' in kwargs:
            self.turn_depth = kwargs['turn_depth']
            updated['turn_depth'] = self.turn_depth
            
        if 'smallest_working_distance' in kwargs:
            self.smallest_working_distance = kwargs['smallest_working_distance']
            updated['smallest_working_distance'] = self.smallest_working_distance
            
        if 'largest_working_distance' in kwargs:
            self.largest_working_distance = kwargs['largest_working_distance']
            updated['largest_working_distance'] = self.largest_working_distance
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def update_offsets(self, **kwargs) -> Dict[str, Tuple[float, float]]:
        """
        Update button and knob offsets
        
        Args:
            left_button_offsets: Offset for left button approach
            right_button_offsets: Offset for right button approach
            knob_offsets: Offset for knob approach
            
        Returns:
            Dictionary of updated offset parameters
        """
        updated = {}
        
        if 'left_button_offsets' in kwargs:
            self.left_button_offsets = kwargs['left_button_offsets']
            updated['left_button_offsets'] = self.left_button_offsets
            
        if 'right_button_offsets' in kwargs:
            self.right_button_offsets = kwargs['right_button_offsets']
            updated['right_button_offsets'] = self.right_button_offsets
            
        if 'knob_offsets' in kwargs:
            self.knob_offsets = kwargs['knob_offsets']
            updated['knob_offsets'] = self.knob_offsets
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def update_movement_parameters(self, **kwargs) -> Dict[str, Any]:
        """
        Update movement parameters
        
        Args:
            v: Movement speed parameter
            r: Movement radius parameter
            connect: Movement connect parameter
            block: Movement block parameter
            
        Returns:
            Dictionary of updated movement parameters
        """
        updated = {}
        
        if 'v' in kwargs:
            self.movement_v = int(kwargs['v'])
            updated['v'] = self.movement_v
            
        if 'r' in kwargs:
            self.movement_r = int(kwargs['r'])
            updated['r'] = self.movement_r
            
        if 'connect' in kwargs:
            self.movement_connect = kwargs['connect']
            updated['connect'] = self.movement_connect
            
        if 'block' in kwargs:
            self.movement_block = kwargs['block']
            updated['block'] = self.movement_block
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def update_robot_settings(self, **kwargs) -> Dict[str, Any]:
        """
        Update robot connection settings
        
        Args:
            robot_ip: Robot IP address
            robot_port: Robot port number
            arm_model: Robot arm model
            force_type: Robot force type
            
        Returns:
            Dictionary of updated robot settings
        """
        updated = {}
        
        if 'robot_ip' in kwargs:
            self.robot_ip = kwargs['robot_ip']
            updated['robot_ip'] = self.robot_ip
            
        if 'robot_port' in kwargs:
            self.robot_port = kwargs['robot_port']
            updated['robot_port'] = self.robot_port
            
        if 'arm_model' in kwargs:
            self.arm_model = kwargs['arm_model']
            updated['arm_model'] = self.arm_model
            
        if 'force_type' in kwargs:
            self.force_type = kwargs['force_type']
            updated['force_type'] = self.force_type
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def update_processing_settings(self, **kwargs) -> Dict[str, Any]:
        """
        Update processing settings

        Args:
            use_robust_normal: Whether to use robust normal calculation
            multi_frame_count: Multi-frame collection count
            target_button: Default target button ('left' or 'right')
            outer_large_radius_ratio: Outer radius for hollow circle (multiplier of button radius)
            outer_small_radius_ratio: Inner radius for hollow circle (multiplier of button radius)
            inner_radius_ratio: Inner radius for fallback shrink circle (multiplier of button radius)
            left_button_ideal_xyz_cam: Left button center ideal position (camera coordinates in meters)
            right_button_ideal_xyz_cam: Right button center ideal position (camera coordinates in meters)
            knob_center_ideal_xyz_cam: Knob center ideal position (camera coordinates in meters)
            knob_left_ideal_xyz_cam: Knob left position ideal position (camera coordinates in meters)
            knob_right_ideal_xyz_cam: Knob right position ideal position (camera coordinates in meters)
            finetune_threshold: Fine-tuning error threshold in meters
            finetune_max_iterations: Maximum number of fine-tuning iterations

        Returns:
            Dictionary of updated processing settings
        """
        updated = {}
        
        if 'use_robust_normal' in kwargs:
            self.use_robust_normal = kwargs['use_robust_normal']
            updated['use_robust_normal'] = self.use_robust_normal
            
        if 'multi_frame_count' in kwargs:
            self.multi_frame_count = kwargs['multi_frame_count']
            updated['multi_frame_count'] = self.multi_frame_count
            
        if 'target_button' in kwargs:
            self.target_button = kwargs['target_button']
            updated['target_button'] = self.target_button
            
        if 'outer_large_radius_ratio' in kwargs:
            self.outer_large_radius_ratio = kwargs['outer_large_radius_ratio']
            updated['outer_large_radius_ratio'] = self.outer_large_radius_ratio
            
        if 'outer_small_radius_ratio' in kwargs:
            self.outer_small_radius_ratio = kwargs['outer_small_radius_ratio']
            updated['outer_small_radius_ratio'] = self.outer_small_radius_ratio
            
        if 'inner_radius_ratio' in kwargs:
            self.inner_radius_ratio = kwargs['inner_radius_ratio']
            updated['inner_radius_ratio'] = self.inner_radius_ratio
            
        if 'left_button_ideal_xyz_cam' in kwargs:
            self.left_button_ideal_xyz_cam = kwargs['left_button_ideal_xyz_cam']
            updated['left_button_ideal_xyz_cam'] = self.left_button_ideal_xyz_cam
            
        if 'right_button_ideal_xyz_cam' in kwargs:
            self.right_button_ideal_xyz_cam = kwargs['right_button_ideal_xyz_cam']
            updated['right_button_ideal_xyz_cam'] = self.right_button_ideal_xyz_cam
            
        if 'knob_center_ideal_xyz_cam' in kwargs:
            self.knob_center_ideal_xyz_cam = kwargs['knob_center_ideal_xyz_cam']
            updated['knob_center_ideal_xyz_cam'] = self.knob_center_ideal_xyz_cam
        if 'knob_left_ideal_xyz_cam' in kwargs:
            self.knob_left_ideal_xyz_cam = kwargs['knob_left_ideal_xyz_cam']
            updated['knob_left_ideal_xyz_cam'] = self.knob_left_ideal_xyz_cam
        if 'knob_right_ideal_xyz_cam' in kwargs:
            self.knob_right_ideal_xyz_cam = kwargs['knob_right_ideal_xyz_cam']
            updated['knob_right_ideal_xyz_cam'] = self.knob_right_ideal_xyz_cam
            
        if 'finetune_threshold' in kwargs:
            self.finetune_threshold = kwargs['finetune_threshold']
            updated['finetune_threshold'] = self.finetune_threshold
            
        if 'finetune_max_iterations' in kwargs:
            self.finetune_max_iterations = kwargs['finetune_max_iterations']
            updated['finetune_max_iterations'] = self.finetune_max_iterations
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def update_camera_settings(self, **kwargs) -> Dict[str, Any]:
        """
        Update camera settings
        
        Args:
            camera_width: Camera width
            camera_height: Camera height
            camera_fps: Camera FPS
            
        Returns:
            Dictionary of updated camera settings
        """
        updated = {}
        
        if 'camera_width' in kwargs:
            self.camera_width = kwargs['camera_width']
            updated['camera_width'] = self.camera_width
            
        if 'camera_height' in kwargs:
            self.camera_height = kwargs['camera_height']
            updated['camera_height'] = self.camera_height
            
        if 'camera_fps' in kwargs:
            self.camera_fps = kwargs['camera_fps']
            updated['camera_fps'] = self.camera_fps
        
        # Auto-save after updates
        self.save_config()
        return updated
    
    def get_distances(self) -> Dict[str, float]:
        """Get all distance parameters"""
        return {
            'observation_distance': self.observation_distance,
            'touch_distance': self.touch_distance,
            'click_depth': self.click_depth,
            'turn_depth': self.turn_depth,
            'smallest_working_distance': self.smallest_working_distance,
            'largest_working_distance': self.largest_working_distance
        }
    
    def get_offsets(self) -> Dict[str, Tuple[float, float]]:
        """Get all offset parameters"""
        return {
            'left_button_offsets': self.left_button_offsets,
            'right_button_offsets': self.right_button_offsets,
            'knob_offsets': self.knob_offsets
        }
    
    def get_movement_parameters(self) -> Dict[str, Any]:
        """Get all movement parameters"""
        return {
            'v': self.movement_v,
            'r': self.movement_r,
            'connect': self.movement_connect,
            'block': self.movement_block
        }
    
    def get_robot_settings(self) -> Dict[str, Any]:
        """Get all robot settings"""
        return {
            'robot_ip': self.robot_ip,
            'robot_port': self.robot_port,
            'arm_model': self.arm_model,
            'force_type': self.force_type
        }
    
    def get_processing_settings(self) -> Dict[str, Any]:
        """Get all processing settings"""
        return {
            'use_robust_normal': self.use_robust_normal,
            'multi_frame_count': self.multi_frame_count,
            'target_button': self.target_button,
            'outer_large_radius_ratio': self.outer_large_radius_ratio,
            'outer_small_radius_ratio': self.outer_small_radius_ratio,
            'inner_radius_ratio': self.inner_radius_ratio,
            'left_button_ideal_xyz_cam': self.left_button_ideal_xyz_cam,
            'right_button_ideal_xyz_cam': self.right_button_ideal_xyz_cam,
            'knob_center_ideal_xyz_cam': self.knob_center_ideal_xyz_cam,
            'knob_left_ideal_xyz_cam': self.knob_left_ideal_xyz_cam,
            'knob_right_ideal_xyz_cam': self.knob_right_ideal_xyz_cam,
            'finetune_threshold': self.finetune_threshold,
            'finetune_max_iterations': self.finetune_max_iterations
        }
    
    def get_camera_settings(self) -> Dict[str, Any]:
        """Get all camera settings"""
        return {
            'camera_width': self.camera_width,
            'camera_height': self.camera_height,
            'camera_fps': self.camera_fps
        }

    def get_search_parameters(self) -> Dict[str, Any]:
        """Get all search parameters"""
        return {
            'joint5_angle': self.joint5_angle
        }
    
    def get_known_orientation(self):
        return self.known_orientation

    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration parameters"""
        return {
            'distances': self.get_distances(),
            'offsets': self.get_offsets(),
            'movement': self.get_movement_parameters(),
            'robot': self.get_robot_settings(),
            'processing': self.get_processing_settings(),
            'camera': self.get_camera_settings(),
            'search_parameters': self.get_search_parameters(),
            'zero_pose': self.zero_pose,
            'known_orientation': self.known_orientation,
            'auto_reset_delay': self.auto_reset_delay
        }
    
    def save_config(self) -> bool:
        """Save configuration to file"""
        try:
            config_data = self.get_all_config()
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            return True
        except Exception as e:
            print(f"Warning: Failed to save config to {self.config_file}: {e}")
            return False
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if not os.path.exists(self.config_file):
                print(f"Config file {self.config_file} not found, using defaults")
                return False
                
            with open(self.config_file, 'r') as f:
                config_data = json.load(f)
            
            # Update distances
            if 'distances' in config_data:
                distances = config_data['distances']
                self.observation_distance = distances.get('observation_distance', self.observation_distance)
                self.touch_distance = distances.get('touch_distance', self.touch_distance)
                self.click_depth = distances.get('click_depth', self.click_depth)
                self.turn_depth = distances.get('turn_depth', self.turn_depth)
                self.smallest_working_distance = distances.get('smallest_working_distance', self.smallest_working_distance)
                self.largest_working_distance = distances.get('largest_working_distance', self.largest_working_distance)
            
            # Update offsets
            if 'offsets' in config_data:
                offsets = config_data['offsets']
                self.left_button_offsets = tuple(offsets.get('left_button_offsets', self.left_button_offsets))
                self.right_button_offsets = tuple(offsets.get('right_button_offsets', self.right_button_offsets))
                self.knob_offsets = tuple(offsets.get('knob_offsets', self.knob_offsets))
            
            # Update movement parameters
            if 'movement' in config_data:
                movement = config_data['movement']
                self.movement_v = movement.get('v', self.movement_v)
                self.movement_r = movement.get('r', self.movement_r)
                self.movement_connect = movement.get('connect', self.movement_connect)
                self.movement_block = movement.get('block', self.movement_block)
            
            # Update robot settings
            if 'robot' in config_data:
                robot = config_data['robot']
                self.robot_ip = robot.get('robot_ip', self.robot_ip)
                self.robot_port = robot.get('robot_port', self.robot_port)
                self.arm_model = robot.get('arm_model', self.arm_model)
                self.force_type = robot.get('force_type', self.force_type)
            
            # Update processing settings
            if 'processing' in config_data:
                processing = config_data['processing']
                self.use_robust_normal = processing.get('use_robust_normal', self.use_robust_normal)
                self.multi_frame_count = processing.get('multi_frame_count', self.multi_frame_count)
                self.target_button = processing.get('target_button', self.target_button)
                self.outer_large_radius_ratio = processing.get('outer_large_radius_ratio', self.outer_large_radius_ratio)
                self.outer_small_radius_ratio = processing.get('outer_small_radius_ratio', self.outer_small_radius_ratio)
                self.inner_radius_ratio = processing.get('inner_radius_ratio', self.inner_radius_ratio)
                self.left_button_ideal_xyz_cam = processing.get('left_button_ideal_xyz_cam', self.left_button_ideal_xyz_cam)
                self.right_button_ideal_xyz_cam = processing.get('right_button_ideal_xyz_cam', self.right_button_ideal_xyz_cam)
                self.knob_center_ideal_xyz_cam = processing.get('knob_center_ideal_xyz_cam', self.knob_center_ideal_xyz_cam)
                self.knob_left_ideal_xyz_cam = processing.get('knob_left_ideal_xyz_cam', self.knob_left_ideal_xyz_cam)
                self.knob_right_ideal_xyz_cam = processing.get('knob_right_ideal_xyz_cam', self.knob_right_ideal_xyz_cam)
                self.finetune_threshold = processing.get('finetune_threshold', self.finetune_threshold)
                self.finetune_max_iterations = processing.get('finetune_max_iterations', self.finetune_max_iterations)
            
            # Update camera settings
            if 'camera' in config_data:
                camera = config_data['camera']
                self.camera_width = camera.get('camera_width', self.camera_width)
                self.camera_height = camera.get('camera_height', self.camera_height)
                self.camera_fps = camera.get('camera_fps', self.camera_fps)
            
            # Update zero pose
            if 'zero_pose' in config_data:
                self.zero_pose = config_data.get('zero_pose', self.zero_pose)
            # Update known_orientation
            if 'known_orientation' in config_data:
                ko = config_data['known_orientation']
                if isinstance(ko, list) and len(ko) == 6:
                    self.known_orientation = ko
                else:
                    self.known_orientation = None

            # Update search parameters
            if 'search_parameters' in config_data:
                search_params = config_data['search_parameters']
                self.joint5_angle = search_params.get('joint5_angle', self.joint5_angle)

            # Update auto-reset delay
            self.auto_reset_delay = config_data.get('auto_reset_delay', self.auto_reset_delay)

            print(f"Configuration loaded from {self.config_file}")
            return True
            
        except Exception as e:
            print(f"Warning: Failed to load config from {self.config_file}: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """Reset all parameters to default values"""
        self.__init__(self.config_file)
        return self.save_config()
    
    def print_config(self):
        """Print current configuration in a formatted way"""
        print("\n=== Current Robot Configuration ===")
        
        distances = self.get_distances()
        print("Distance Parameters:")
        for key, value in distances.items():
            print(f"  {key}: {value*1000:.1f}mm")
        
        offsets = self.get_offsets()
        print("Offset Parameters:")
        for key, value in offsets.items():
            print(f"  {key}: {value}")
        
        movement = self.get_movement_parameters()
        print("Movement Parameters:")
        for key, value in movement.items():
            print(f"  {key}: {value}")
        
        robot = self.get_robot_settings()
        print("Robot Settings:")
        for key, value in robot.items():
            print(f"  {key}: {value}")
        
        processing = self.get_processing_settings()
        print("Processing Settings:")
        for key, value in processing.items():
            if key in ['left_button_ideal_xyz_cam', 'right_button_ideal_xyz_cam', 'knob_center_ideal_xyz_cam', 'knob_left_ideal_xyz_cam', 'knob_right_ideal_xyz_cam']:
                print(f"  {key}: {value} (x, y, z in meters)")
            elif key == 'finetune_threshold':
                print(f"  {key}: {value} m")
            else:
                print(f"  {key}: {value}")
        
        camera = self.get_camera_settings()
        print("Camera Settings:")
        for key, value in camera.items():
            print(f"  {key}: {value}")
        
        print(f"Zero Pose: {self.zero_pose}")
        
        print("===================================\n")


class ConfigPanel:
    """
    GUI-based configuration panel for editing all robot parameters
    
    This class provides a comprehensive interface for adjusting:
    - Distance parameters (observation, touch, click, turn, adjustable)
    - Button and knob offsets (left, right, knob)
    - Movement parameters (v, r, connect, block)
    - Robot settings (IP, port, model, force type)
    - Processing settings (robust normal, frame count, target button)
    - Camera settings (width, height, fps)
    """
    
    def __init__(self, robot_config: RobotConfig, controller=None):
        """
        Initialize the configuration panel
        
        Args:
            robot_config: RobotConfig instance to modify
            controller: Optional controller instance for status messages
        """
        self.robot_config = robot_config
        self.controller = controller
        self.root = None
        self.vars = {}
        self.updated = False
    
    # def show_config_panel(self):
    #     """Show the configuration panel window"""
    #     try:
    #         # Create main window
    #         self.root = tk.Tk()
    #         self.root.title("RoboArm Configuration Panel")
    #         self.root.geometry("800x700")
    #         self.root.resizable(True, True)
            
    #         # Create notebook for different parameter groups
    #         notebook = ttk.Notebook(self.root)
    #         notebook.pack(fill='both', expand=True, padx=10, pady=10)
            
    #         # Create frames for different parameter groups
    #         self._create_distance_frame(notebook)
    #         self._create_offset_frame(notebook)
    #         self._create_movement_frame(notebook)
    #         self._create_robot_frame(notebook)
    #         self._create_processing_frame(notebook)
    #         self._create_camera_frame(notebook)
            
    #         # Create button frame
    #         button_frame = tk.Frame(self.root)
    #         button_frame.pack(fill='x', padx=10, pady=5)
            
    #         # Create buttons
    #         update_btn = tk.Button(
    #             button_frame, 
    #             text="Update", 
    #             command=self._update_config,
    #             bg='#4CAF50', 
    #             fg='white', 
    #             font=('Arial', 12, 'bold'),
    #             width=15
    #         )
    #         update_btn.pack(side='right', padx=5)
            
    #         cancel_btn = tk.Button(
    #             button_frame, 
    #             text="Cancel", 
    #             command=self._cancel_config,
    #             bg='#f44336', 
    #             fg='white', 
    #             font=('Arial', 12, 'bold'),
    #             width=15
    #         )
    #         cancel_btn.pack(side='right', padx=5)
            
    #         reset_btn = tk.Button(
    #             button_frame, 
    #             text="Reset to Defaults", 
    #             command=self._reset_config,
    #             bg='#ff9800', 
    #             fg='white', 
    #             font=('Arial', 12),
    #             width=15
    #         )
    #         reset_btn.pack(side='left', padx=5)
            
    #         # Center the window
    #         self.root.update_idletasks()
    #         x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
    #         y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
    #         self.root.geometry(f"+{x}+{y}")
            
    #         # Make window modal
    #         self.root.transient()
    #         self.root.grab_set()
            
    #         # Start the GUI loop
    #         self.root.mainloop()
            
    #         return self.updated
            
    #     except Exception as e:
    #         print(f"Error showing config panel: {e}")
    #         if self.root:
    #             self.root.destroy()
    #         return False
    
    # def _create_distance_frame(self, notebook):
    #     """Create distance parameters frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Distance Parameters")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Distance parameters
    #     distances = self.robot_config.get_distances()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Distance Parameters (meters)", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=3, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     distance_labels = {
    #         'observation_distance': 'Observation Distance (Step 1)',
    #         'touch_distance': 'Touch Distance (Step 2)',
    #         'click_depth': 'Click Depth',
    #         'turn_depth': 'Turn Depth'
    #     }
        
    #     for key, label in distance_labels.items():
    #         tk.Label(scrollable_frame, text=f"{label}:", width=25, anchor='w').grid(
    #             row=row, column=0, padx=5, pady=2, sticky='w'
    #         )
            
    #         var = tk.DoubleVar(value=distances[key])
    #         self.vars[f'distance_{key}'] = var
            
    #         entry = tk.Entry(scrollable_frame, textvariable=var, width=15)
    #         entry.grid(row=row, column=1, padx=5, pady=2)
            
    #         # Show mm equivalent
    #         mm_label = tk.Label(scrollable_frame, text=f"({distances[key]*1000:.1f} mm)", 
    #                            fg='gray')
    #         mm_label.grid(row=row, column=2, padx=5, pady=2, sticky='w')
            
    #         row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _create_offset_frame(self, notebook):
    #     """Create offset parameters frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Offset Parameters")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Offset parameters
    #     offsets = self.robot_config.get_offsets()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Button/Knob Offset Parameters (meters)", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=4, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     offset_labels = {
    #         'left_button_offsets': 'Left Button Offsets',
    #         'right_button_offsets': 'Right Button Offsets',
    #         'knob_offsets': 'Knob Offsets'
    #     }
        
    #     for key, label in offset_labels.items():
    #         tk.Label(scrollable_frame, text=f"{label}:", width=20, anchor='w').grid(
    #             row=row, column=0, padx=5, pady=2, sticky='w'
    #         )
            
    #         # X offset
    #         tk.Label(scrollable_frame, text="X:", width=3).grid(
    #             row=row, column=1, padx=2, pady=2
    #         )
    #         var_x = tk.DoubleVar(value=offsets[key][0])
    #         self.vars[f'offset_{key}_x'] = var_x
    #         entry_x = tk.Entry(scrollable_frame, textvariable=var_x, width=10)
    #         entry_x.grid(row=row, column=2, padx=2, pady=2)
            
    #         # Y offset
    #         tk.Label(scrollable_frame, text="Y:", width=3).grid(
    #             row=row, column=3, padx=2, pady=2
    #         )
    #         var_y = tk.DoubleVar(value=offsets[key][1])
    #         self.vars[f'offset_{key}_y'] = var_y
    #         entry_y = tk.Entry(scrollable_frame, textvariable=var_y, width=10)
    #         entry_y.grid(row=row, column=4, padx=2, pady=2)
            
    #         row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _create_movement_frame(self, notebook):
    #     """Create movement parameters frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Movement Parameters")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Movement parameters
    #     movement = self.robot_config.get_movement_parameters()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Robot Movement Parameters", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=3, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     movement_labels = {
    #         'v': 'Velocity Parameter',
    #         'r': 'Radius Parameter',
    #         'connect': 'Connect Parameter',
    #         'block': 'Block Parameter'
    #     }
        
    #     for key, label in movement_labels.items():
    #         tk.Label(scrollable_frame, text=f"{label}:", width=20, anchor='w').grid(
    #             row=row, column=0, padx=5, pady=2, sticky='w'
    #         )
            
    #         if key in ['connect', 'block']:
    #             var = tk.IntVar(value=movement[key])
    #         else:
    #             var = tk.DoubleVar(value=movement[key])
            
    #         self.vars[f'movement_{key}'] = var
            
    #         entry = tk.Entry(scrollable_frame, textvariable=var, width=15)
    #         entry.grid(row=row, column=1, padx=5, pady=2)
            
    #         row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _create_robot_frame(self, notebook):
    #     """Create robot settings frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Robot Settings")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Robot settings
    #     robot = self.robot_config.get_robot_settings()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Robot Connection Settings", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=3, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     # Robot IP
    #     tk.Label(scrollable_frame, text="Robot IP Address:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_ip = tk.StringVar(value=robot['robot_ip'])
    #     self.vars['robot_ip'] = var_ip
    #     entry_ip = tk.Entry(scrollable_frame, textvariable=var_ip, width=20)
    #     entry_ip.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Robot Port
    #     tk.Label(scrollable_frame, text="Robot Port:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_port = tk.IntVar(value=robot['robot_port'])
    #     self.vars['robot_port'] = var_port
    #     entry_port = tk.Entry(scrollable_frame, textvariable=var_port, width=20)
    #     entry_port.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Arm Model
    #     tk.Label(scrollable_frame, text="Arm Model:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_model = tk.StringVar(value=robot['arm_model'])
    #     self.vars['arm_model'] = var_model
    #     entry_model = tk.Entry(scrollable_frame, textvariable=var_model, width=30)
    #     entry_model.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Force Type
    #     tk.Label(scrollable_frame, text="Force Type:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_force = tk.StringVar(value=robot['force_type'])
    #     self.vars['force_type'] = var_force
    #     entry_force = tk.Entry(scrollable_frame, textvariable=var_force, width=30)
    #     entry_force.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _create_processing_frame(self, notebook):
    #     """Create processing settings frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Processing Settings")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Processing settings
    #     processing = self.robot_config.get_processing_settings()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Data Processing Settings", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=3, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     # Use Robust Normal
    #     tk.Label(scrollable_frame, text="Use Robust Normal:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_robust = tk.BooleanVar(value=processing['use_robust_normal'])
    #     self.vars['use_robust_normal'] = var_robust
    #     check_robust = tk.Checkbutton(scrollable_frame, variable=var_robust)
    #     check_robust.grid(row=row, column=1, padx=5, pady=2, sticky='w')
    #     row += 1
        
    #     # Multi-frame Count
    #     tk.Label(scrollable_frame, text="Multi-frame Count:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_frames = tk.IntVar(value=processing['multi_frame_count'])
    #     self.vars['multi_frame_count'] = var_frames
    #     entry_frames = tk.Entry(scrollable_frame, textvariable=var_frames, width=15)
    #     entry_frames.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Target Button
    #     tk.Label(scrollable_frame, text="Target Button:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_target = tk.StringVar(value=processing['target_button'])
    #     self.vars['target_button'] = var_target
    #     combo_target = ttk.Combobox(scrollable_frame, textvariable=var_target, 
    #                                values=['left', 'right'], width=12)
    #     combo_target.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Fine-tuning Settings
    #     title_finetune = tk.Label(scrollable_frame, text="Fine-tuning Settings", 
    #                              font=('Arial', 11, 'bold'))
    #     title_finetune.grid(row=row, column=0, columnspan=4, pady=(20, 10), sticky='w')
    #     row += 1
        
    #     # Left Button Ideal Position
    #     tk.Label(scrollable_frame, text="Left Button Ideal (cam):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     # X meters
    #     tk.Label(scrollable_frame, text="X(m):", width=5).grid(row=row, column=1, padx=2, pady=2)
    #     var_left_x = tk.DoubleVar(value=processing['left_button_ideal_xyz_cam'][0])
    #     self.vars['left_button_ideal_x'] = var_left_x
    #     entry_left_x = tk.Entry(scrollable_frame, textvariable=var_left_x, width=8)
    #     entry_left_x.grid(row=row, column=2, padx=2, pady=2)
    #     # Y meters
    #     tk.Label(scrollable_frame, text="Y(m):", width=5).grid(row=row, column=3, padx=2, pady=2)
    #     var_left_y = tk.DoubleVar(value=processing['left_button_ideal_xyz_cam'][1])
    #     self.vars['left_button_ideal_y'] = var_left_y
    #     entry_left_y = tk.Entry(scrollable_frame, textvariable=var_left_y, width=8)
    #     entry_left_y.grid(row=row, column=4, padx=2, pady=2)
    #     # Z meters
    #     tk.Label(scrollable_frame, text="Z(m):", width=5).grid(row=row, column=5, padx=2, pady=2)
    #     var_left_d = tk.DoubleVar(value=processing['left_button_ideal_xyz_cam'][2])
    #     self.vars['left_button_ideal_d'] = var_left_d
    #     entry_left_d = tk.Entry(scrollable_frame, textvariable=var_left_d, width=8)
    #     entry_left_d.grid(row=row, column=6, padx=2, pady=2)
    #     row += 1
        
    #     # Right Button Ideal Position
    #     tk.Label(scrollable_frame, text="Right Button Ideal (cam):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     # X meters
    #     tk.Label(scrollable_frame, text="X(m):", width=5).grid(row=row, column=1, padx=2, pady=2)
    #     var_right_x = tk.DoubleVar(value=processing['right_button_ideal_xyz_cam'][0])
    #     self.vars['right_button_ideal_x'] = var_right_x
    #     entry_right_x = tk.Entry(scrollable_frame, textvariable=var_right_x, width=8)
    #     entry_right_x.grid(row=row, column=2, padx=2, pady=2)
    #     # Y meters
    #     tk.Label(scrollable_frame, text="Y(m):", width=5).grid(row=row, column=3, padx=2, pady=2)
    #     var_right_y = tk.DoubleVar(value=processing['right_button_ideal_xyz_cam'][1])
    #     self.vars['right_button_ideal_y'] = var_right_y
    #     entry_right_y = tk.Entry(scrollable_frame, textvariable=var_right_y, width=8)
    #     entry_right_y.grid(row=row, column=4, padx=2, pady=2)
    #     # Z meters
    #     tk.Label(scrollable_frame, text="Z(m):", width=5).grid(row=row, column=5, padx=2, pady=2)
    #     var_right_d = tk.DoubleVar(value=processing['right_button_ideal_xyz_cam'][2])
    #     self.vars['right_button_ideal_d'] = var_right_d
    #     entry_right_d = tk.Entry(scrollable_frame, textvariable=var_right_d, width=8)
    #     entry_right_d.grid(row=row, column=6, padx=2, pady=2)
    #     row += 1
        
    #     # Knob Center Ideal Position
    #     tk.Label(scrollable_frame, text="Knob Center Ideal (cam):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     # X meters
    #     tk.Label(scrollable_frame, text="X(m):", width=5).grid(row=row, column=1, padx=2, pady=2)
    #     var_knob_center_x = tk.DoubleVar(value=processing['knob_center_ideal_xyz_cam'][0])
    #     self.vars['knob_center_ideal_x'] = var_knob_center_x
    #     entry_knob_center_x = tk.Entry(scrollable_frame, textvariable=var_knob_center_x, width=8)
    #     entry_knob_center_x.grid(row=row, column=2, padx=2, pady=2)
    #     # Y meters
    #     tk.Label(scrollable_frame, text="Y(m):", width=5).grid(row=row, column=3, padx=2, pady=2)
    #     var_knob_center_y = tk.DoubleVar(value=processing['knob_center_ideal_xyz_cam'][1])
    #     self.vars['knob_center_ideal_y'] = var_knob_center_y
    #     entry_knob_center_y = tk.Entry(scrollable_frame, textvariable=var_knob_center_y, width=8)
    #     entry_knob_center_y.grid(row=row, column=4, padx=2, pady=2)
    #     # Z meters
    #     tk.Label(scrollable_frame, text="Z(m):", width=5).grid(row=row, column=5, padx=2, pady=2)
    #     var_knob_center_d = tk.DoubleVar(value=processing['knob_center_ideal_xyz_cam'][2])
    #     self.vars['knob_center_ideal_d'] = var_knob_center_d
    #     entry_knob_center_d = tk.Entry(scrollable_frame, textvariable=var_knob_center_d, width=8)
    #     entry_knob_center_d.grid(row=row, column=6, padx=2, pady=2)
    #     row += 1

    #     # Knob Left Ideal Position
    #     tk.Label(scrollable_frame, text="Knob Left Ideal (cam):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     # X meters
    #     tk.Label(scrollable_frame, text="X(m):", width=5).grid(row=row, column=1, padx=2, pady=2)
    #     var_knob_left_x = tk.DoubleVar(value=processing['knob_left_ideal_xyz_cam'][0])
    #     self.vars['knob_left_ideal_x'] = var_knob_left_x
    #     entry_knob_left_x = tk.Entry(scrollable_frame, textvariable=var_knob_left_x, width=8)
    #     entry_knob_left_x.grid(row=row, column=2, padx=2, pady=2)
    #     # Y meters
    #     tk.Label(scrollable_frame, text="Y(m):", width=5).grid(row=row, column=3, padx=2, pady=2)
    #     var_knob_left_y = tk.DoubleVar(value=processing['knob_left_ideal_xyz_cam'][1])
    #     self.vars['knob_left_ideal_y'] = var_knob_left_y
    #     entry_knob_left_y = tk.Entry(scrollable_frame, textvariable=var_knob_left_y, width=8)
    #     entry_knob_left_y.grid(row=row, column=4, padx=2, pady=2)
    #     # Z meters
    #     tk.Label(scrollable_frame, text="Z(m):", width=5).grid(row=row, column=5, padx=2, pady=2)
    #     var_knob_left_d = tk.DoubleVar(value=processing['knob_left_ideal_xyz_cam'][2])
    #     self.vars['knob_left_ideal_d'] = var_knob_left_d
    #     entry_knob_left_d = tk.Entry(scrollable_frame, textvariable=var_knob_left_d, width=8)
    #     entry_knob_left_d.grid(row=row, column=6, padx=2, pady=2)
    #     row += 1

    #     # Knob Right Ideal Position
    #     tk.Label(scrollable_frame, text="Knob Right Ideal (cam):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     # X meters
    #     tk.Label(scrollable_frame, text="X(m):", width=5).grid(row=row, column=1, padx=2, pady=2)
    #     var_knob_right_x = tk.DoubleVar(value=processing['knob_right_ideal_xyz_cam'][0])
    #     self.vars['knob_right_ideal_x'] = var_knob_right_x
    #     entry_knob_right_x = tk.Entry(scrollable_frame, textvariable=var_knob_right_x, width=8)
    #     entry_knob_right_x.grid(row=row, column=2, padx=2, pady=2)
    #     # Y meters
    #     tk.Label(scrollable_frame, text="Y(m):", width=5).grid(row=row, column=3, padx=2, pady=2)
    #     var_knob_right_y = tk.DoubleVar(value=processing['knob_right_ideal_xyz_cam'][1])
    #     self.vars['knob_right_ideal_y'] = var_knob_right_y
    #     entry_knob_right_y = tk.Entry(scrollable_frame, textvariable=var_knob_right_y, width=8)
    #     entry_knob_right_y.grid(row=row, column=4, padx=2, pady=2)
    #     # Z meters
    #     tk.Label(scrollable_frame, text="Z(m):", width=5).grid(row=row, column=5, padx=2, pady=2)
    #     var_knob_right_d = tk.DoubleVar(value=processing['knob_right_ideal_xyz_cam'][2])
    #     self.vars['knob_right_ideal_d'] = var_knob_right_d
    #     entry_knob_right_d = tk.Entry(scrollable_frame, textvariable=var_knob_right_d, width=8)
    #     entry_knob_right_d.grid(row=row, column=6, padx=2, pady=2)
    #     row += 1
        
    #     # Fine-tuning Threshold
    #     tk.Label(scrollable_frame, text="Finetune Threshold (m):", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_threshold = tk.DoubleVar(value=processing['finetune_threshold'])
    #     self.vars['finetune_threshold'] = var_threshold
    #     entry_threshold = tk.Entry(scrollable_frame, textvariable=var_threshold, width=15)
    #     entry_threshold.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     # Fine-tuning Max Iterations
    #     tk.Label(scrollable_frame, text="Finetune Max Iterations:", width=20, anchor='w').grid(
    #         row=row, column=0, padx=5, pady=2, sticky='w'
    #     )
    #     var_max_iter = tk.IntVar(value=processing['finetune_max_iterations'])
    #     self.vars['finetune_max_iterations'] = var_max_iter
    #     entry_max_iter = tk.Entry(scrollable_frame, textvariable=var_max_iter, width=15)
    #     entry_max_iter.grid(row=row, column=1, padx=5, pady=2)
    #     row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _create_camera_frame(self, notebook):
    #     """Create camera settings frame"""
    #     frame = ttk.Frame(notebook)
    #     notebook.add(frame, text="Camera Settings")
        
    #     # Create scrollable frame
    #     canvas = tk.Canvas(frame)
    #     scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
    #     scrollable_frame = ttk.Frame(canvas)
        
    #     scrollable_frame.bind(
    #         "<Configure>",
    #         lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    #     )
        
    #     canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    #     canvas.configure(yscrollcommand=scrollbar.set)
        
    #     # Camera settings
    #     camera = self.robot_config.get_camera_settings()
    #     row = 0
        
    #     # Title
    #     title = tk.Label(scrollable_frame, text="Camera Configuration", 
    #                     font=('Arial', 12, 'bold'))
    #     title.grid(row=row, column=0, columnspan=3, pady=(10, 20), sticky='w')
    #     row += 1
        
    #     camera_labels = {
    #         'camera_width': 'Camera Width',
    #         'camera_height': 'Camera Height',
    #         'camera_fps': 'Camera FPS'
    #     }
        
    #     for key, label in camera_labels.items():
    #         tk.Label(scrollable_frame, text=f"{label}:", width=20, anchor='w').grid(
    #             row=row, column=0, padx=5, pady=2, sticky='w'
    #         )
            
    #         var = tk.IntVar(value=camera[key])
    #         self.vars[f'{key}'] = var
            
    #         entry = tk.Entry(scrollable_frame, textvariable=var, width=15)
    #         entry.grid(row=row, column=1, padx=5, pady=2)
            
    #         row += 1
        
    #     canvas.pack(side="left", fill="both", expand=True)
    #     scrollbar.pack(side="right", fill="y")
    
    # def _validate_inputs(self) -> bool:
    #     """Validate all input values"""
    #     try:
    #         # Validate distance parameters
    #         for key in ['observation_distance', 'touch_distance', 'click_depth', 
    #                    'turn_depth']:
    #             value = self.vars[f'distance_{key}'].get()
    #             if not (0.001 <= value <= 1.0):
    #                 messagebox.showerror("Invalid Input", 
    #                                    f"Distance parameter '{key}' must be between 0.001 and 1.0 meters")
    #                 return False
            
    #         # Validate offset parameters
    #         for key in ['left_button_offsets', 'right_button_offsets', 'knob_offsets']:
    #             x_value = self.vars[f'offset_{key}_x'].get()
    #             y_value = self.vars[f'offset_{key}_y'].get()
    #             if not (-0.1 <= x_value <= 0.1) or not (-0.1 <= y_value <= 0.1):
    #                 messagebox.showerror("Invalid Input", 
    #                                    f"Offset parameter '{key}' values must be between -0.1 and 0.1 meters")
    #                 return False
            
    #         # Validate movement parameters
    #         if not (1 <= self.vars['movement_v'].get() <= 100):
    #             messagebox.showerror("Invalid Input", "Velocity must be between 1 and 100")
    #             return False
            
    #         if not (1 <= self.vars['movement_r'].get() <= 100):
    #             messagebox.showerror("Invalid Input", "Radius must be between 1 and 100")
    #             return False
            
    #         # Validate robot port
    #         if not (1 <= self.vars['robot_port'].get() <= 65535):
    #             messagebox.showerror("Invalid Input", "Port must be between 1 and 65535")
    #             return False
            
    #         # Validate camera settings
    #         if not (1 <= self.vars['camera_width'].get() <= 4096):
    #             messagebox.showerror("Invalid Input", "Camera width must be between 1 and 4096")
    #             return False
            
    #         if not (1 <= self.vars['camera_height'].get() <= 4096):
    #             messagebox.showerror("Invalid Input", "Camera height must be between 1 and 4096")
    #             return False
            
    #         if not (1 <= self.vars['camera_fps'].get() <= 120):
    #             messagebox.showerror("Invalid Input", "Camera FPS must be between 1 and 120")
    #             return False
            
    #         # Validate multi-frame count
    #         if not (1 <= self.vars['multi_frame_count'].get() <= 300):
    #             messagebox.showerror("Invalid Input", "Multi-frame count must be between 1 and 300")
    #             return False
            
    #         # Validate fine-tuning parameters
    #         if not (0.0001 <= self.vars['finetune_threshold'].get() <= 0.1):
    #             messagebox.showerror("Invalid Input", "Fine-tuning threshold must be between 0.0001 and 0.1 meters")
    #             return False
            
    #         if not (1 <= self.vars['finetune_max_iterations'].get() <= 100):
    #             messagebox.showerror("Invalid Input", "Fine-tuning max iterations must be between 1 and 100")
    #             return False
            
    #         # Validate ideal position coordinates (camera coordinates in meters)
    #         for prefix, name in [('left_button', 'Left Button'), ('right_button', 'Right Button'), ('knob_center', 'Knob Center'), ('knob_left', 'Knob Left'), ('knob_right', 'Knob Right')]:
    #             x_val = self.vars[f'{prefix}_ideal_x'].get()
    #             y_val = self.vars[f'{prefix}_ideal_y'].get()
    #             z_val = self.vars[f'{prefix}_ideal_d'].get()
                
    #             if not (-2.0 <= x_val <= 2.0):
    #                 messagebox.showerror("Invalid Input", f"{name} ideal X coordinate must be between -2.0 and 2.0 meters")
    #                 return False
                
    #             if not (-2.0 <= y_val <= 2.0):
    #                 messagebox.showerror("Invalid Input", f"{name} ideal Y coordinate must be between -2.0 and 2.0 meters")
    #                 return False
                
    #             if not (0.001 <= z_val <= 5.0):
    #                 messagebox.showerror("Invalid Input", f"{name} ideal Z coordinate must be between 0.001 and 5.0 meters")
    #                 return False
            
    #         return True
            
    #     except Exception as e:
    #         messagebox.showerror("Validation Error", f"Error validating inputs: {e}")
    #         return False
    
    def _update_config(self):
        """Update configuration with current values"""
        try:
            if not self._validate_inputs():
                return
            
            # Update distance parameters
            distance_updates = {}
            for key in ['observation_distance', 'touch_distance', 'click_depth', 
                       'turn_depth']:
                distance_updates[key] = self.vars[f'distance_{key}'].get()
            self.robot_config.update_distances(**distance_updates)
            
            # Update offset parameters
            offset_updates = {}
            for key in ['left_button_offsets', 'right_button_offsets', 'knob_offsets']:
                x_val = self.vars[f'offset_{key}_x'].get()
                y_val = self.vars[f'offset_{key}_y'].get()
                offset_updates[key] = (x_val, y_val)
            self.robot_config.update_offsets(**offset_updates)
            
            # Update movement parameters
            movement_updates = {
                'v': self.vars['movement_v'].get(),
                'r': self.vars['movement_r'].get(),
                'connect': self.vars['movement_connect'].get(),
                'block': self.vars['movement_block'].get()
            }
            self.robot_config.update_movement_parameters(**movement_updates)
            
            # Update robot settings
            robot_updates = {
                'robot_ip': self.vars['robot_ip'].get(),
                'robot_port': self.vars['robot_port'].get(),
                'arm_model': self.vars['arm_model'].get(),
                'force_type': self.vars['force_type'].get()
            }
            self.robot_config.update_robot_settings(**robot_updates)
            
            # Update processing settings
            processing_updates = {
                'use_robust_normal': self.vars['use_robust_normal'].get(),
                'multi_frame_count': self.vars['multi_frame_count'].get(),
                'target_button': self.vars['target_button'].get(),
                'left_button_ideal_xyz_cam': [
                    self.vars['left_button_ideal_x'].get(),
                    self.vars['left_button_ideal_y'].get(),
                    self.vars['left_button_ideal_d'].get()
                ],
                'right_button_ideal_xyz_cam': [
                    self.vars['right_button_ideal_x'].get(),
                    self.vars['right_button_ideal_y'].get(),
                    self.vars['right_button_ideal_d'].get()
                ],
                'knob_center_ideal_xyz_cam': [
                    self.vars['knob_center_ideal_x'].get(),
                    self.vars['knob_center_ideal_y'].get(),
                    self.vars['knob_center_ideal_d'].get()
                ],
                'knob_left_ideal_xyz_cam': [
                    self.vars['knob_left_ideal_x'].get(),
                    self.vars['knob_left_ideal_y'].get(),
                    self.vars['knob_left_ideal_d'].get()
                ],
                'knob_right_ideal_xyz_cam': [
                    self.vars['knob_right_ideal_x'].get(),
                    self.vars['knob_right_ideal_y'].get(),
                    self.vars['knob_right_ideal_d'].get()
                ],
                'finetune_threshold': self.vars['finetune_threshold'].get(),
                'finetune_max_iterations': self.vars['finetune_max_iterations'].get()
            }
            self.robot_config.update_processing_settings(**processing_updates)
            
            # Update camera settings
            camera_updates = {
                'camera_width': self.vars['camera_width'].get(),
                'camera_height': self.vars['camera_height'].get(),
                'camera_fps': self.vars['camera_fps'].get()
            }
            self.robot_config.update_camera_settings(**camera_updates)
            
            # # Show success message
            # messagebox.showinfo("Success", "Configuration updated successfully!")
            
            # Set status message if controller available
            if self.controller:
                self.controller.set_status_message(
                    "Configuration updated successfully", 
                    (0, 255, 0), 3000
                )
            
            print("Configuration updated via GUI panel")
            self.updated = True
            self.root.destroy()
            
        except Exception as e:
            # messagebox.showerror("Update Error", f"Error updating configuration: {e}")
            print(f"Error updating configuration: {e}")
    
    def _cancel_config(self):
        """Cancel configuration changes"""
        self.updated = False
        self.root.destroy()
    
    # def _reset_config(self):
    #     """Reset configuration to defaults"""
    #     result = messagebox.askyesno(
    #         "Reset Configuration", 
    #         "Are you sure you want to reset all parameters to default values?\n\nThis action cannot be undone."
    #     )
        
    #     if result:
    #         try:
    #             self.robot_config.reset_to_defaults()
    #             messagebox.showinfo("Success", "Configuration reset to defaults!")
                
    #             if self.controller:
    #                 self.controller.set_status_message(
    #                     "Configuration reset to defaults", 
    #                     (255, 191, 0), 3000
    #                 )
                
    #             print("Configuration reset to defaults via GUI panel")
    #             self.updated = True
    #             self.root.destroy()
                
    #         except Exception as e:
    #             messagebox.showerror("Reset Error", f"Error resetting configuration: {e}")


def show_config_panel(robot_config=None, controller=None):
    """
    Show the configuration panel
    
    Args:
        robot_config: RobotConfig instance (uses global if None)
        controller: Optional controller instance for status messages
        
    Returns:
        bool: True if configuration was updated, False otherwise
    """
    if robot_config is None:
        robot_config = get_config()
    
    try:
        panel = ConfigPanel(robot_config, controller)
        return panel.show_config_panel()
    except Exception as e:
        print(f"Error showing config panel: {e}")
        return False


def debug_adjust_offsets(controller):
    """
    Legacy function for backward compatibility with adjust.py imports
    Now shows the comprehensive config panel instead
    
    Args:
        controller: RealTimeButtonControl instance
    """
    try:
        return show_config_panel(controller.robot_config, controller)
    except Exception as e:
        print(f"Error during config panel display: {e}")
        return False


# Global configuration instance
# This allows other modules to import and use the same configuration
robot_config = RobotConfig()

# Convenience functions for easy access
def get_config() -> RobotConfig:
    """Get the global configuration instance"""
    return robot_config

def update_distances(**kwargs) -> Dict[str, float]:
    """Update distance parameters using the global config"""
    return robot_config.update_distances(**kwargs)

def update_offsets(**kwargs) -> Dict[str, Tuple[float, float]]:
    """Update offset parameters using the global config"""
    return robot_config.update_offsets(**kwargs)

def update_movement_parameters(**kwargs) -> Dict[str, Any]:
    """Update movement parameters using the global config"""
    return robot_config.update_movement_parameters(**kwargs)

def get_distances() -> Dict[str, float]:
    """Get distance parameters using the global config"""
    return robot_config.get_distances()

def get_offsets() -> Dict[str, Tuple[float, float]]:
    """Get offset parameters using the global config"""
    return robot_config.get_offsets()

def get_movement_parameters() -> Dict[str, Any]:
    """Get movement parameters using the global config"""
    return robot_config.get_movement_parameters() 