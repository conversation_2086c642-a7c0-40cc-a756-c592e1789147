"""
Coordinate transformation operations module for RoboArm project.
Handles transformations between different coordinate systems (camera, effector, base).
"""

import numpy as np
from typing import List, Tuple, Optional, Union, Any
from scipy.spatial.transform import Rotation as R

# Transformation matrices (from button_action.py)
# Camera to effector transformation matrix
camera_to_effector_rotation_matrix = np.array([
    [-0.03914598, -0.99869253, -0.03287572],
    [ 0.99923066, -0.0392032 ,  0.00109761],
    [-0.00238501, -0.03280746,  0.99945884]
])
camera_to_effector_translation_vector = np.array([
    0.08695189,
    -0.04231192,
    0.00636331
])

T_camera_to_effector = np.eye(4)
T_camera_to_effector[:3, :3] = camera_to_effector_rotation_matrix
T_camera_to_effector[:3, 3] = camera_to_effector_translation_vector


class CoordinateTransformer:
    """
    Coordinate transformation utilities for robot vision system.
    Handles transformations between camera, effector, and base coordinate systems.
    """
    
    def __init__(self):
        self.T_camera_to_effector = T_camera_to_effector
        
    def camera_to_base_transform(self, coords_camera: Union[List, Tuple, np.ndarray], 
                                current_pose: Optional[List] = None,
                                arm_interface: Any = None) -> Union[List, None]:
        """
        Transform coordinates from camera coordinate system to base coordinate system
        
        Args:
            coords_camera: coordinates in camera system (single point or list of points)
            current_pose: current arm pose (if None, will get current pose from arm_interface)
            arm_interface: robot arm interface object for getting current pose
            
        Returns:
            coordinates in base system (same format as input)
        """
        try:
            # Handle None input
            if coords_camera is None:
                return None
            
            # Get current pose if not provided
            if current_pose is None:
                if arm_interface is None:
                    print("Warning: No arm interface provided for coordinate transformation")
                    return coords_camera
                
                ret_msg = arm_interface.rm_get_arm_all_state()
                if ret_msg[0] != 0:
                    print(f"Warning: Cannot get arm state for coordinate transformation")
                    return coords_camera
                
                state = arm_interface.rm_get_current_arm_state()
                if state[0] != 0:
                    print(f"Warning: Cannot get current arm state for coordinate transformation")
                    return coords_camera
                
                current_pose = state[1]['pose']
            
            # Build effector to base transformation matrix
            position = current_pose[:3]
            orientation = R.from_euler('xyz', current_pose[3:], degrees=False).as_matrix()
            T_effector_to_base = np.eye(4)
            T_effector_to_base[:3, :3] = orientation
            T_effector_to_base[:3, 3] = position
            
            # Complete transformation: camera -> effector -> base
            T_camera_to_base = T_effector_to_base @ self.T_camera_to_effector
            
            # Handle different input formats more robustly
            single_point = False
            
            # Check if it's a single point (3D coordinate)
            if isinstance(coords_camera, (list, tuple, np.ndarray)):
                coords_array = np.array(coords_camera)
                
            # Single point: shape (3,) or list/tuple of 3 numbers
            if coords_array.ndim == 1 and len(coords_array) == 3:
                coords_list = [coords_camera]
                single_point = True
            # List of points: shape (N, 3) or list of 3D points
            elif coords_array.ndim == 2 and coords_array.shape[1] == 3:
                coords_list = coords_camera
            # Handle list of 3D points (each point is a list/tuple)
            elif all(isinstance(pt, (list, tuple, np.ndarray)) and len(pt) == 3 for pt in coords_camera if pt is not None):
                coords_list = coords_camera
            else:
                print(f"Warning: Invalid coordinate format: {coords_array.shape if hasattr(coords_array, 'shape') else type(coords_camera)}")
                return coords_camera
            
            # Transform each point
            transformed_coords = []
            for coord in coords_list:
                if coord is None:
                    transformed_coords.append(None)
                    continue
                
                # Ensure coord is a list/tuple/array with 3 elements
                try:
                    coord_array = np.array(coord)
                    if coord_array.size != 3:
                        print(f"Warning: Invalid coordinate size: {coord_array.size}, expected 3")
                        transformed_coords.append(None)
                        continue
                    
                    # Convert to homogeneous coordinates
                    coord_homo = np.array([coord_array[0], coord_array[1], coord_array[2], 1.0])
                
                    # Transform to base coordinates
                    coord_base_homo = T_camera_to_base @ coord_homo
                    
                    # Convert back to 3D coordinates
                    coord_base = coord_base_homo[:3].tolist()
                    transformed_coords.append(coord_base)
                    
                except (IndexError, TypeError, ValueError) as e:
                    print(f"Warning: Failed to transform coordinate {coord}: {e}")
                    transformed_coords.append(None)
            
            # Return in same format as input
            if single_point:
                return transformed_coords[0] if transformed_coords else None
            else:
                return transformed_coords
                
        except Exception as e:
            print(f"Warning: Coordinate transformation failed: {e}")
            return coords_camera  # Return original coordinates if transformation fails
    
    def robust_depth_estimation(self, depth_image: np.ndarray, 
                               bx: float, by: float, br: float, status: str = 'uncertain', 
                               debug: bool = False) -> Optional[float]:
        """
        Robust depth estimation using hollow circle mask based on button radius
        
        Args:
            depth_image: Depth image array
            bx, by: Button center coordinates
            br: Button radius
            status: Current system status to determine fallback strategy
            
        Returns:
            Estimated depth value or None if estimation fails
        """
        from config import get_config
        
        # Get configuration parameters
        config = get_config()
        processing_settings = config.get_processing_settings()
        outer_large_ratio = processing_settings['outer_large_radius_ratio']
        outer_small_ratio = processing_settings['outer_small_radius_ratio']
        inner_ratio = processing_settings['inner_radius_ratio']
        
        # Boundary check
        if not (0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]):
            return None
            
        # Single-point method as fallback
        single_point_depth = depth_image[int(by)][int(bx)]
        
        # Only proceed with advanced methods for buttons with reasonable radius
        if br <= 3:
            return single_point_depth if single_point_depth > 0 else None
        
        # Calculate hollow circle parameters
        outer_radius = outer_large_ratio * br
        inner_radius = outer_small_ratio * br
        
        # Create coordinate grids
        h, w = depth_image.shape
        y, x = np.ogrid[:h, :w]
        distances = np.sqrt((x - bx)**2 + (y - by)**2)
        
        # Create hollow circle mask
        hollow_mask = (distances <= outer_radius) & (distances >= inner_radius)
        
        # Check if hollow circle extends beyond image boundaries
        hollow_extends_beyond = (
            (bx - outer_radius < 0) or (bx + outer_radius >= w) or
            (by - outer_radius < 0) or (by + outer_radius >= h)
        )
        
        # Apply fallback strategy based on status and boundary conditions
        if hollow_extends_beyond:
            if status == 'uncertain':
                # For uncertain status, fallback to shrink circle method
                return self._fallback_shrink_circle_estimation(depth_image, bx, by, br, inner_ratio, single_point_depth)
            else:
                # For non-uncertain status, use available hollow circle data within bounds
                pass  # Continue with hollow circle processing
        
        # Extract depth values from hollow mask
        depth_values_hollow = depth_image[hollow_mask]
        valid_depths = depth_values_hollow[depth_values_hollow > 0]  # Remove invalid depths
        
        # Debug output for understanding the algorithm choice
        if debug:
            print(f"Hollow circle: outer_r={outer_radius:.1f}, inner_r={inner_radius:.1f}, valid_depths={len(valid_depths)}, status={status}")
        
        if len(valid_depths) >= 3:  # Need at least 3 valid points for reliable estimate
            # Use median to reduce noise (more robust than mean)
            depth_val = np.median(valid_depths)
            
            # Optional: Add outlier filtering using IQR for better robustness
            if len(valid_depths) >= 5:
                q1, q3 = np.percentile(valid_depths, [25, 75])
                iqr = q3 - q1
                if iqr > 0:  # Avoid division by zero
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    filtered_depths = valid_depths[(valid_depths >= lower_bound) & (valid_depths <= upper_bound)]
                    
                    if len(filtered_depths) >= 3:
                        depth_val = np.median(filtered_depths)
            
            if debug:
                print(f"Using hollow circle method: depth={depth_val:.1f}mm")
            return depth_val
        
        else:
            # Not enough valid points in hollow circle
            if debug:
                print(f"Insufficient valid points in hollow circle ({len(valid_depths)}), trying fallback...")
            
            if status == 'uncertain':
                # For uncertain status, try shrink circle method
                if debug:
                    print("Status is uncertain, falling back to shrink circle method")
                return self._fallback_shrink_circle_estimation(depth_image, bx, by, br, inner_ratio, single_point_depth)
            else:
                # For non-uncertain status, be more aggressive with hollow circle
                # Even if we have fewer than 3 points, use what we have if available
                if len(valid_depths) >= 1:
                    depth_val = np.median(valid_depths)
                    if debug:
                        print(f"Non-uncertain status: using available hollow circle data ({len(valid_depths)} points): depth={depth_val:.1f}mm")
                    return depth_val
                elif single_point_depth > 0:
                    if debug:
                        print(f"Non-uncertain status: fallback to single point: depth={single_point_depth}mm")
                    return single_point_depth
                else:
                    if debug:
                        print("Non-uncertain status: no valid depth data available")
                    return None
    
    def _fallback_shrink_circle_estimation(self, depth_image: np.ndarray, 
                                         bx: float, by: float, br: float, 
                                         inner_ratio: float, single_point_depth: float) -> Optional[float]:
        """
        Fallback method using shrink circle (original inner circle method)
        
        Args:
            depth_image: Depth image array
            bx, by: Button center coordinates
            br: Button radius
            inner_ratio: Ratio for inner circle (e.g., 0.6)
            single_point_depth: Single point depth as final fallback
            
        Returns:
            Estimated depth value or None if estimation fails
        """
        # Calculate shrink circle radius
        roi_radius = max(int(br * inner_ratio), 2)  # At least 2 pixels
        
        # Extract circular ROI around button center
        y_min = max(0, int(by) - roi_radius)
        y_max = min(depth_image.shape[0], int(by) + roi_radius + 1)
        x_min = max(0, int(bx) - roi_radius)
        x_max = min(depth_image.shape[1], int(bx) + roi_radius + 1)
        
        # Create circular mask for ROI
        roi_depth = depth_image[y_min:y_max, x_min:x_max]
        if roi_depth.size == 0:
            return single_point_depth if single_point_depth > 0 else None
            
        mask_h, mask_w = roi_depth.shape
        
        # Generate coordinate grids relative to ROI
        yy, xx = np.ogrid[:mask_h, :mask_w]
        center_y_roi = int(by) - y_min
        center_x_roi = int(bx) - x_min
        
        # Create circular mask
        circular_mask = (xx - center_x_roi)**2 + (yy - center_y_roi)**2 <= roi_radius**2
        
        # Extract valid depth values within the circular ROI
        roi_depths = roi_depth[circular_mask]
        valid_depths = roi_depths[roi_depths > 0]  # Filter out invalid depths
        
        if len(valid_depths) >= 3:  # Need at least 3 valid points for reliable estimate
            # Use median to reduce noise (more robust than mean)
            depth_val = np.median(valid_depths)
            
            # Optional: Add outlier filtering using IQR
            if len(valid_depths) >= 5:
                q1, q3 = np.percentile(valid_depths, [25, 75])
                iqr = q3 - q1
                if iqr > 0:  # Avoid division by zero
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    filtered_depths = valid_depths[(valid_depths >= lower_bound) & (valid_depths <= upper_bound)]
                    
                    if len(filtered_depths) >= 3:
                        depth_val = np.median(filtered_depths)
            
            return depth_val
        
        elif single_point_depth > 0:
            # Final fallback to single point
            return single_point_depth
        
        else:
            return None
    
    def visualize_depth_roi(self, image: np.ndarray, button: Tuple[float, float, float], 
                           depth_image: np.ndarray, status: str = 'uncertain', 
                           title: str = "Depth ROI Visualization") -> np.ndarray:
        """
        Visualize the hollow circle ROI used for robust depth estimation (for debugging)
        
        Args:
            image: RGB image to draw on
            button: Button tuple (bx, by, br)
            depth_image: Depth image
            status: Current system status to show appropriate visualization
            title: Window title
            
        Returns:
            Image with ROI visualization
        """
        import cv2
        from config import get_config
        
        bx, by, br = button
        vis_image = image.copy()
        
        if br > 3:
            # Get configuration parameters
            config = get_config()
            processing_settings = config.get_processing_settings()
            outer_large_ratio = processing_settings['outer_large_radius_ratio']
            outer_small_ratio = processing_settings['outer_small_radius_ratio']
            inner_ratio = processing_settings['inner_radius_ratio']
            
            # Calculate hollow circle parameters
            outer_radius = int(outer_large_ratio * br)
            inner_radius = int(outer_small_ratio * br)
            shrink_radius = max(int(br * inner_ratio), 2)
            
            # Draw detected button circle (yellow)
            cv2.circle(vis_image, (int(bx), int(by)), int(br), (0, 255, 255), 2, cv2.LINE_AA)
            cv2.putText(vis_image, f"Button r={br:.1f}", (int(bx) + int(br) + 5, int(by)), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
            
            # Check if hollow circle extends beyond boundaries
            h, w = depth_image.shape
            hollow_extends_beyond = (
                (bx - outer_radius < 0) or (bx + outer_radius >= w) or
                (by - outer_radius < 0) or (by + outer_radius >= h)
            )
            
            # Determine which method will be used and draw accordingly
            if hollow_extends_beyond and status == 'uncertain':
                # Will use shrink circle method - draw shrink circle (blue)
                cv2.circle(vis_image, (int(bx), int(by)), shrink_radius, (255, 0, 0), 2, cv2.LINE_AA)
                cv2.putText(vis_image, f"Shrink r={shrink_radius} (fallback)", (int(bx) + shrink_radius + 5, int(by) + 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)
            else:
                # Will use hollow circle method - draw hollow circle
                # Outer circle (green)
                cv2.circle(vis_image, (int(bx), int(by)), outer_radius, (0, 255, 0), 2, cv2.LINE_AA)
                cv2.putText(vis_image, f"Outer r={outer_radius}", (int(bx) + outer_radius + 5, int(by)), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                
                # Inner circle (red)
                cv2.circle(vis_image, (int(bx), int(by)), inner_radius, (0, 0, 255), 2, cv2.LINE_AA)
                cv2.putText(vis_image, f"Inner r={inner_radius}", (int(bx) + inner_radius + 5, int(by) + 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
                
                if hollow_extends_beyond:
                    cv2.putText(vis_image, "Beyond bounds", (int(bx) - 50, int(by) + 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 191, 0), 1)
            
            # Draw center point
            cv2.circle(vis_image, (int(bx), int(by)), 2, (255, 255, 255), -1)
            
            # Get depth values for visualization and determine method used
            single_point_depth = depth_image[int(by)][int(bx)] if (0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]) else 0
            
            # Analyze which method will be used
            h, w = depth_image.shape
            y, x = np.ogrid[:h, :w]
            distances = np.sqrt((x - bx)**2 + (y - by)**2)
            hollow_mask = (distances <= outer_radius) & (distances >= inner_radius)
            depth_values_hollow = depth_image[hollow_mask]
            valid_depths_hollow = depth_values_hollow[depth_values_hollow > 0]
            
            # Determine method actually used
            method_used = "Unknown"
            method_color = (128, 128, 128)
            
            if hollow_extends_beyond and status == 'uncertain':
                method_used = "Shrink Circle"
                method_color = (255, 0, 0)  # Blue
            elif len(valid_depths_hollow) >= 3:
                method_used = "Hollow Circle"
                method_color = (0, 255, 0)  # Green
            elif status == 'uncertain':
                method_used = "Shrink Circle"
                method_color = (255, 0, 0)  # Blue
            elif len(valid_depths_hollow) >= 1:
                method_used = "Hollow Circle (few pts)"
                method_color = (0, 255, 255)  # Yellow
            else:
                method_used = "Single Point"
                method_color = (255, 255, 255)  # White
            
            robust_depth = self.robust_depth_estimation(depth_image, bx, by, br, status, debug=False)
            
            # Display depth values and method
            cv2.putText(vis_image, f"Single: {single_point_depth}mm", (int(bx) - 80, int(by) - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            cv2.putText(vis_image, f"Result: {robust_depth:.1f}mm" if robust_depth else "Result: None", 
                       (int(bx) - 80, int(by) - 45), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 191, 0), 1)
            cv2.putText(vis_image, f"Method: {method_used}", (int(bx) - 80, int(by) - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, method_color, 1)
            cv2.putText(vis_image, f"Status: {status}", (int(bx) - 80, int(by) - 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
            cv2.putText(vis_image, f"Hollow pts: {len(valid_depths_hollow)}", (int(bx) - 80, int(by) + 0), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
            
            # Show improvement
            if robust_depth and single_point_depth > 0:
                diff = abs(robust_depth - single_point_depth)
                cv2.putText(vis_image, f"Diff: {diff:.1f}mm", (int(bx) - 80, int(by) + 15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        
        return vis_image
    
    def visualize_depth_roi_simple(self, image: np.ndarray, button: Tuple[float, float, float], 
                                  depth_image: np.ndarray, status: str = 'uncertain', 
                                  button_label: str = "") -> np.ndarray:
        """
        Simple visualization of depth ROI (no text overlay, for batch visualization)
        
        Args:
            image: RGB image to draw on
            button: Button tuple (bx, by, br)
            depth_image: Depth image
            status: Current system status
            button_label: Optional label for the button (e.g., "BL", "BR", "TL", "TR", "K")
            
        Returns:
            Image with ROI visualization (circles only)
        """
        import cv2
        from config import get_config
        
        bx, by, br = button
        vis_image = image.copy()
        
        if br > 3:
            # Get configuration parameters
            config = get_config()
            processing_settings = config.get_processing_settings()
            outer_large_ratio = processing_settings['outer_large_radius_ratio']
            outer_small_ratio = processing_settings['outer_small_radius_ratio']
            inner_ratio = processing_settings['inner_radius_ratio']
            
            # Calculate hollow circle parameters
            outer_radius = int(outer_large_ratio * br)
            inner_radius = int(outer_small_ratio * br)
            shrink_radius = max(int(br * inner_ratio), 2)
            
            # Check if hollow circle extends beyond boundaries
            h, w = depth_image.shape
            hollow_extends_beyond = (
                (bx - outer_radius < 0) or (bx + outer_radius >= w) or
                (by - outer_radius < 0) or (by + outer_radius >= h)
            )
            
            # Determine which method will be used and draw accordingly
            if hollow_extends_beyond and status == 'uncertain':
                # Will use shrink circle method - draw shrink circle (blue)
                cv2.circle(vis_image, (int(bx), int(by)), shrink_radius, (255, 0, 0), 2, cv2.LINE_AA)
                if button_label:
                    cv2.putText(vis_image, f"{button_label}:S", (int(bx) - 15, int(by) - shrink_radius - 5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
            else:
                # Will use hollow circle method - draw hollow circle
                # Outer circle (green) - thinner line for less clutter
                cv2.circle(vis_image, (int(bx), int(by)), outer_radius, (0, 255, 0), 1, cv2.LINE_AA)
                
                # Inner circle (red) - thinner line for less clutter
                cv2.circle(vis_image, (int(bx), int(by)), inner_radius, (0, 0, 255), 1, cv2.LINE_AA)
                
                if button_label:
                    cv2.putText(vis_image, f"{button_label}:H", (int(bx) - 15, int(by) - outer_radius - 5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            # Draw detected button circle (yellow) - thinner line
            cv2.circle(vis_image, (int(bx), int(by)), int(br), (0, 255, 255), 1, cv2.LINE_AA)
            
            # Draw center point (smaller)
            cv2.circle(vis_image, (int(bx), int(by)), 1, (255, 255, 255), -1)
        
        return vis_image


def decompose_transform(T: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    Decompose a 4x4 transformation matrix into position and rotation components
    
    Args:
        T: 4x4 transformation matrix
        
    Returns:
        Tuple of (position, rotation_matrix)
    """
    position = T[:3, 3]
    rotation = T[:3, :3]
    return position, rotation


def build_transformation_matrix(position: np.ndarray, rotation: np.ndarray) -> np.ndarray:
    """
    Build a 4x4 transformation matrix from position and rotation components
    
    Args:
        position: 3D position vector
        rotation: 3x3 rotation matrix
        
    Returns:
        4x4 transformation matrix
    """
    T = np.eye(4)
    T[:3, :3] = rotation
    T[:3, 3] = position
    return T


def euler_to_rotation_matrix(euler_angles: List[float], degrees: bool = False) -> np.ndarray:
    """
    Convert Euler angles to rotation matrix
    
    Args:
        euler_angles: List of [rx, ry, rz] euler angles
        degrees: Whether angles are in degrees (default: False for radians)
        
    Returns:
        3x3 rotation matrix
    """
    return R.from_euler('xyz', euler_angles, degrees=degrees).as_matrix()


def rotation_matrix_to_euler(rotation_matrix: np.ndarray, degrees: bool = False) -> np.ndarray:
    """
    Convert rotation matrix to Euler angles
    
    Args:
        rotation_matrix: 3x3 rotation matrix
        degrees: Whether to return angles in degrees (default: False for radians)
        
    Returns:
        Array of [rx, ry, rz] euler angles
    """
    return R.from_matrix(rotation_matrix).as_euler('xyz', degrees=degrees)


# Global coordinate transformer instance
coordinate_transformer = CoordinateTransformer()

# Convenience functions for easy access
def camera_to_base_transform(coords_camera: Union[List, Tuple, np.ndarray], 
                            current_pose: Optional[List] = None,
                            arm_interface: Any = None) -> Union[List, None]:
    """Transform coordinates from camera to base coordinate system using global transformer"""
    return coordinate_transformer.camera_to_base_transform(coords_camera, current_pose, arm_interface)

def robust_depth_estimation(depth_image: np.ndarray, 
                           bx: float, by: float, br: float, status: str = 'uncertain', 
                           debug: bool = False) -> Optional[float]:
    """Robust depth estimation using global transformer"""
    return coordinate_transformer.robust_depth_estimation(depth_image, bx, by, br, status, debug)

def visualize_depth_roi(image: np.ndarray, button: Tuple[float, float, float], 
                       depth_image: np.ndarray, status: str = 'uncertain',
                       title: str = "Depth ROI Visualization") -> np.ndarray:
    """Visualize depth ROI using global transformer"""
    return coordinate_transformer.visualize_depth_roi(image, button, depth_image, status, title)

def visualize_all_depth_rois(image: np.ndarray, button_data: Tuple, 
                            depth_image: np.ndarray, status: str = 'uncertain') -> np.ndarray:
    """
    Visualize depth ROI for all detected buttons and knob
    
    Args:
        image: RGB image to draw on
        button_data: Button detection results tuple (display_img, top_row, bottom_row, rotation_button, handle_angle, mode_code)
        depth_image: Depth image
        status: Current system status
        
    Returns:
        Image with all ROI visualizations
    """
    display_img, top_row, bottom_row, rotation_button, handle_angle, mode_code = button_data
    vis_image = image.copy()
    
    # Visualize bottom row buttons (highest priority)
    if bottom_row is not None:
        button_labels = ["BL", "BR"]  # Bottom Left, Bottom Right
        for i, button in enumerate(bottom_row):
            if button is not None:
                label = button_labels[i] if i < len(button_labels) else f"B{i}"
                vis_image = coordinate_transformer.visualize_depth_roi_simple(vis_image, button, depth_image, status, label)
    
    # Visualize top row buttons  
    if top_row is not None:
        button_labels = ["TL", "TR"]  # Top Left, Top Right
        for i, button in enumerate(top_row):
            if button is not None:
                label = button_labels[i] if i < len(button_labels) else f"T{i}"
                vis_image = coordinate_transformer.visualize_depth_roi_simple(vis_image, button, depth_image, status, label)
    
    # Visualize knob (if detected)
    if rotation_button is not None:
        vis_image = coordinate_transformer.visualize_depth_roi_simple(vis_image, rotation_button, depth_image, status, "K")
    
    return vis_image 