#!/usr/bin/env python3
"""
Data Collection and Persistence Module for Robotic Arm Button Control

This module handles all data collection, processing, and file I/O operations
including images, point clouds, coordinates, poses, and comprehensive data saving.

Author: Refactored from rtBC.py
"""

import numpy as np
import cv2
import pyrealsense2 as rs
import os
import csv
from datetime import datetime
from button_detection import detect_buttons_by_status
from button_action import build_object_coordinate_system


class DataManager:
    """
    Comprehensive data collection and persistence manager
    
    This class handles:
    - Real-time frame capture and alignment
    - Button/knob detection and coordinate extraction  
    - Point cloud generation
    - Multi-coordinate system support (camera/base)
    - File I/O for various data formats
    - CSV recording with comprehensive headers
    """
    
    def __init__(self, controller):
        """
        Initialize data manager

        Args:
            controller: RealTimeButtonControl instance for accessing hardware and parameters
        """
        self.controller = controller
        # Removed gauge_recording related attributes

        # 优化：预创建align对象避免重复创建
        import pyrealsense2 as rs
        self._align = rs.align(rs.stream.color)
    
    def collect_data(self, multi_frame=False, frames_count=None):
        """
        Collect all current data and return as comprehensive dictionary

        Args:
            multi_frame (bool): Whether to use multi-frame detection for stability
            frames_count (int): Number of frames to collect for multi-frame mode.
                               If None, uses value from robot_config.

        Returns:
            dict: Comprehensive data dictionary with all collected information
        """
        # Get frames_count from config if not provided
        if frames_count is None:
            processing_settings = self.controller.robot_config.get_processing_settings()
            frames_count = processing_settings.get('multi_frame_count', 10)
        if multi_frame:
            # print(f"Collecting multi-frame data ({frames_count} frames)...")
            
            # Import multiframe processor 
            try:
                from multiframe_processor import MultiFrameProcessor
                processor = MultiFrameProcessor(self.controller)
                
                # Progress tracking for GUI
                self.controller.multi_frame_progress = {'current': 0, 'total': frames_count, 'valid': 0}
                
                def progress_callback(current, total, valid):
                    self.controller.multi_frame_progress = {'current': current, 'total': total, 'valid': valid}
                
                # Perform multi-frame detection
                multi_result = processor.multi_frame_detection(frames_count, progress_callback)
                print('debug | number of buttons detected: ', len(multi_result['button_coords']))
                # if len(multi_result['button_coords']) < 4:
                #     self.controller._auto_save_debug_data("less_than_4_buttons_", "multi_frame_detection_failed")

                
                # Convert multi-frame result to standard format
                data = self._format_multi_frame_data(multi_result)
                
                # Clear progress
                self.controller.multi_frame_progress = None
                
            except ImportError:
                # print("Warning: multiframe_processor not available, falling back to single frame")
                return self.collect_data(multi_frame=False)
        else:
            # print("Collecting single-frame data...")
            data = self._collect_single_frame_data()
        
        # Add coordinate system transformations
        data = self._add_coordinate_transformations(data)
        
        # Add stability metrics
        data = self._add_stability_metrics(data, multi_frame)
        
        return data
    
    def _collect_single_frame_data(self):
        """Collect data from a single frame"""
        
        color_image = self.controller.camera_instance.color_image.copy() if self.controller.camera_instance.color_image is not None else None
        depth_image = self.controller.camera_instance.depth_image.copy() if self.controller.camera_instance.depth_image is not None else None
        
        # Get intrinsics from the current aligned frames
        color_intrin = self.controller.camera_instance.color_intrin if self.controller.camera_instance.color_intrin is not None else None
        depth_intrin = self.controller.camera_instance.depth_intrin if self.controller.camera_instance.depth_intrin is not None else None
        
        
        # 使用5次检测循环提高数据收集可靠性
        from button_detection import detect_buttons_by_status

        max_detection_attempts = 5
        detection_results = []
        successful_detections = 0

        for attempt in range(max_detection_attempts):
            # 获取新的图像帧
            color_image = self.controller.camera_instance.color_image.copy() if self.controller.camera_instance.color_image is not None else None
            depth_image = self.controller.camera_instance.depth_image.copy() if self.controller.camera_instance.depth_image is not None else None

            if color_image is None or depth_image is None:
                continue

            button_data = detect_buttons_by_status(
                color_image,
                status=self.controller.status,
                verbose=False,  # 减少输出以提高性能
                display_process=False
            )

            # 评估检测质量
            quality_score = 0
            if isinstance(button_data, dict):
                # 单目标检测结果
                if button_data.get('target') is not None:
                    quality_score = button_data.get('confidence', 0)
            else:
                # 完整检测结果
                if button_data and len(button_data) >= 6:
                    _, top_row, bottom_row, knob, handle_angle, _ = button_data
                    quality_score = (len(top_row) if top_row else 0) + (len(bottom_row) if bottom_row else 0) + (1 if knob else 0)

            if quality_score > 0:
                detection_results.append({
                    'button_data': button_data,
                    'color_image': color_image,
                    'depth_image': depth_image,
                    'quality_score': quality_score
                })
                successful_detections += 1

            # 短暂延迟获取不同帧
            if attempt < max_detection_attempts - 1:
                import time
                time.sleep(0.05)

        # 判断检测成功：5次中至少1次成功
        if successful_detections < 1:
            print(f"Data collection detection failed: only {successful_detections}/5 successful, below threshold 1")
            # Auto-save debug data when detection fails
            if hasattr(self.controller, '_auto_save_debug_data'):
                self.controller._auto_save_debug_data("all_single_frame_detection_failed_", "all_single_frame_detection_failed")
            # 使用最后一次的图像作为备选
            button_data = None
        else:
            # 选择质量最高的检测结果
            best_result = max(detection_results, key=lambda x: x['quality_score'])
            button_data = best_result['button_data']
            color_image = best_result['color_image']
            depth_image = best_result['depth_image']
            print(f"Data collection detection successful: {successful_detections}/{max_detection_attempts} successful, selected best result (quality score: {best_result['quality_score']})")
        
        data = {
            'color_image': color_image.copy(),
            'depth_image': depth_image.copy(),
            'button_data': button_data,
            'button_coords_camera': [],  # Camera coordinate system
            'button_labels': [],
            'knob_coord_camera': None,   # Camera coordinate system
            'knob_angle': None,
            'point_cloud': None,
            'pose': None,
            'joint': None,
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'color_intrin': color_intrin,
            'depth_intrin': depth_intrin
        }
        
        # Generate point cloud using CORRECT aligned intrinsics
        data['point_cloud'] = self._generate_point_cloud(data['depth_image'], color_intrin)
        
        # Get current arm state
        data['pose'], data['joint'] = self._get_arm_state()
        
        # Convert button coordinates to 3D using CORRECT aligned intrinsics
        self._extract_button_coordinates(data, color_intrin)
        
        return data
    
    def _extract_single_target_coordinates(self, data, color_intrin):
        """Extract coordinates from single target detection (touching_* status)"""
        button_result = data['button_data']
        target = button_result.get('target')
        target_type = button_result.get('target_type')
        confidence = button_result.get('confidence', 0.0)
        
        if target is None:
            print(f"No {target_type} detected in single target mode")
            # Ensure we have empty lists for consistency
            data['button_coords_camera'] = []
            data['button_labels'] = []
            data['knob_coord_camera'] = None
            return
        
        # Convert 2D detection result to 3D coordinates
        def convert_button_to_3d(button):
            bx, by, br = button
            # Use robust depth estimation with circular ROI
            depth_val = self.controller._robust_depth_estimation(data['depth_image'], bx, by, br)
            
            if depth_val is not None and depth_val > 0:  # Valid depth
                point_3d = rs.rs2_deproject_pixel_to_point(
                    color_intrin,  # Use COLOR intrinsics for aligned depth
                    (float(bx), float(by)),
                    depth_val / 1000.0  # Convert from mm to meters
                )
                return point_3d
            return None
        
        # Convert to 3D coordinates
        target_3d = convert_button_to_3d(target)
        
        if target_3d is None:
            print(f"Failed to convert {target_type} to 3D coordinates")
            # Ensure we have empty lists for consistency
            data['button_coords_camera'] = []
            data['button_labels'] = []
            data['knob_coord_camera'] = None
            return
        
        if target_type in ['left_button', 'right_button']:
            # For button targets, store in button_coords_camera
            data['button_coords_camera'] = [target_3d]
            data['button_labels'] = [target_type]
            print(f"Single {target_type} detected at 2D: {target}, 3D: {target_3d} (confidence: {confidence:.3f})")
            
        elif target_type == 'knob':
            # For knob target, store in knob_coord_camera
            data['knob_coord_camera'] = target_3d
            print(f"Single knob detected at 2D: {target}, 3D: {target_3d} (confidence: {confidence:.3f})")
        
        # Note: Single target mode doesn't provide angle information
        # The angle detection would need to be done separately if needed
    
    def _format_multi_frame_data(self, multi_result):
        """Format multi-frame processor result to standard data format"""
        print('debug | button_data shape before format: ', len(multi_result['button_coords']))
        for coord, label in zip(multi_result['button_coords'], multi_result['button_labels']):
            print('debug | coord and label: ', coord, label)

        try:
            # Safe filtering to avoid index out of range errors
            button_coords_filtered = []
            button_labels_filtered = []
            
            # Ensure both lists have the same length
            min_length = min(len(multi_result['button_coords']), len(multi_result['button_labels']))
            
            for i in range(min_length):
                if multi_result['button_coords'][i] is not None:
                    button_coords_filtered.append(multi_result['button_coords'][i])
                    button_labels_filtered.append(multi_result['button_labels'][i])

            data = {
                'color_image': multi_result['color_image'],
                'depth_image': multi_result['depth_image'],
                'button_data': None,  # Not used in multi-frame mode
                'button_coords_camera': button_coords_filtered,
                'button_labels': button_labels_filtered,
                'knob_coord_camera': multi_result['knob_coord'],
                'knob_angle': multi_result['knob_angle'],
                'point_cloud': None,  # Will be generated from last frame
                'pose': None,
                'joint': None,
                'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
                'color_intrin': multi_result['color_intrin'],
                'depth_intrin': None,  # Use color_intrin for aligned frames
                'stability_metrics': multi_result['stability_metrics'],
                'is_multi_frame': True
            }
            print('debug | button_data shape after format: ', len(data['button_coords_camera']))
            if len(data['button_coords_camera']) < 4:
                self.controller._auto_save_debug_data("less_than_4_buttons_", "find_less_than_4_buttons")
            # Generate point cloud from last frame (for multi-frame mode)
            if data['depth_image'] is not None and data['color_intrin'] is not None:
                data['point_cloud'] = self._generate_point_cloud(data['depth_image'], data['color_intrin'])
            
            # Get current arm state (for multi-frame mode)
            data['pose'], data['joint'] = self._get_arm_state()
            
            return data
            
        except Exception as e:
            print(f"Error in _format_multi_frame_data: {e}")
            # Auto-save debug data when formatting fails
            self.controller._auto_save_debug_data("format_multi_frame_data_error_", f"format_multi_frame_data_error_{str(e)}")
            raise
    
    def _generate_point_cloud(self, depth_image, color_intrin):
        """Generate point cloud from depth image and intrinsics"""
        if depth_image is None or color_intrin is None:
            return None
            
        pcd_points = []
        # More efficient point cloud generation using COLOR intrinsics for aligned depth
        for v in range(0, color_intrin.height, 2):
            for u in range(0, color_intrin.width, 2):
                depth_value = depth_image[v, u] / 1000.0
                if 0.1 < depth_value < 3.0:
                    point = rs.rs2_deproject_pixel_to_point(color_intrin, [u, v], depth_value)
                    pcd_points.append(point)
        
        if pcd_points:
            # print(f"Generated point cloud: {len(pcd_points)} points")
            return np.array(pcd_points)
        else:
            return None
    
    def _get_arm_state(self):
        """Get current arm pose and joint angles"""
        try:
            ret_msg = self.controller.arm.rm_get_arm_all_state()
            if ret_msg[0] == 0:
                state = self.controller.arm.rm_get_current_arm_state()
                if state[0] == 0:
                    return state[1]['pose'], state[1]['joint']
        except Exception as e:
            # print(f"Warning: Could not get arm state: {e}")
            pass
        
        return None, None
    
    def _extract_button_coordinates(self, data, color_intrin):
        """Extract 3D button coordinates from detection data"""
        if data['button_data'] is None or data['depth_image'] is None:
            return
        
        # Handle different button_data formats from status-aware detection
        if isinstance(data['button_data'], dict):
            # Single target detection result from touching_* status
            self._extract_single_target_coordinates(data, color_intrin)
            return
        else:
            # Full detection result from non-touching_* status
            display_img, top_row, bottom_row, rotation_button, handle_angle, mode_code = data['button_data']
        
        def convert_button_to_3d(button):
            bx, by, br = button
            # Use robust depth estimation with circular ROI
            depth_val = self.controller._robust_depth_estimation(data['depth_image'], bx, by, br)
            
            if depth_val is not None and depth_val > 0:  # Valid depth
                point_3d = rs.rs2_deproject_pixel_to_point(
                    color_intrin,  # Use COLOR intrinsics for aligned depth
                    (float(bx), float(by)),
                    depth_val / 1000.0  # Convert from mm to meters
                )
                return point_3d
            return None
        
        # Collect button coordinates in the same order as on_image.py
        # Order: bottom_left, bottom_right, top_left, top_right, rotation
        button_mapping = []
        
        # Parse detected buttons into consistent order
        if bottom_row is not None and len(bottom_row) >= 2:
            button_mapping.extend([
                (bottom_row[0], "bottom_left"),   # bottom_left
                (bottom_row[1], "bottom_right")   # bottom_right
            ])
        
        if top_row is not None and len(top_row) >= 2:
            button_mapping.extend([
                (top_row[0], "top_left"),         # top_left
                (top_row[1], "top_right")         # top_right
            ])
        
        # Convert button coordinates to 3D in the correct order
        for button, label in button_mapping:
            point_3d = convert_button_to_3d(button)
            if point_3d is not None:
                data['button_coords_camera'].append(point_3d)
                data['button_labels'].append(label)
        
        # Handle knob (rotation button) separately
        if rotation_button is not None:
            knob_3d = convert_button_to_3d(rotation_button)
            if knob_3d is not None:
                data['knob_coord_camera'] = knob_3d
                # print(f"Detected knob coordinate: {knob_3d}")
        
        # Store knob angle
        if handle_angle is not None:
            data['knob_angle'] = handle_angle
            # print(f"Detected knob angle: {handle_angle:.1f}°")
        
        # print(f"Detected buttons: {len(data['button_coords_camera'])}")
    
    def _add_coordinate_transformations(self, data):
        """Add base coordinate system transformations"""
        # Transform coordinates to base system
        data['button_coords_base'] = self.controller.camera_to_base_transform(
            data['button_coords_camera'], data['pose']
        )
        data['knob_coord_base'] = self.controller.camera_to_base_transform(
            data['knob_coord_camera'], data['pose']
        ) if data['knob_coord_camera'] else None
        
        # For backward compatibility, keep original field names pointing to camera coordinates
        data['button_coords'] = data['button_coords_camera']
        data['knob_coord'] = data['knob_coord_camera']
        
        return data
    
    def _add_stability_metrics(self, data, is_multi_frame):
        """Add stability metrics to data"""
        if is_multi_frame and 'stability_metrics' in data:
            # Multi-frame already has stability metrics
            data['is_multi_frame'] = True
        else:
            # Add stability metrics for single-frame (for consistency)
            data['stability_metrics'] = {
                'button_detection_rate': 1.0 if len(data['button_coords_camera']) >= 4 else 0.0,
                'knob_detection_rate': 1.0 if data['knob_coord_camera'] is not None else 0.0,
                'angle_detection_rate': 1.0 if data['knob_angle'] is not None else 0.0,
                'button_std': [0.0] * len(data['button_coords_camera']) if data['button_coords_camera'] else [],
                'knob_std': 0.0 if data['knob_coord_camera'] is not None else float('inf'),
                'angle_std': 0.0 if data['knob_angle'] is not None else float('inf')
            }
            data['is_multi_frame'] = False
        
        return data
    
    def save_data(self, prefix=""):
        """Save collected data with optional prefix"""
        data = self.collect_data()
        if data is None:
            # print("Warning: Unable to get valid data, skipping save")
            return None
            
        # Use the enhanced save_collected_data method which handles both coordinate systems
        return self.save_collected_data(data, prefix)
    
    def save_collected_data(self, data, prefix=""):
        """Save already collected data with optional prefix"""
        # Use timestamped directory for regular saves
        save_dir = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H")}'

        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        timestamp = data['timestamp']
        
        # Save images
        self._save_images(data, save_dir, prefix, timestamp)
        
        # Save coordinates (both camera and base systems)
        self._save_coordinates(data, save_dir, prefix, timestamp)
        
        # Save knob data
        self._save_knob_data(data, save_dir, prefix, timestamp)
        
        # # Save point cloud
        # self._save_point_cloud(data, save_dir, prefix, timestamp)
        
        # Save arm state
        self._save_arm_state(data, save_dir, prefix, timestamp)
        
        # Save metadata
        self._save_metadata(data, save_dir, prefix, timestamp)
        
        return data
    
    def _save_images(self, data, save_dir, prefix, timestamp):
        """Save color and depth images"""
        if data['color_image'] is not None:
            cv2.imwrite(f"{save_dir}/{prefix}colorImage_{timestamp}.png", data['color_image'])
            # print(f"Saved {prefix}colorImage_{timestamp}.png")
            
        if data['depth_image'] is not None:
            cv2.imwrite(f"{save_dir}/{prefix}depthImage_{timestamp}.png", data['depth_image'])
            # print(f"Saved {prefix}depthImage_{timestamp}.png")
    
    def _save_coordinates(self, data, save_dir, prefix, timestamp):
        """Save button coordinates in both camera and base coordinate systems"""
        # Save button coordinates in camera coordinate system
        if data.get('button_coords_camera'):
            coords_array = np.array(data['button_coords_camera'])
            header = 'x_camera,y_camera,z_camera'
            np.savetxt(f"{save_dir}/{prefix}buttonCoords_camera_{timestamp}.csv", coords_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}buttonCoords_camera_{timestamp}.csv with {len(data['button_coords_camera'])} buttons")
        else:
            # Save empty file to indicate no buttons detected
            with open(f"{save_dir}/{prefix}buttonCoords_camera_{timestamp}.csv", 'w') as f:
                f.write('x_camera,y_camera,z_camera\n')
            # print(f"Saved {prefix}buttonCoords_camera_{timestamp}.csv (no buttons detected)")
        
        # Save button coordinates in base coordinate system
        if data.get('button_coords_base') and any(coord is not None for coord in data['button_coords_base']):
            # Filter out None values
            valid_coords = [coord for coord in data['button_coords_base'] if coord is not None]
            if valid_coords:
                coords_array = np.array(valid_coords)
                header = 'x_base,y_base,z_base'
                np.savetxt(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", coords_array, 
                          delimiter=',', header=header, comments='', fmt='%.6f')
                # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv with {len(valid_coords)} buttons")
            else:
                with open(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", 'w') as f:
                    f.write('x_base,y_base,z_base\n')
                # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv (no valid coordinates)")
        else:
            # Save empty file to indicate no buttons detected in base coordinates
            with open(f"{save_dir}/{prefix}buttonCoords_base_{timestamp}.csv", 'w') as f:
                f.write('x_base,y_base,z_base\n')
            # print(f"Saved {prefix}buttonCoords_base_{timestamp}.csv (no buttons detected)")
    
    def _save_knob_data(self, data, save_dir, prefix, timestamp):
        """Save knob coordinates and angle data"""
        # Save knob coordinate in camera coordinate system
        if data.get('knob_coord_camera') is not None:
            knob_array = np.array(data['knob_coord_camera']).reshape(1, -1)
            header = 'x_camera,y_camera,z_camera'
            np.savetxt(f"{save_dir}/{prefix}knobCoord_camera_{timestamp}.csv", knob_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}knobCoord_camera_{timestamp}.csv")
        else:
            # Save empty file to indicate no knob detected
            with open(f"{save_dir}/{prefix}knobCoord_camera_{timestamp}.csv", 'w') as f:
                f.write('x_camera,y_camera,z_camera\n')
            # print(f"Saved {prefix}knobCoord_camera_{timestamp}.csv (no knob detected)")
        
        # Save knob coordinate in base coordinate system
        if data.get('knob_coord_base') is not None:
            knob_array = np.array(data['knob_coord_base']).reshape(1, -1)
            header = 'x_base,y_base,z_base'
            np.savetxt(f"{save_dir}/{prefix}knobCoord_base_{timestamp}.csv", knob_array, 
                      delimiter=',', header=header, comments='', fmt='%.6f')
            # print(f"Saved {prefix}knobCoord_base_{timestamp}.csv")
        else:
            # Save empty file to indicate no knob detected in base coordinates
            with open(f"{save_dir}/{prefix}knobCoord_base_{timestamp}.csv", 'w') as f:
                f.write('x_base,y_base,z_base\n')
            # print(f"Saved {prefix}knobCoord_base_{timestamp}.csv (no knob detected)")
        
        # Save knob angle
        if data['knob_angle'] is not None:
            with open(f"{save_dir}/{prefix}knobAngle_{timestamp}.txt", 'w') as f:
                f.write(f"{data['knob_angle']:.6f}\n")
            # print(f"Saved {prefix}knobAngle_{timestamp}.txt with angle {data['knob_angle']:.1f}°")
        else:
            with open(f"{save_dir}/{prefix}knobAngle_{timestamp}.txt", 'w') as f:
                f.write("None\n")
            # print(f"Saved {prefix}knobAngle_{timestamp}.txt (no angle detected)")
    
    # def _save_point_cloud(self, data, save_dir, prefix, timestamp):
    #     """Save point cloud data"""
    #     if data['point_cloud'] is not None:
    #         pcd = o3d.geometry.PointCloud()
    #         pcd.points = o3d.utility.Vector3dVector(data['point_cloud'])
    #         o3d.io.write_point_cloud(f"{save_dir}/{prefix}pointCloud_{timestamp}.pcd", pcd)
    #         # print(f"Saved {prefix}pointCloud_{timestamp}.pcd with {len(data['point_cloud'])} points")
    
    def _save_arm_state(self, data, save_dir, prefix, timestamp):
        """Save arm pose and joint angles"""
        if data['pose'] is not None:
            np.savetxt(f"{save_dir}/{prefix}pose_{timestamp}.txt", data['pose'], fmt='%.6f')
            # print(f"Saved {prefix}pose_{timestamp}.txt")
            
        if data['joint'] is not None:
            np.savetxt(f"{save_dir}/{prefix}joint_{timestamp}.txt", data['joint'], fmt='%.6f')
            # print(f"Saved {prefix}joint_{timestamp}.txt")
    
    def _save_metadata(self, data, save_dir, prefix, timestamp):
        """Save coordinate system information and stability metrics"""
        # Save coordinate system information
        coord_info = {
            'has_camera_coords': data.get('button_coords_camera') is not None,
            'has_base_coords': data.get('button_coords_base') is not None,
            'transformation_successful': data.get('button_coords_base') is not None and data['pose'] is not None
        }
        
        with open(f"{save_dir}/{prefix}coordinate_system_info_{timestamp}.txt", 'w') as f:
            for key, value in coord_info.items():
                f.write(f"{key}: {value}\n")
        # print(f"Saved {prefix}coordinate_system_info_{timestamp}.txt")
        
        # Save stability metrics for multi-frame data
        if data.get('is_multi_frame', False):
            with open(f"{save_dir}/{prefix}stability_{timestamp}.txt", 'w') as f:
                metrics = data['stability_metrics']
                f.write(f"Button detection rate: {metrics.get('button_detection_rate', 0):.3f}\n")
                f.write(f"Knob detection rate: {metrics.get('knob_detection_rate', 0):.3f}\n")
                f.write(f"Angle detection rate: {metrics.get('angle_detection_rate', 0):.3f}\n")
                f.write(f"Button stability (std): {metrics.get('button_std', [])}\n")
                f.write(f"Knob stability (std): {metrics.get('knob_std', float('inf')):.6f}\n")
                f.write(f"Angle stability (std): {metrics.get('angle_std', float('inf')):.6f}\n")
            # print(f"Saved {prefix}stability_{timestamp}.txt")
    
    # CSV Recording functionality
    # Removed start_gauge_recording, stop_gauge_recording, _get_csv_header


if __name__ == "__main__":
    # Test the module independently
    print("Testing DataManager...")
    print("Note: This module requires a RealTimeButtonControl instance to function properly")
    print("Use within the main rtBC.py system for full functionality") 