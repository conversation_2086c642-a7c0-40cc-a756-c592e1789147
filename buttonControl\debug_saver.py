import cv2
import numpy as np
import os
from pathlib import Path
import json
from datetime import datetime

class DebugSaver:
    """
    Class to save intermediate detection results for debugging purposes
    """
    
    def __init__(self, save_dir="debug_results", enabled=True):
        """
        Initialize the debug saver
        
        Args:
            save_dir: Directory to save debug results
            enabled: Whether to enable saving (can be controlled via environment variable)
        """
        self.enabled = enabled and os.getenv('BUTTON_DEBUG_SAVE', 'False').lower() == 'true'
        self.save_dir = Path(save_dir)
        self.current_session = None
        
        if self.enabled:
            # Create save directory if it doesn't exist
            self.save_dir.mkdir(parents=True, exist_ok=True)
    
    def start_session(self, session_name=None):
        """
        Start a new debug session
        
        Args:
            session_name: Name for this session, if None will use timestamp
        """
        if not self.enabled:
            return
            
        if session_name is None:
            session_name = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        self.current_session = session_name
        self.session_dir = self.save_dir / session_name
        self.session_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for different types of results
        (self.session_dir / "masks").mkdir(exist_ok=True)
        (self.session_dir / "processed").mkdir(exist_ok=True)
        (self.session_dir / "intermediate").mkdir(exist_ok=True)
    
    def save_image(self, image, filename, subdir=""):
        """
        Save an image to the current session directory
        
        Args:
            image: Image to save (numpy array)
            filename: Filename (without extension)
            subdir: Subdirectory within session directory
        """
        if not self.enabled or self.current_session is None:
            return
        
        save_path = self.session_dir
        if subdir:
            save_path = save_path / subdir
            save_path.mkdir(exist_ok=True)
        
        # Add .png extension if not present
        if not filename.endswith(('.png', '.jpg', '.jpeg')):
            filename += '.png'
        
        full_path = save_path / filename
        cv2.imwrite(str(full_path), image)
    
    def save_mask(self, mask, name):
        """
        Save a mask image
        
        Args:
            mask: Binary mask (numpy array)
            name: Name of the mask
        """
        self.save_image(mask, f"{name}_mask", "masks")
    
    def save_processed(self, image, name):
        """
        Save a processed image
        
        Args:
            image: Processed image
            name: Name of the processed image
        """
        self.save_image(image, name, "processed")
    
    def save_intermediate(self, image, name):
        """
        Save an intermediate result image
        
        Args:
            image: Intermediate result image
            name: Name of the intermediate result
        """
        self.save_image(image, name, "intermediate")
    
    def save_data(self, data, filename):
        """
        Save data as JSON
        
        Args:
            data: Data to save (must be JSON serializable)
            filename: Filename (without extension)
        """
        if not self.enabled or self.current_session is None:
            return
        
        if not filename.endswith('.json'):
            filename += '.json'
        
        full_path = self.session_dir / filename
        
        # Convert numpy types to Python types for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, tuple):
                return list(obj)
            return obj
        
        # Recursively convert numpy types
        def deep_convert(obj):
            if isinstance(obj, dict):
                return {k: deep_convert(v) for k, v in obj.items()}
            elif isinstance(obj, (list, tuple)):
                return [deep_convert(item) for item in obj]
            else:
                return convert_numpy(obj)
        
        converted_data = deep_convert(data)
        
        with open(full_path, 'w') as f:
            json.dump(converted_data, f, indent=2)
    
    def end_session(self):
        """
        End the current debug session
        """
        if not self.enabled:
            return
            
        self.current_session = None
        self.session_dir = None

# Global debug saver instance
debug_saver = DebugSaver()