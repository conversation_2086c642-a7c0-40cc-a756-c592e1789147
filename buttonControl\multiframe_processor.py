#!/usr/bin/env python3
"""
Multi-Frame Detection Processing Module for Robotic Arm Button Control

This module handles multi-frame detection, aggregation, outlier filtering,
and stability analysis for improved robotic arm button detection accuracy.

Author: Refactored from rtBC.py
"""

import numpy as np
import cv2
import pyrealsense2 as rs
from button_detection import detect_buttons_by_status
from datetime import datetime


class MultiFrameProcessor:
    """
    Multi-frame detection processor with outlier filtering and stability analysis
    
    This class provides:
    - Multi-frame button and knob detection
    - Statistical outlier filtering using IQR method
    - Circular statistics for angle processing
    - Stability metrics calculation
    - Robust depth estimation integration
    """
    
    def __init__(self, controller):
        """
        Initialize multi-frame processor
        
        Args:
            controller: RealTimeButtonControl instance for accessing hardware and parameters
        """
        self.controller = controller
    
    def multi_frame_detection(self, frames_count=None, progress_callback=None):
        """
        Multi-frame button and knob detection with outlier filtering

        Args:
            frames_count (int): Number of frames to collect and process.
                               If None, uses value from robot_config.
            progress_callback (callable): Optional callback for progress reporting
                signature: callback(current_frame, total_frames, valid_frames)

        Returns:
            dict: Processed multi-frame detection result
        """
        # Get frames_count from config if not provided
        if frames_count is None:
            processing_settings = self.controller.robot_config.get_processing_settings()
            frames_count = processing_settings.get('multi_frame_count', 10)
        # print(f"Starting multi-frame detection: {frames_count} frames...")
        
        # Storage for detection results
        button_detections = []
        knob_detections = []
        angle_detections = []
        valid_frame_count = 0
        
        # Last frame data for final result
        last_frame_data = {}
        
        # Collect frames and detections
        for i in range(frames_count):
            try:
                color_image = self.controller.camera_instance.color_image.copy() if self.controller.camera_instance.color_image is not None else None
                depth_image = self.controller.camera_instance.depth_image.copy() if self.controller.camera_instance.depth_image is not None else None
                
                # Get intrinsics
                color_intrin = self.controller.camera_instance.color_intrin if self.controller.camera_instance.color_intrin is not None else None
                
                # Detect buttons on current frame using status-aware detection
                from button_detection import detect_buttons_by_status
                button_data = detect_buttons_by_status(
                    color_image, 
                    status=self.controller.status,
                    verbose=False, 
                    display_process=False
                )
                
                # Store frame detection results
                frame_result = {
                    'frame_idx': i,
                    'buttons': [],
                    'knob': None,
                    'angle': None
                }
                
                # Handle different button_data formats from status-aware detection
                if isinstance(button_data, dict):
                    # Single target detection result from touching_* status
                    self._process_single_target_frame(button_data, frame_result, depth_image, color_intrin)
                else:
                    # Full detection result from non-touching_* status
                    display_img, top_row, bottom_row, rotation_button, handle_angle, mode_code = button_data
                    self._process_full_detection_frame(frame_result, top_row, bottom_row, rotation_button, handle_angle, depth_image, color_intrin)
                
                # Store last frame data
                if i == frames_count - 1:
                    last_frame_data = {
                        'color_image': color_image,
                        'depth_image': depth_image,
                        'color_intrin': color_intrin
                    }
                
                valid_frame_count += 1
                
                # Report progress
                if progress_callback:
                    progress_callback(i + 1, frames_count, valid_frame_count)
                    
            except Exception as e:
                # print(f"Frame {i} detection failed: {e}")
                continue
        
        print(f"Collected {valid_frame_count} valid frames")
        
        # Use appropriate detection lists based on mode
        if hasattr(self, '_single_target_detections') and len(self._single_target_detections) > 0:
            # Single button target mode
            button_detections = [[target] for target in self._single_target_detections]
            # print(f"Single button target detections: {len(self._single_target_detections)}")
        elif hasattr(self, '_button_detections'):
            # Full detection mode
            button_detections = self._button_detections
            # print(f"Button detections: {len(button_detections)}")
        
        if hasattr(self, '_single_knob_detections') and len(self._single_knob_detections) > 0:
            # Single knob target mode
            knob_detections = self._single_knob_detections
            # print(f"Single knob target detections: {len(self._single_knob_detections)}")
        elif hasattr(self, '_knob_detections'):
            # Full detection mode
            knob_detections = self._knob_detections
            # print(f"Knob detections: {len(knob_detections)}")
        
        if hasattr(self, '_angle_detections'):
            angle_detections = self._angle_detections
            # print(f"Angle detections: {len(angle_detections)}")
        
        # Process collected data with outlier filtering
        stable_result = self._process_multi_frame_data(
            button_detections, knob_detections, angle_detections, 
            last_frame_data
        )
        
        return stable_result
    
    def _process_single_target_frame(self, button_data, frame_result, depth_image, color_intrin):
        """Process single target detection result (touching_* status)"""
        target = button_data.get('target')
        target_type = button_data.get('target_type')
        
        if target is None:
            return
        
        if target_type in ['left_button', 'right_button']:
            # Target is already in 3D coordinates from detection
            frame_result['buttons'] = [target]
            # For single target mode, we collect individual button detections
            if not hasattr(self, '_single_target_detections'):
                self._single_target_detections = []
            self._single_target_detections.append(target)
            
        elif target_type == 'knob':
            # Target is already in 3D coordinates from detection
            frame_result['knob'] = target
            # For single target mode, we collect individual knob detections
            if not hasattr(self, '_single_knob_detections'):
                self._single_knob_detections = []
            self._single_knob_detections.append(target)
    
    def _process_full_detection_frame(self, frame_result, top_row, bottom_row, rotation_button, handle_angle, depth_image, color_intrin):
        """Process full detection result (non-touching_* status)"""
        # Process button coordinates - ensure consistent ordering
        if top_row is not None and len(top_row) >= 2 and bottom_row is not None and len(bottom_row) >= 2:
            # Sort by x-coordinate to ensure consistent ordering
            top_sorted = sorted(top_row[:2], key=lambda b: b[0])
            bottom_sorted = sorted(bottom_row[:2], key=lambda b: b[0])
            
            # Convert to 3D coordinates using robust depth estimation
            button_3d_coords = []
            for button in [bottom_sorted[0], bottom_sorted[1], top_sorted[0], top_sorted[1]]:  # bottom_left, bottom_right, top_left, top_right
                bx, by, br = button
                # Use robust depth estimation with circular ROI
                depth_val = self.controller._robust_depth_estimation(depth_image, bx, by, br)
                if depth_val is not None and depth_val > 0:
                    point_3d = rs.rs2_deproject_pixel_to_point(
                        color_intrin, (float(bx), float(by)), depth_val / 1000.0
                    )
                    button_3d_coords.append(point_3d)
            
            if len(button_3d_coords) == 4:
                frame_result['buttons'] = button_3d_coords
                # Access collection lists from class attributes
                if not hasattr(self, '_button_detections'):
                    self._button_detections = []
                self._button_detections.append(button_3d_coords)
        
        # Process knob coordinate using robust depth estimation
        if rotation_button is not None:
            bx, by, br = rotation_button
            # Use robust depth estimation with circular ROI
            depth_val = self.controller._robust_depth_estimation(depth_image, bx, by, br)
            if depth_val is not None and depth_val > 0:
                knob_3d = rs.rs2_deproject_pixel_to_point(
                    color_intrin, (float(bx), float(by)), depth_val / 1000.0
                )
                frame_result['knob'] = knob_3d
                # Access collection lists from class attributes
                if not hasattr(self, '_knob_detections'):
                    self._knob_detections = []
                self._knob_detections.append(knob_3d)
        
        # Process handle angle
        if handle_angle is not None:
            frame_result['angle'] = handle_angle
            # Access collection lists from class attributes
            if not hasattr(self, '_angle_detections'):
                self._angle_detections = []
            self._angle_detections.append(handle_angle)
    
    def _process_multi_frame_data(self, button_detections, knob_detections, angle_detections, last_frame_data):
        """
        Process multi-frame detection data with outlier filtering
        
        Args:
            button_detections (list): List of button detection results
            knob_detections (list): List of knob detection results  
            angle_detections (list): List of angle detection results
            last_frame_data (dict): Data from the last processed frame
        
        Returns:
            dict: Processed stable detection result
        """
        result = {
            'button_coords': [],
            'button_labels': ['bottom_left', 'bottom_right', 'top_left', 'top_right'],
            'knob_coord': None,
            'knob_angle': None,
            'stability_metrics': {},
            'color_image': last_frame_data.get('color_image'),
            'depth_image': last_frame_data.get('depth_image'),
            'color_intrin': last_frame_data.get('color_intrin')
        }
        
        # Process button coordinates
        if button_detections:
            button_coords_array = np.array(button_detections)  # Shape: (n_frames, 4, 3)
            stable_buttons = []
            button_stds = []
            
            for i in range(4):  # For each button position
                button_coords = button_coords_array[:, i, :]  # All frames for button i
                
                # Remove outliers using IQR method
                filtered_coords = self._filter_coordinate_outliers(button_coords)
                
                if len(filtered_coords) > 0:
                    stable_coord = np.mean(filtered_coords, axis=0)
                    coord_std = np.std(filtered_coords, axis=0)
                    stable_buttons.append(stable_coord.tolist())
                    button_stds.append(np.linalg.norm(coord_std))
                else:
                    stable_buttons.append(None)
                    button_stds.append(float('inf'))
            
            result['button_coords'] = stable_buttons
            result['stability_metrics']['button_std'] = button_stds
        
        # Process knob coordinate
        if knob_detections:
            knob_coords_array = np.array(knob_detections)
            filtered_knob_coords = self._filter_coordinate_outliers(knob_coords_array)
            
            if len(filtered_knob_coords) > 0:
                stable_knob = np.mean(filtered_knob_coords, axis=0)
                knob_std = np.std(filtered_knob_coords, axis=0)
                result['knob_coord'] = stable_knob.tolist()
                result['stability_metrics']['knob_std'] = np.linalg.norm(knob_std)
                # print('debug | survive outlier filtering')
            else:
                result['stability_metrics']['knob_std'] = float('inf')
        
        # Process handle angle
        if angle_detections:
            filtered_angles = self._filter_angle_outliers(angle_detections)
            
            if len(filtered_angles) > 0:
                stable_angle = np.mean(filtered_angles)
                angle_std = np.std(filtered_angles)
                result['knob_angle'] = stable_angle
                result['stability_metrics']['angle_std'] = angle_std
            else:
                result['stability_metrics']['angle_std'] = float('inf')
        
        # Add detection rates
        total_frames = max(len(button_detections), len(knob_detections), len(angle_detections), 1)
        result['stability_metrics']['button_detection_rate'] = len(button_detections) / total_frames
        result['stability_metrics']['knob_detection_rate'] = len(knob_detections) / total_frames
        result['stability_metrics']['angle_detection_rate'] = len(angle_detections) / total_frames
        
        # print(f"Stability metrics: {result['stability_metrics']}")
        
        return result
    
    def _filter_coordinate_outliers(self, coords_array, threshold=2.0):
        """
        Filter coordinate outliers using IQR method
        
        Args:
            coords_array (np.ndarray): Array of coordinates to filter
            threshold (float): IQR threshold multiplier for outlier detection
            
        Returns:
            np.ndarray: Filtered coordinates array
        """
        if len(coords_array) < 3:
            return coords_array
        
        # Calculate IQR for each dimension
        q1 = np.percentile(coords_array, 25, axis=0)
        q3 = np.percentile(coords_array, 75, axis=0)
        iqr = q3 - q1
        
        # Define outlier bounds
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # Filter outliers
        valid_mask = np.all(
            (coords_array >= lower_bound) & (coords_array <= upper_bound), 
            axis=1
        )
        
        return coords_array[valid_mask]
    
    def _filter_angle_outliers(self, angles_list, threshold=15.0):
        """
        Filter angle outliers considering circular nature of angles
        
        Args:
            angles_list (list): List of angles to filter
            threshold (float): Angular threshold in degrees for outlier detection
            
        Returns:
            np.ndarray: Filtered angles array
        """
        if len(angles_list) < 3:
            return angles_list
        
        angles_array = np.array(angles_list)
        
        # Use circular statistics for angles
        angles_rad = np.deg2rad(angles_array)
        mean_angle_rad = np.angle(np.mean(np.exp(1j * angles_rad)))
        
        # Calculate angular differences
        angle_diffs = np.abs(np.angle(np.exp(1j * (angles_rad - mean_angle_rad))))
        
        # Filter based on angular threshold
        valid_mask = angle_diffs <= np.deg2rad(threshold)
        
        return angles_array[valid_mask]
    
    def calculate_stability_score(self, stability_metrics):
        """
        Calculate overall stability score from metrics
        
        Args:
            stability_metrics (dict): Stability metrics dictionary
            
        Returns:
            float: Overall stability score (0-1, higher is better)
        """
        # Detection rate score (30% weight)
        detection_score = 0.3 * (
            stability_metrics.get('button_detection_rate', 0) * 0.5 +
            stability_metrics.get('knob_detection_rate', 0) * 0.3 +
            stability_metrics.get('angle_detection_rate', 0) * 0.2
        )
        
        # Stability score (70% weight)
        button_stds = stability_metrics.get('button_std', [float('inf')])
        button_stability = 1.0 / (1.0 + np.mean([std for std in button_stds if std != float('inf')]))
        
        knob_std = stability_metrics.get('knob_std', float('inf'))
        knob_stability = 1.0 / (1.0 + knob_std) if knob_std != float('inf') else 0
        
        angle_std = stability_metrics.get('angle_std', float('inf'))
        angle_stability = 1.0 / (1.0 + angle_std) if angle_std != float('inf') else 0
        
        stability_score = 0.7 * (
            button_stability * 0.5 +
            knob_stability * 0.3 +
            angle_stability * 0.2
        )
        
        total_score = detection_score + stability_score
        return min(total_score, 1.0)
    
    def get_recommended_frame_count(self, initial_frames=30):
        """
        Get recommended frame count based on initial detection stability
        
        Args:
            initial_frames (int): Number of frames to use for initial assessment
            
        Returns:
            int: Recommended frame count for optimal stability
        """
        # print(f"Assessing detection stability with {initial_frames} frames...")
        
        # Run initial detection
        initial_result = self.multi_frame_detection(initial_frames)
        stability_score = self.calculate_stability_score(initial_result['stability_metrics'])
        
        # print(f"Initial stability score: {stability_score:.3f}")
        
        # Recommend frame count based on stability
        if stability_score > 0.8:
            return 30  # Good stability, fewer frames needed
        elif stability_score > 0.6:
            return 60  # Moderate stability, standard frame count
        elif stability_score > 0.4:
            return 90  # Poor stability, more frames needed
        else:
            return 120  # Very poor stability, maximum frames
    
    def adaptive_multi_frame_detection(self, max_frames=120, target_stability=0.8, progress_callback=None):
        """
        Adaptive multi-frame detection that stops when target stability is reached
        
        Args:
            max_frames (int): Maximum number of frames to collect
            target_stability (float): Target stability score to achieve
            progress_callback (callable): Optional progress callback
            
        Returns:
            dict: Detection result with achieved stability
        """
        min_frames = 20
        check_interval = 10
        
        # print(f"Starting adaptive multi-frame detection (target stability: {target_stability})")
        
        # Collect frames in batches
        all_button_detections = []
        all_knob_detections = []
        all_angle_detections = []
        
        for frames_collected in range(min_frames, max_frames + 1, check_interval):
            # Collect additional frames
            batch_result = self.multi_frame_detection(check_interval, progress_callback)
            
            # Accumulate results (this is a simplified version - in practice you'd want to 
            # maintain the raw detection lists across batches)
            
            # Check stability
            stability_score = self.calculate_stability_score(batch_result['stability_metrics'])
            
            # print(f"Frames: {frames_collected}, Stability: {stability_score:.3f}")
            
            if stability_score >= target_stability:
                # print(f"Target stability achieved with {frames_collected} frames")
                return batch_result
        
        # print(f"Maximum frames ({max_frames}) reached without achieving target stability")
        return batch_result


if __name__ == "__main__":
    # Test the module independently
    # print("Testing MultiFrameProcessor...")
    print("Note: This module requires a RealTimeButtonControl instance to function properly")
    print("Use within the main rtBC.py system for full functionality")
    
    # Test outlier filtering functions
    processor = MultiFrameProcessor(None)
    
    # Test coordinate outlier filtering
    test_coords = np.array([
        [1.0, 2.0, 3.0],
        [1.1, 2.1, 3.1],
        [1.0, 2.0, 3.0],
        [5.0, 2.0, 3.0],  # Outlier
        [0.9, 1.9, 2.9]
    ])
    
    filtered = processor._filter_coordinate_outliers(test_coords)
    # print(f"Original coords: {len(test_coords)}, Filtered: {len(filtered)}")
    
    # Test angle outlier filtering
    test_angles = [10, 12, 11, 50, 9]  # 50 is outlier
    filtered_angles = processor._filter_angle_outliers(test_angles)
    # print(f"Original angles: {len(test_angles)}, Filtered: {len(filtered_angles)}") 