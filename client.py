import datetime
import socket
import json
import threading
import time
from task.pole_processor import <PERSON><PERSON><PERSON>, Camera
from task.button_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils import *

# Global variables for communication
client_socket = None
is_connected = False
command_queue = []
command_lock = threading.Lock()
socket_lock = threading.Lock()
stop_event = threading.Event()


# Global variables for task management
current_task = None  # "pole", "photo", "control_toggle", None
last_task = None
def socket_client_thread():
    """Thread function to handle socket connection and receive commands"""
    global client_socket, is_connected, command_queue, current_task
    
    # Server connection details
    host = '*************'  # Change to the actual server IP
    # host = 'localhost'  # Change to the actual server IP
    port = 17852
    reconnect_delay = 5  # seconds

    while not stop_event.is_set():
        try:
            if not is_connected:
                # Create socket
                with socket_lock:
                    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    client_socket.settimeout(10)  # 10 seconds timeout for connection
                    client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                    # Connect to server
                    print(f"Connecting to server {host}:{port}...")
                    client_socket.connect((host, port))
                    is_connected = True
                    print(f"Connected to server {host}:{port}")
            
            # Wait for commands
            try:
                data = client_socket.recv(1024)
                if not data:
                    # Connection closed by server
                    print("Connection closed by server")
                    is_connected = False
                    with socket_lock:
                        client_socket.close()
                    time.sleep(reconnect_delay)
                    continue
                
                # Parse command
                command_data = data.decode('utf-8')
                try:
                    command = json.loads(command_data)
                    print(f'[{datetime.datetime.now().strftime("%m-%d %H:%M:%S")}] Received command: {command}, push into queue')
                    command_type = command.get("command")
                    if command_type == 'emergency_stop':
                        # Handle emergency stop immediately - stop both processors for safety
                        print("Emergency stop triggered - stopping all processors")

                        # Always stop the arm first
                        cm.arm.rm_set_arm_stop()
                        cm.arm.rm_set_arm_delete_trajectory()

                        # Stop smoke pole processor
                        sp.emergency_stop()

                        # Stop button processor if available
                        if button_handler is not None:
                            button_handler.emergency_stop()

                        # Handle therm_test if implemented
                        if current_task in ['therm_test']:
                            # TODO: Implement emergency stop for therm_test
                            time.sleep(1.0)

                        print("Emergency stop completed for all processors")
                        initial_response = {
                            "code": SUCCESS_CODE,
                            "status": "received",
                            "command": "emergency_stop",
                            "task": current_task,
                            "message": f"Receive emergency_stop command on task {current_task}",
                            "data": {}
                        }
                        send_response(initial_response)
                    else:
                        # Add command to queue for processing
                        with command_lock:
                            command_queue.append(command)
                        
                except json.JSONDecodeError:
                    print(f"Invalid JSON received: {command_data}")
                    
            except socket.timeout:
                # No data received, just continue
                continue
                
        except socket.error as e:
            print(f"Socket error: {e}")
            is_connected = False
            with socket_lock:
                if client_socket:
                    client_socket.close()
            time.sleep(reconnect_delay)
            
        except Exception as e:
            print(f"Unexpected error in socket thread: {e}")
            is_connected = False
            with socket_lock:
                if client_socket:
                    client_socket.close()
            time.sleep(reconnect_delay)
    
    # Clean up when thread is stopping
    with socket_lock:
        if client_socket:
            client_socket.close()
    print("Socket client thread terminated")


def send_response(response):
    """Send response to the server"""
    global client_socket, is_connected
    
    if not is_connected:
        print("Cannot send response: not connected")
        return False
    
    try:
        with socket_lock:
            client_socket.sendall(json.dumps(response).encode('utf-8'))
            print(f'[{datetime.datetime.now().strftime("%m-%d %H:%M:%S")}] "Response sent: {response}')
        return True
    except Exception as e:
        print(f"Error sending response: {e}")
        is_connected = False
        return False


def command_processor_thread():
    """Thread function to process commands from the queue"""
    global command_queue, current_task, button_handler, last_task
    
    while not stop_event.is_set():
        # Check if there are commands to process
        if command_queue:
            with command_lock:
                command = command_queue.pop(0)
            
            # Extract command details
            command_type = command.get("command")
            print(f'[{datetime.datetime.now().strftime("%m-%d %H:%M:%S")}] Received command: {command}, get from queue')
            task_type = command.get("task")
            params = command.get("params", {})
            
            # Process start-smoke_test command
            if command_type == "start" and task_type == "smoke_test":
                print("Processing start-smoke_test command...")
                current_task = "smoke_test"
                last_task = current_task
                
                # Get parameters from command
                pole_length = params.get("pole_length", None)  # Default 1150
                wait_time = params.get("wait_time", None)  # Default 10 seconds
                unreachable_action = params.get("unreachable_action", 'continue')
                default_position_idx = params.get("checkpoint", None)
                checkpoint_bool = params.get("checkpoint_bool", None)

                # Configure smoke pole
                sp.reset_emergency_stop()
                sp.resume_frame_acquisition()
                time.sleep(0.5)
                sp.resume_detection()
                sp.set_pole_length(pole_length)
                sp.set_wait_time(wait_time)
                sp.set_unreachable_action(unreachable_action)
                sp.set_default_position_idx(default_position_idx)

                # Send initial response
                initial_response = {
                    "code": SUCCESS_CODE,
                    "status": "received",
                    "command": "start",
                    "task": "smoke_test",
                    "message": "Receive start-smoke_test command",
                    "data": {}
                }
                send_response(initial_response)
                time.sleep(1.0)

                # Execute pole procedure
                result = sp.work_procedure()
                if result[0] == SUCCESS_CODE:
                    response = {
                        "code": SUCCESS_CODE,
                        "status": "success",
                        "command": "start",
                        "task": "smoke_test",
                        "message": "Complete start-smoke_test command",
                        "data": {
                            "result": sp.detection_result,
                            "path": sp.detection_path,
                            # "default_position_idx": sp.default_position_idx
                        }
                    }
                    response["data"]["checkpoint_bool"] = checkpoint_bool
                    if checkpoint_bool:
                        response['data']['checkpoint'] = sp.default_position_idx
                else:
                    error_code = result[0]
                    error_message = result[1]['error_message']
                    data = result[1].get("data", None)
                    offset = data.get("offset", None) if data is not None else None
                    response = {
                        "code": error_code,
                        "status": "error",
                        "command": "start",
                        "task": "smoke_test",
                        "message": "Error during start-smoke_test command: " + error_message,
                        "data": {
                            "offset": offset,
                            "result": sp.detection_result,
                            "path": sp.detection_path,
                            # "checkpoint": sp.default_position_idx
                        }
                    }

                    response["data"]["checkpoint_bool"] = checkpoint_bool
                    if error_code != EMERGENCY_STOP_ERROR:
                        time.sleep(0.2)
                        ret = sp.reset()
                        print(f"reset's ret: {ret}")
                    else:
                        cm.arm.rm_set_arm_delete_trajectory()

                    time.sleep(0.2)
                    sp.reset_emergency_stop()
                    # cm.arm.rm_set_arm_delete_trajectory()

                send_response(response)
                current_task = None

            # Process start-therm_test command
            elif command_type == "start" and task_type == "therm_test":
                print("Processing start-therm_test command...")
                current_task = "therm_test"
                last_task = current_task

                time.sleep(0.5)

                # Send initial response
                initial_response = {
                    "code": SUCCESS_CODE,
                    "status": "received",
                    "command": "start",
                    "task": "therm_test",
                    "message": "Receive start-therm_test command",
                    "data": {}
                }
                send_response(initial_response)

                # No exact task running here, so sleep for one second
                ret, msg = sp.release_gripper()
                time.sleep(1.0)

                # Execute pole procedure
                # result = [SUCCESS_CODE, {'error_message': 'No error'}]
                if ret == SUCCESS_CODE:
                    response = {
                        "code": SUCCESS_CODE,
                        "status": "success",
                        "command": "start",
                        "task": "therm_test",
                        "message": "Complete start-therm_test command",
                        "data": {
                            "result": True,
                            "path": "./fake_img.png"
                        }
                    }
                else:
                    error_code = ret
                    error_message = msg#result[1]['error_message']
                    response = {
                        "code": error_code,
                        "status": "error",
                        "command": "start",
                        "task": "therm_test",
                        "message": "Error during start-therm_test command: " + error_message,
                        "data": {
                            "result": True,
                            "path": "./fake_img.png"
                        }
                    }
                    time.sleep(1.0)
                    cm.arm.rm_set_arm_delete_trajectory()
                send_response(response)
                current_task = None

            # Process start-control_toggle command
            elif command_type == "start" and task_type == "control_toggle":
                print("Processing start-control_toggle command...")
                current_task = "control_toggle"
                last_task = current_task

                if button_handler is None:
                    response = {
                        "code": UNKNOWN_ERROR,
                        "status": "error",
                        "command": "start",
                        "task": "control_toggle",
                        "message": "control_toggle task object not available",
                        "data": {}
                    }
                    send_response(response)
                    current_task = None
                    continue

                # Send initial response
                initial_response = {
                    "code": SUCCESS_CODE,
                    "status": "received",
                    "command": "start",
                    "task": "control_toggle",
                    "message": "Receive start-control_toggle command",
                    "data": {}
                }
                send_response(initial_response)

                # Check if this is a continuous sequence request
                operating_sequence = params.get("operating", None)
                checkpoint_bool = params.get("checkpoint_bool", None)
                # checkpoint = params.get("checkpoint", None)
                # button_handler.set_known_orientation(checkpoint)

                if operating_sequence is not None:
                    # Execute continuous sequence
                    print(f"ButtonHandler: Executing continuous sequence: {operating_sequence}")
                    result = button_handler.execute_continuous_sequence(params)
                else:
                    # Execute single action (original behavior)
                    result = button_handler.run(params)
                
                if result[0] == 0:
                    response = {
                        "code": 0,
                        "status": "success",
                        "command": "start",
                        "task": "control_toggle",
                        "message": "Complete start-control_toggle command",
                        "data": result[1] if len(result) > 1 else {}
                    }
                    response["data"]["checkpoint_bool"] = checkpoint_bool
                    if checkpoint_bool and "checkpoint" in result[1]:
                        response["data"]["checkpoint"] = result[1]["checkpoint"]
                    current_task = None
                else:
                    error_code = result[0]
                    error_message = result[1]['error_message']
                    response = {
                        "code": error_code,
                        "status": "error",
                        "command": "start",
                        "task": "control_toggle",
                        "message": "Error during start-control_toggle command: " + error_message,
                        "data": result[1] if len(result) > 1 else {}
                    }
                    response["data"]["checkpoint_bool"] = checkpoint_bool
                    current_task = None
                send_response(response)
            
            # Process start-photo command    
            elif command_type == "start" and task_type == "photo":
                print("Processing start-photo command...")
                current_task = "photo"
                target_task = current_task
                
                # Configure photo mode
                photo_direct_mode = params.get("photo_direct_mode", False)
                sp.set_photo_direct_mode(photo_direct_mode)
                sp.resume_frame_acquisition()
                time.sleep(0.5)
                sp.resume_detection()
                time.sleep(1.0)
                
                try:
                    # Send initial response
                    response = {
                        "code": SUCCESS_CODE,
                        "status": "received",
                        "command": "start",
                        "task": "photo",
                        "message": "Receive start-photo command",
                        "data": {}
                    }
                    send_response(response)
                    
                    # Take photos
                    ret = sp.take_photos()
                    ret_code, ret_data = ret
                    
                    if ret_code == SUCCESS_CODE:
                        response = {
                            "code": SUCCESS_CODE,
                            "status": "success",
                            "command": "start",
                            "task": "photo",
                            "message": "Complete start-photo command",
                            "data": {
                                "result": ret_data['result'],
                                "path": ret_data['path']
                            }
                        }
                    else:
                        response = {
                            "code": ret_code,
                            "status": "error",
                            "command": "start",
                            "task": "photo",
                            "message": "Error during start-photo command: " + ret_data['error_message'],
                            "data": {
                                "result": ret_data['result'],
                                "path": ret_data['path']
                            }
                        }
                        if ret_code != EMERGENCY_STOP_ERROR:
                            time.sleep(0.2)
                            ret = sp.reset()
                            print(f"reset's ret: {ret}")
                        time.sleep(0.2)
                        sp.reset_emergency_stop()
                        cm.arm.rm_set_arm_delete_trajectory()
                except Exception as e:
                    import traceback
                    error_message = f"Error during start-photo command: {str(e)}\n{traceback.format_exc()}"
                    response = {
                        "code": UNKNOWN_ERROR,
                        "status": "error",
                        "command": "start",
                        "task": "photo",
                        "message": error_message,
                        "data": {}
                    }
                
                send_response(response)
                if not sp._emergency_stop:
                    sp.pause_detection()
                
                current_task = None
            
            # Process reset command
            elif command_type == "reset":
                print("Processing reset command...")

                # Always reset both processors for consistency
                print("Resetting smoke pole processor...")
                sp.reset_emergency_stop()
                sp.resume_frame_acquisition()
                sp.resume_detection()

                # Reset button processor if available
                if button_handler is not None:
                    print("Resetting button processor...")
                    button_handler.reset_emergency_stop()

                time.sleep(0.2)  # Allow processors to reset
                target_task = current_task if current_task is not None else last_task
                if target_task in ['photo', 'smoke_test'] or target_task is None:

                    # Send initial response
                    initial_response = {
                        "code": SUCCESS_CODE,
                        "status": "received",
                        "command": "reset",
                        "task": target_task,
                        "message": f"Receive reset-{target_task} command",
                        "data": {}
                    }
                    send_response(initial_response)
                    ret = sp.reset()
                    
                    if ret[0] != SUCCESS_CODE:
                        response = {
                            "code": ret[0],
                            "status": "error",
                            "command": command_type,
                            "task": target_task,
                            "message": f"Error during reset-{target_task} command: " + ret[1]['error_message'],
                            "data": {}
                        }
                    else:
                        sp.reset_emergency_stop()
                        response = {
                            "code": ret[0],
                            "status": "success",
                            "command": command_type,
                            "task": target_task,
                            "message": f"Complete reset-{target_task} command complete",
                            "data": {}
                        }
                        current_task = None
                    send_response(response)

                elif target_task == 'control_toggle' and button_handler is not None:
                    # Reset button task - optimized for faster response
                    print("Processing control_toggle reset...")

                    # Send initial response immediately
                    initial_response = {
                        "code": SUCCESS_CODE,
                        "status": "received",
                        "command": "reset",
                        "task": target_task,
                        "message": f"Receive reset-{target_task} command",
                        "data": {}
                    }
                    send_response(initial_response)
                    
                    # Reset emergency stop flag
                    button_handler.reset_emergency_stop()
                    
                    # Execute reset with timeout protection
                    try:
                        ret = button_handler.reset()
                        
                        if ret != SUCCESS_CODE:
                            response = {
                                "code": ret,
                                "status": "error",
                                "command": "reset",
                                "task": target_task,
                                "message": f"Error during reset-{target_task} command",
                                "data": {}
                            }
                        else:
                            response = {
                                "code": ret,
                                "status": "success",
                                "command": "reset",
                                "task": target_task,
                                "message": f"Complete reset-{target_task} command",
                                "data": {}
                            }
                            current_task = None
                    except Exception as e:
                        print(f"Error during button handler reset: {e}")
                        response = {
                            "code": UNKNOWN_ERROR,
                            "status": "error",
                            "command": "reset",
                            "task": current_task,
                            "message": f"Exception during reset-{current_task}/{target_task} command: {str(e)}",
                            "data": {}
                        }
                    
                    send_response(response)
                    current_task = None
                elif target_task == 'therm_test' and button_handler is not None:
                    # Reset button task

                    # Send initial response
                    initial_response = {
                        "code": SUCCESS_CODE,
                        "status": "received",
                        "command": "reset",
                        "task": target_task,
                        "message": f"Receive reset-{target_task} command",
                        "data": {}
                    }
                    send_response(initial_response)

                    # No exact task running here, so sleep for one second
                    time.sleep(1.0)

                    # Execute pole procedure
                    ret = [SUCCESS_CODE, {'error_message': 'No error'}]

                    if ret[0] != SUCCESS_CODE:
                        response = {
                            "code": ret[0],
                            "status": "error",
                            "command": "reset",
                            "task": target_task,
                            "message": f"Error during reset-{target_task} command" + ret[1]["error_message"],
                            "data": {}
                        }
                    else:
                        response = {
                            "code": ret[0],
                            "status": "success",
                            "command": "reset",
                            "task": target_task,
                            "message": f"Complete reset-{target_task} command",
                            "data": {}
                        }
                    send_response(response)
                    current_task = None

                # Process resume command
                elif command_type == "resume":
                    print("Processing resume command...(not implement yet...)")
                    if current_task in ['photo', 'smoke_test'] or current_task is None:
                        # Reset smoke_test task
                        sp.reset_emergency_stop()
                        sp.resume_frame_acquisition()
                        sp.resume_detection()
                        time.sleep(0.5)

                        # Send initial response
                        initial_response = {
                            "code": SUCCESS_CODE,
                            "status": "received",
                            "command": "resume",
                            "task": current_task,
                            "message": f"Receive resume-{current_task} command",
                            "data": {}
                        }
                        send_response(initial_response)
                        # ret = sp.resume()
                    elif current_task == 'control_toggle':
                        # Reset button task

                        # Send initial response
                        initial_response = {
                            "code": SUCCESS_CODE,
                            "status": "received",
                            "command": "resume",
                            "task": current_task,
                            "message": f"Receive resume-{current_task} command",
                            "data": {}
                        }
                        send_response(initial_response)
                else:
                    response = {
                        "code": UNKNOWN_ERROR,
                        "status": "error",
                        "command": "resume",
                        "task": "control_toggle",
                        "message": "No paused task active to resume",
                        "data": {}
                    }
                    send_response(response)

            # Process debug-capture_current_view command
            elif command_type == "debug" and task_type == "capture_current_view":
                print("Processing debug-capture_current_view command...")
                current_task = "debug_capture"

                if button_handler is None:
                    response = {
                        "code": UNKNOWN_ERROR,
                        "status": "error",
                        "command": "debug",
                        "task": "capture_current_view",
                        "message": "ButtonHandler not available for debug capture",
                        "data": {}
                    }
                    send_response(response)
                    current_task = None
                    continue

                # Send initial response
                initial_response = {
                    "code": SUCCESS_CODE,
                    "status": "received",
                    "command": "debug",
                    "task": "capture_current_view",
                    "message": "Receive debug-capture_current_view command",
                    "data": {}
                }
                send_response(initial_response)

                try:
                    # Execute debug capture
                    result = button_handler.debug_capture_current_view(params)
                    ret_code, ret_data = result

                    if ret_code == SUCCESS_CODE:
                        response = {
                            "code": SUCCESS_CODE,
                            "status": "success",
                            "command": "debug",
                            "task": "capture_current_view",
                            "message": "Complete debug-capture_current_view command",
                            "data": ret_data
                        }
                    else:
                        response = {
                            "code": ret_code,
                            "status": "error",
                            "command": "debug",
                            "task": "capture_current_view",
                            "message": "Error during debug-capture_current_view command: " + ret_data.get('error_message', 'Unknown error'),
                            "data": ret_data
                        }
                except Exception as e:
                    import traceback
                    error_message = f"Exception during debug-capture_current_view command: {str(e)}\n{traceback.format_exc()}"
                    response = {
                        "code": UNKNOWN_ERROR,
                        "status": "error",
                        "command": "debug",
                        "task": "capture_current_view",
                        "message": error_message,
                        "data": {}
                    }

                send_response(response)
                current_task = None

            # Handle unknown commands
            else:
                print(f"Unknown command: {command_type}")
                response = {
                    "code": PARSING_ERROR,
                    "status": "error",
                    "command": command_type,
                    "message": f"Unknown command: {command_type}",
                    "data": {}
                }
                send_response(response)
                
        time.sleep(0.1)


def resource_monitor_thread():
    """Thread to monitor and optimize resource usage"""
    global last_activity_time, current_task
    
    while not stop_event.is_set():
        # If no active task and detection not paused, pause it to save resources
        if current_task is None:
            if not sp._detection_paused:
                # Only pause if it's been idle for a while (300 seconds)
                if time.time() - last_activity_time > 300:
                    print("Pausing detection to save resources")
                    sp.pause_detection()
            if not sp._frame_acquisition_paused:
                # After another period of inactivity (600 seconds), also pause frame acquisition
                if time.time() - last_activity_time > 600:
                    print("Pausing frame acquisition to save resources")
                    sp.pause_frame_acquisition()
        time.sleep(5)


if __name__ == "__main__":
    # Initialize timestamp for activity monitoring
    last_activity_time = time.time()

    # Initialize camera
    print("Initializing camera...")
    cm = Camera(visible=False)  # True
    thread_cm = threading.Thread(target=cm.run)
    thread_cm.daemon = True
    thread_cm.start()
    
    # Wait for camera to initialize
    time.sleep(2)

    # Initialize smoke pole first
    print("Initializing smoke smoke_test...")
    sp = SmokePole(cm, visible=False)

    # Try to import button handler (after camera initialization)
    button_handler = ButtonHandler(cm)
    
    # Start detection thread (initially paused to save resources)
    thread_sp = threading.Thread(target=sp.update_frames_and_detection)
    thread_sp.daemon = True
    thread_sp.start()
    
    # Initially pause detection to save resources
    sp.pause_detection()

    # Start worker threads
    socket_thread = threading.Thread(target=socket_client_thread)
    socket_thread.daemon = True
    socket_thread.start()
    
    processor_thread = threading.Thread(target=command_processor_thread)
    processor_thread.daemon = True
    processor_thread.start()
    
    monitor_thread = threading.Thread(target=resource_monitor_thread)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    try:
        # Main loop
        print("System ready. Waiting for commands...")
        while True:
            # Update activity timestamp when there's user interaction or commands
            if command_queue:
                last_activity_time = time.time()
            
            # Sleep to reduce CPU usage
            time.sleep(0.1)
    
    except KeyboardInterrupt:
        print("Shutting down...")
        
    finally:
        # Signal threads to stop
        stop_event.set()
        
        # Wait for threads to finish
        if socket_thread.is_alive():
            socket_thread.join(timeout=2)
        if processor_thread.is_alive():
            processor_thread.join(timeout=2)
        if monitor_thread.is_alive():
            monitor_thread.join(timeout=2)
        
        # Close socket if open
        with socket_lock:
            if client_socket:
                client_socket.close()
        
        print("System shutdown complete")
