import cv2
import numpy as np
import os
import json
from pathlib import Path
import argparse
from datetime import datetime

class DebugVisualizer:
    """
    Visualization plugin for button detection debug results
    """
    
    def __init__(self, debug_dir="debug_results"):
        self.debug_dir = Path(debug_dir)
        self.current_session = None
        self.session_data = {}
    
    def list_sessions(self):
        """List all available debug sessions"""
        if not self.debug_dir.exists():
            print(f"Debug directory {self.debug_dir} does not exist")
            return []
        
        sessions = []
        for item in self.debug_dir.iterdir():
            if item.is_dir():
                sessions.append(item.name)
        
        sessions.sort(reverse=True)  # Most recent first
        return sessions
    
    def load_session(self, session_name):
        """Load a debug session"""
        self.current_session = session_name
        session_path = self.debug_dir / session_name
        
        if not session_path.exists():
            print(f"Session {session_name} does not exist")
            return False
        
        # Load detection results if available
        results_file = session_path / "detection_results.json"
        if results_file.exists():
            with open(results_file, 'r') as f:
                self.session_data = json.load(f)
        
        print(f"Loaded session: {session_name}")
        return True
    
    def show_image(self, image_path, title="Image", wait_key=True):
        """Display an image with optional wait for key press"""
        if not image_path.exists():
            print(f"Image {image_path} does not exist")
            return
        
        img = cv2.imread(str(image_path))
        if img is None:
            print(f"Failed to load image {image_path}")
            return
        
        # Resize if image is too large
        height, width = img.shape[:2]
        if height > 800 or width > 1200:
            scale = min(800/height, 1200/width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height))
        
        cv2.imshow(title, img)
        if wait_key:
            cv2.waitKey(0)
    
    def visualize_masks(self):
        """Visualize all color masks"""
        if not self.current_session:
            print("No session loaded")
            return
        
        session_path = self.debug_dir / self.current_session
        masks_dir = session_path / "masks"
        
        if not masks_dir.exists():
            print("No masks directory found")
            return
        
        print("=== Color Masks Visualization ===")
        
        # Show masks in order
        mask_files = [
            ("03_red_mask1_mask.png", "Red Mask 1 (0-15 hue)"),
            ("03_red_mask2_mask.png", "Red Mask 2 (160-180 hue)"),
            ("04_red_mask_combined_mask.png", "Combined Red Mask"),
            ("04_green_mask_mask.png", "Green Mask"),
            ("05_red_mask_processed_mask.png", "Red Mask After Morphology"),
            ("05_green_mask_processed_mask.png", "Green Mask After Morphology")
        ]
        
        for filename, title in mask_files:
            mask_path = masks_dir / filename
            if mask_path.exists():
                print(f"Showing: {title}")
                self.show_image(mask_path, title)
            else:
                print(f"Missing: {filename}")
        
        cv2.destroyAllWindows()
    
    def visualize_processing_steps(self):
        """Visualize processing steps"""
        if not self.current_session:
            print("No session loaded")
            return
        
        session_path = self.debug_dir / self.current_session
        intermediate_dir = session_path / "intermediate"
        processed_dir = session_path / "processed"
        
        print("=== Processing Steps Visualization ===")
        
        # Show intermediate steps
        intermediate_files = [
            ("01_original_image.png", "Original Image"),
            ("02_hsv_image.png", "HSV Image")
        ]
        
        for filename, title in intermediate_files:
            img_path = intermediate_dir / filename
            if img_path.exists():
                print(f"Showing: {title}")
                self.show_image(img_path, title)
            else:
                print(f"Missing: {filename}")
        
        # Show final result
        final_result = processed_dir / "99_final_result.png"
        if final_result.exists():
            print("Showing: Final Detection Result")
            self.show_image(final_result, "Final Detection Result")
        
        cv2.destroyAllWindows()
    
    def create_comparison_view(self):
        """Create a comparison view of key images"""
        if not self.current_session:
            print("No session loaded")
            return
        
        session_path = self.debug_dir / self.current_session
        
        # Load key images
        original_path = session_path / "intermediate" / "01_original_image.png"
        red_mask_path = session_path / "masks" / "04_red_mask_combined_mask.png"
        green_mask_path = session_path / "masks" / "04_green_mask_mask.png"
        final_path = session_path / "processed" / "99_final_result.png"
        
        images = []
        titles = []
        
        for path, title in [(original_path, "Original"), 
                           (red_mask_path, "Red Mask"),
                           (green_mask_path, "Green Mask"),
                           (final_path, "Final Result")]:
            if path.exists():
                img = cv2.imread(str(path))
                if img is not None:
                    # Resize to consistent size
                    img = cv2.resize(img, (300, 200))
                    images.append(img)
                    titles.append(title)
        
        if len(images) < 2:
            print("Not enough images for comparison")
            return
        
        # Create comparison grid
        rows = 2
        cols = 2
        comparison = np.zeros((rows * 200, cols * 300, 3), dtype=np.uint8)
        
        for i, (img, title) in enumerate(zip(images[:4], titles[:4])):
            row = i // cols
            col = i % cols
            y_start = row * 200
            y_end = y_start + 200
            x_start = col * 300
            x_end = x_start + 300
            
            comparison[y_start:y_end, x_start:x_end] = img
            
            # Add title
            cv2.putText(comparison, title, (x_start + 10, y_start + 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow("Comparison View", comparison)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    def print_detection_results(self):
        """Print detection results from the session"""
        if not self.session_data:
            print("No detection results available")
            return
        
        print("=== Detection Results ===")
        print(f"Session: {self.current_session}")
        print(f"Top Row Buttons: {self.session_data.get('top_row', 'N/A')}")
        print(f"Bottom Row Buttons: {self.session_data.get('bottom_row', 'N/A')}")
        print(f"Knob Position: {self.session_data.get('knob', 'N/A')}")
        print(f"Handle Angle: {self.session_data.get('handle_angle', 'N/A')}")
        print(f"Mode Code: {self.session_data.get('mode_code', 'N/A')}")
    
    def interactive_menu(self):
        """Interactive menu for visualization"""
        while True:
            print("\n=== Debug Visualization Menu ===")
            print("1. List available sessions")
            print("2. Load a session")
            print("3. Show color masks")
            print("4. Show processing steps")
            print("5. Show comparison view")
            print("6. Print detection results")
            print("7. Exit")
            
            choice = input("Enter your choice (1-7): ").strip()
            
            if choice == '1':
                sessions = self.list_sessions()
                if sessions:
                    print("Available sessions:")
                    for i, session in enumerate(sessions, 1):
                        print(f"  {i}. {session}")
                else:
                    print("No sessions found")
            
            elif choice == '2':
                sessions = self.list_sessions()
                if not sessions:
                    print("No sessions available")
                    continue
                
                print("Available sessions:")
                for i, session in enumerate(sessions, 1):
                    print(f"  {i}. {session}")
                
                try:
                    idx = int(input("Enter session number: ")) - 1
                    if 0 <= idx < len(sessions):
                        self.load_session(sessions[idx])
                    else:
                        print("Invalid session number")
                except ValueError:
                    print("Please enter a valid number")
            
            elif choice == '3':
                self.visualize_masks()
            
            elif choice == '4':
                self.visualize_processing_steps()
            
            elif choice == '5':
                self.create_comparison_view()
            
            elif choice == '6':
                self.print_detection_results()
            
            elif choice == '7':
                break
            
            else:
                print("Invalid choice")

def main():
    parser = argparse.ArgumentParser(description='Button Detection Debug Visualizer')
    parser.add_argument('--session', help='Session name to load')
    parser.add_argument('--debug-dir', default='debug_results', help='Debug results directory')
    parser.add_argument('--auto', action='store_true', help='Auto-load latest session and show all')
    
    args = parser.parse_args()
    
    visualizer = DebugVisualizer(args.debug_dir)
    
    if args.auto:
        # Auto mode: load specified session or latest session and show everything
        session_to_load = args.session
        if not session_to_load:
            sessions = visualizer.list_sessions()
            if sessions:
                session_to_load = sessions[0]  # Most recent
            else:
                print("No debug sessions found")
                return
        
        if visualizer.load_session(session_to_load):
            print("=== Auto Visualization Mode ===")
            visualizer.print_detection_results()
            visualizer.visualize_processing_steps()
            visualizer.visualize_masks()
            visualizer.create_comparison_view()
    
    elif args.session:
        # Load specific session
        if visualizer.load_session(args.session):
            visualizer.interactive_menu()
    
    else:
        # Interactive mode
        visualizer.interactive_menu()

if __name__ == "__main__":
    main()