"""
Debug wrapper for button detection functions
This wrapper adds debug functionality without modifying the original files
"""
import os
import cv2
import numpy as np
from buttonControl.debug_saver import debug_saver
from buttonControl.button_detection import detect_buttons, detect_buttons_by_status

def debug_detect_buttons(image, verbose=False, display_process=True):
    """
    Wrapper for detect_buttons with debug functionality
    """
    # Start debug session
    debug_saver.start_session()
    
    # Save original image
    debug_saver.save_intermediate(image, "01_original_image")
    
    # Convert to HSV and save
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    debug_saver.save_intermediate(hsv, "02_hsv_image")
    
    # Create color masks for debugging
    lower_red1 = np.array([0, 50, 20])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([160, 50, 20])
    upper_red2 = np.array([180, 255, 255])
    lower_green = np.array([25, 50, 20])
    upper_green = np.array([95, 255, 255])
    
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)
    green_mask = cv2.inRange(hsv, lower_green, upper_green)
    
    # Save color masks
    debug_saver.save_mask(red_mask1, "03_red_mask1")
    debug_saver.save_mask(red_mask2, "03_red_mask2")
    debug_saver.save_mask(red_mask, "04_red_mask_combined")
    debug_saver.save_mask(green_mask, "04_green_mask")
    
    # Apply morphological operations and save
    kernel = np.ones((3,3), np.uint8)
    red_mask_processed = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    green_mask_processed = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)
    
    debug_saver.save_mask(red_mask_processed, "05_red_mask_processed")
    debug_saver.save_mask(green_mask_processed, "05_green_mask_processed")
    
    # Call original function
    result = detect_buttons(image, verbose, display_process)
    
    # Save final results
    if isinstance(result, tuple) and len(result) >= 6:
        display_img, top_row, bottom_row, knob, handle_angle, mode_code = result
        
        debug_saver.save_processed(display_img, "99_final_result")
        debug_saver.save_data({
            "top_row": top_row,
            "bottom_row": bottom_row,
            "knob": knob,
            "handle_angle": handle_angle,
            "mode_code": mode_code
        }, "detection_results")
    
    debug_saver.end_session()
    return result

def debug_detect_buttons_by_status(image, status='uncertain', verbose=False, display_process=True, **kwargs):
    """
    Wrapper for detect_buttons_by_status with debug functionality
    """
    # Start debug session
    debug_saver.start_session()
    
    # Save original image
    debug_saver.save_intermediate(image, "01_original_image")
    
    # Convert to HSV and save
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    debug_saver.save_intermediate(hsv, "02_hsv_image")
    
    # Create color masks for debugging
    lower_red1 = np.array([0, 50, 20])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([160, 50, 20])
    upper_red2 = np.array([180, 255, 255])
    lower_green = np.array([25, 50, 20])
    upper_green = np.array([95, 255, 255])
    
    red_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    red_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(red_mask1, red_mask2)
    green_mask = cv2.inRange(hsv, lower_green, upper_green)
    
    # Save color masks
    debug_saver.save_mask(red_mask1, "03_red_mask1")
    debug_saver.save_mask(red_mask2, "03_red_mask2")
    debug_saver.save_mask(red_mask, "04_red_mask_combined")
    debug_saver.save_mask(green_mask, "04_green_mask")
    
    # Apply morphological operations and save
    kernel = np.ones((3,3), np.uint8)
    red_mask_processed = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel)
    green_mask_processed = cv2.morphologyEx(green_mask, cv2.MORPH_CLOSE, kernel)
    
    debug_saver.save_mask(red_mask_processed, "05_red_mask_processed")
    debug_saver.save_mask(green_mask_processed, "05_green_mask_processed")
    
    # Call original function
    result = detect_buttons_by_status(image, status, verbose, display_process, **kwargs)
    
    # Save final results
    if isinstance(result, tuple):
        if len(result) == 4:
            target_result, confidence_score, display_img, crop_info = result
            debug_saver.save_processed(display_img, "99_final_result")
            debug_saver.save_data({
                "target_result": target_result,
                "confidence_score": confidence_score,
                "crop_info": crop_info
            }, "detection_results")
        else:
            # Handle other tuple lengths
            debug_saver.save_data({
                "result_length": len(result),
                "result": str(result)[:500]  # Truncate for safety
            }, "detection_results")
            if len(result) > 0 and hasattr(result[0], 'shape'):  # Likely an image
                debug_saver.save_processed(result[0], "99_final_result")
    
    debug_saver.end_session()
    return result