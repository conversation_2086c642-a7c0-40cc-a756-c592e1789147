import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status, convert_coordinates_to_original

def detect_knob_in_image(image_path, knob_type='knob_center', verbose=True, display_process=True):
    """
    Detect the knob in a given image.
    
    Args:
        image_path (str): Path to the input image
        knob_type (str): Type of knob to detect ('knob_left', 'knob_right', 'knob_center')
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        dict: Detection results including target, confidence, display image, etc.
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Map knob_type to status
    knob_status_map = {
        'knob_left': 'touching_knob_left',
        'knob_right': 'touching_knob_right', 
        'knob_center': 'touching_knob_center'
    }
    
    status = knob_status_map.get(knob_type, 'touching_knob_center')
    
    # Detect knob
    detection_result = detect_buttons_by_status(
        image, 
        status=status, 
        verbose=verbose, 
        display_process=display_process
    )
    
    return detection_result
    

if __name__ == "__main__":
    # Detect knob
    from pathlib import Path
    knob_img_list = Path('./data/button-debug-saves/knob/mustPass').glob('*.png')
    knob_img_list = [img.name for img in knob_img_list]
    # [
    #     'less_than_4_buttons_colorImage_20250730_094620_886',
    #     'less_than_4_buttons_colorImage_20250730_091940_387',
    #     'all_single_frame_detection_failed_colorImage_20250730_071950_117',
    #     'less_than_4_buttons_colorImage_20250730_071951_608',
    #     'less_than_4_buttons_colorImage_20250730_073402_571',
    #     'less_than_4_buttons_colorImage_20250730_073957_372',
    #     'less_than_4_buttons_colorImage_20250730_074759_405'
    # ]
    knob_types = ['knob_center', 'knob_left', 'knob_right']
    total = len(knob_img_list) * len(knob_types)
    success_count = 0

    for img_name in knob_img_list:
        for knob_type in knob_types:
            result = detect_knob_in_image(
                f'./data/button-debug-saves/knob/mustPass/{img_name}',
                knob_type=knob_type,
                verbose=True,
                display_process=True
            )

            # Print detection details
            # print(f"\n--- {knob_type.replace('_', ' ').title()} Detection Results ---")
            if isinstance(result, dict):
                target = result.get('target')
                confidence = result.get('confidence', 0.0)
                display_img = result.get('display_img')
                status = result.get('status', 'unknown')
                target_type = result.get('target_type', 'unknown')
                crop_info = result.get('crop_info', {})

                # print(f"Status: {status}")
                # print(f"Target Type: {target_type}")
                # print(f"Target Found: {target is not None}")
                if target:
                    # print(f"Knob Center: {target}")
                    # print(f"Confidence: {confidence:.3f}")

                    # Convert coordinates if crop_info is available
                    if crop_info:
                        original_coords = convert_coordinates_to_original(target, crop_info)
                        if original_coords:
                            # print(f"Original Coordinates: {original_coords}")
                            pass
                    success_count += 1
                else:
                    print(f"{img_name} with {knob_type}: Not detected")
                # print(f"Success: {target is not None}")
            else:
                # print(f"Unexpected result format: {type(result)}")
                pass

            # print(f"\nDetection completed for {img_name} - {knob_type}")

    # 汇报成功率
    print("\n==============================")
    print(f"Knob Detection Success Rate: {success_count}/{total} ({(success_count/total)*100:.1f}%)")
    print("==============================")