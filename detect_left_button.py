import cv2
import numpy as np
import sys
import os
from pathlib import Path

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status, convert_coordinates_to_original

def detect_left_button_in_image(image_path, verbose=True, display_process=True):
    """
    Detect the left button in a given image.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        dict: Detection results including target, confidence, display image, etc.
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Detect left button
    detection_result = detect_buttons_by_status(
        image, 
        status='touching_left_button', 
        verbose=verbose, 
        display_process=display_process
    )
    
    return detection_result
    

if __name__ == "__main__":
    # Detect left button
    mustPassList = list(Path('./data/button-debug-saves/leftButton/mustPass').glob('*.png'))
    toPassList = list(Path('./data/button-debug-saves/leftButton/toPass').glob('*.png'))
    mustPass_success_count = 0
    toPass_success_count = 0

    # Prepare output directories
    mustPassDetectionDir = Path('./data/button-debug-saves/leftButton/mustPassDetection')
    toPassDetectionDir = Path('./data/button-debug-saves/leftButton/toPassDetection')
    mustPassDetectionDir.mkdir(parents=True, exist_ok=True)
    toPassDetectionDir.mkdir(parents=True, exist_ok=True)
    # r_dist = []

    for imgList, outDir in zip([mustPassList, toPassList], [mustPassDetectionDir, toPassDetectionDir]):
        total = len(imgList)
        for img in imgList:
            result = detect_left_button_in_image(
                str(img),
                verbose=True,
                display_process=True
            )

            # Save detection result image if available
            if isinstance(result, dict):
                target = result.get('target')
                confidence = result.get('confidence', 0.0)
                display_img = result.get('display_img')
                status = result.get('status', 'unknown')
                target_type = result.get('target_type', 'unknown')
                crop_info = result.get('crop_info', {})


                # Save the display image with detection results
                if display_img is not None:
                    # Save as PNG, use original filename
                    out_path = outDir / img.name
                    cv2.imwrite(str(out_path), display_img)

                if target:
                    # r = target[2]
                    # r_dist.append(r)
                    # print(f"Button Center: {target}")
                    # print(f"Confidence: {confidence:.3f}")

                    # Convert coordinates if crop_info is available
                    if crop_info:
                        original_coords = convert_coordinates_to_original(target, crop_info)
                        if original_coords:
                            # print(f"Original Coordinates: {original_coords}")
                            pass
                    if imgList == mustPassList:
                        mustPass_success_count += 1
                    else:
                        toPass_success_count += 1
                else:
                    print(f"{img.name} No target detected")
                    # pass
                # print(f"Success: {target is not None}")
            else:
                print(f"Unexpected result format: {type(result)}")

            # print(f"\nDetection completed for {img.name}")

    # 汇报成功率
    print("\n==============================")
    print(f"mustPass Success Rate: {mustPass_success_count}/{len(mustPassList)} ({(mustPass_success_count/len(mustPassList))*100:.1f}%)")
    print(f"toPass Success Rate: {toPass_success_count}/{len(toPassList)} ({(toPass_success_count/len(toPassList))*100:.1f}%)")
    print("==============================")
    # print(f"r_dist: {r_dist}")
    # import matplotlib.pyplot as plt
    # import os

    # if r_dist:
    #     plt.figure(figsize=(8, 4))
    #     plt.hist(r_dist, bins=20, color='skyblue', edgecolor='black')
    #     plt.title("Distribution of Detected Button Radii (r_dist)")
    #     plt.xlabel("Radius (pixels)")
    #     plt.ylabel("Frequency")
    #     plt.grid(True, linestyle='--', alpha=0.6)

    #     # Ensure output directory exists
    #     save_dir = "./data/button-debug-saves/leftButton/"
    #     os.makedirs(save_dir, exist_ok=True)
    #     save_path = os.path.join(save_dir, "r_dist.png")
    #     plt.savefig(save_path)
    #     print(f"r_dist histogram saved to {save_path}")
    #     plt.close()
    # else:
    #     print("No radii collected in r_dist to plot.")