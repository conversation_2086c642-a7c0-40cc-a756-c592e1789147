import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status, convert_coordinates_to_original

def detect_right_button_in_image(image_path, verbose=True, display_process=True):
    """
    Detect the right button in a given image.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        dict: Detection results including target, confidence, display image, etc.
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    # Detect right button
    detection_result = detect_buttons_by_status(
        image, 
        status='touching_right_button', 
        verbose=verbose, 
        display_process=display_process
    )
    
    return detection_result
    

if __name__ == "__main__":
    # Detect right button
    from pathlib import Path
    right_button_img_list = Path('./data/button-debug-saves/rightButton/mustPass').glob('*.png')
    right_button_img_list = [img.name for img in right_button_img_list]
    # [
    #     'less_than_4_buttons_colorImage_20250730_094620_886',
    #     'less_than_4_buttons_colorImage_20250730_091940_387',
    #     'all_single_frame_detection_failed_colorImage_20250730_071950_117',
    #     'less_than_4_buttons_colorImage_20250730_071951_608',
    #     'less_than_4_buttons_colorImage_20250730_073402_571',
    #     'less_than_4_buttons_colorImage_20250730_073957_372',
    #     'less_than_4_buttons_colorImage_20250730_074759_405'
    # ]
    total = len(right_button_img_list)
    success_count = 0

    for img_name in right_button_img_list:
        result = detect_right_button_in_image(
            f'./data/button-debug-saves/rightButton/mustPass/{img_name}',
            verbose=True,
            display_process=True
        )

        # Print detection details
        print("\n--- Right Button Detection Results ---")
        if isinstance(result, dict):
            target = result.get('target')
            confidence = result.get('confidence', 0.0)
            display_img = result.get('display_img')
            status = result.get('status', 'unknown')
            target_type = result.get('target_type', 'unknown')
            crop_info = result.get('crop_info', {})

            print(f"Status: {status}")
            print(f"Target Type: {target_type}")
            print(f"Target Found: {target is not None}")
            if target:
                print(f"Button Center: {target}")
                print(f"Confidence: {confidence:.3f}")

                # Convert coordinates if crop_info is available
                if crop_info:
                    original_coords = convert_coordinates_to_original(target, crop_info)
                    if original_coords:
                        print(f"Original Coordinates: {original_coords}")
                success_count += 1
            else:
                print("No target detected")
            print(f"Success: {target is not None}")
        else:
            print(f"Unexpected result format: {type(result)}")

        print(f"\nDetection completed for {img_name}")

    # 汇报成功率
    print("\n==============================")
    print(f"Right Button Detection Success Rate: {success_count}/{total} ({(success_count/total)*100:.1f}%)")
    print("==============================")