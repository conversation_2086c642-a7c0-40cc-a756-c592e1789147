#!/usr/bin/env python3
"""
Button processor module for robotArm project.
Provides integration between buttonControl modules and robotArm architecture.

AUTOMATIC DATA SAVING FEATURE:
This module now includes automatic joint data saving functionality, similar to rtBC.py.
The following actions will automatically save joint data to txt files:

- find_buttons: Saves with prefix 'search_'
- observe_buttons: Saves with prefix 'observation_'  
- touch_left_button: Saves with prefix 'touch_left_'
- touch_right_button: Saves with prefix 'touch_right_'
- touch_knob: Saves with prefix 'touch_knob_'
- finetune: Saves with prefix 'finetune_' (after successful finetune)
- click_button: Saves with prefix 'click_'
- turn_knob: Saves with prefix 'turn_'
- restore: Saves with prefix 'restore_'

Files are saved to the 'data/button-debug-saves' directory with timestamps.
Joint data is saved as: {prefix}joint_{timestamp}.txt
Pose data is saved as: {prefix}pose_{timestamp}.txt
Knob angle data is saved as: {prefix}knobAngle_{timestamp}.txt

The save functionality uses DataManager when available, with a fallback method
when DataManager is not available.
"""

import sys
import os
import threading
import time
import traceback
import numpy as np
import pyrealsense2 as rs
from typing import Tuple, Dict, Any, Optional
import glob
from datetime import datetime


# Add buttonControl to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'buttonControl'))
# Add RM_API2 to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Import robotArm utilities
from utils import *


# Import RM_API2 and pyrealsense2
from RM_API2.Python.Robotic_Arm.rm_robot_interface import RoboticArm, rm_thread_mode_e, rm_robot_arm_model_e, rm_force_type_e

# Import buttonControl modules
try:
    from buttonControl.config import get_config
    BUTTON_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: ButtonControl config module not available: {e}")
    BUTTON_MODULES_AVAILABLE = False

# Import buttonControl.button_action
from buttonControl.button_action import set_observation_checkpoint, set_knob_checkpoint, get_knob_checkpoint
from buttonControl.data_manager import DataManager


class ButtonHandler:
    """
    Button handler class that adapts buttonControl functionality to robotArm architecture.
    
    This class serves as an adapter between the client.py expectations and the 
    buttonControl module implementation.
    """
    
    def __init__(self, camera_instance=None, config=None):
        """
        Initialize button handler.

        Args:
            camera_instance: Optional Camera instance from pole_processor.py
            config: Optional configuration object
        """
        self.camera_instance = camera_instance
        self.emergency_stopped = False
        self.running = False
        self._error_message = ""  # For move_arm error reporting
        self._in_reset_process = False  # Flag to prevent infinite reset loops

        # Initialize status management system (like rtBC)
        self.status = 'uncertain'  # Current status
        self.states = []  # Status history
        self.states.append(self.status)

        # Memory for coordinate system from observing state (like rtBC)
        self.last_coordinate_axes = None  # Store (x_axis, y_axis, z_axis) from observing state
        self.last_coordinate_origin = None  # Store the origin point
        self.last_observing_frame = None  # Frame number when coordinate system was saved

        # Initialize hardware components
        self.arm = None  # RoboticArm instance
        # self.pipeline = None  # RealSense pipeline
        # self.config = None  # RealSense config
        # self.depth_intrin = None  # Depth camera intrinsics
        # self.color_intrin = None  # Color camera intrinsics
        self.robot_config = None  # Robot configuration
        self.data_manager = None  # DataManager instance

        # Initialize if modules are available
        if BUTTON_MODULES_AVAILABLE:
            self._initialize_button_controller()
            print("ButtonHandler initialized successfully")
        else:
            print("Warning: ButtonHandler initialized without buttonControl modules")

        if config is None:
            from buttonControl.config import get_config
            self.config = get_config()
        else:
            self.config = config
    
        self._last_search_checkpoint = None  # Store optimal pose from automatic search

        # Define error codes that should trigger automatic reset
        # All errors except EMERGENCY_STOP_ERROR and SUCCESS_CODE trigger reset
        self._RESET_TRIGGER_ERRORS = {
            ARM_STATE_ERROR,
            ARM_MOVEMENT_ERROR,
            GRIPPER_STATE_ERROR,
            GRIPPER_CONTROL_ERROR,
            ARM_TRAJECTORY_TIMEOUT_ERROR,
            NO_DETECTOR_ERROR,
            BUTTON_DETECTION_ERROR,
            BUTTON_APPROACH_ERROR,
            BUTTON_OPERATION_ERROR,
            FINETUNE_ERROR,
            BUTTON_STATE_ERROR,
            BUTTON_CONFIG_ERROR,
            CAMERA_ERROR,
            MEMORY_ERROR,
            PARSING_ERROR,
            UNKNOWN_ERROR
        }

        # Error codes that should NOT trigger reset
        self._NO_RESET_ERRORS = {
            EMERGENCY_STOP_ERROR,  # Emergency stop should not trigger reset
            SUCCESS_CODE  # Success (including finetune warnings) should not trigger reset
        }

        # Auto-reset delay configuration (in seconds)
        all_config = self.robot_config.get_all_config()
        self._auto_reset_delay = all_config.get('auto_reset_delay', 2.0)
    
    def _initialize_button_controller(self):
        """Initialize hardware components directly."""
        try:
            # Initialize configuration
            self.robot_config = get_config()
            print("ButtonHandler: Configuration loaded")

            # Initialize robotic arm - check if we can share from camera instance
            if self.camera_instance is not None and hasattr(self.camera_instance, 'arm'):
                print("ButtonHandler: Using shared robotic arm from camera instance")
                self.arm = self.camera_instance.arm
                self.handle = self.camera_instance.handle
            else:
                print("ButtonHandler: Initializing independent robotic arm...")
                self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
                self.handle = self.arm.rm_create_robot_arm(
                    self.robot_config.robot_ip,
                    self.robot_config.robot_port
                )
            self.arm_model = getattr(rm_robot_arm_model_e, self.robot_config.arm_model)
            self.force_type = getattr(rm_force_type_e, self.robot_config.force_type)
            
            # Check arm connection
            arm_state = self.arm.rm_get_arm_all_state()
            print(f"ButtonHandler: Arm connection status: {arm_state}")
            current_state = self.arm.rm_get_current_arm_state()
            current_state = self.arm.rm_get_current_arm_state()
            print(f"ButtonHandler: Current arm state: {current_state}")
            self.arm.rm_set_arm_run_mode(1)
            
            # Print configuration parameters
            distances = self.robot_config.get_distances()
            movement = self.robot_config.get_movement_parameters()
            offsets = self.robot_config.get_offsets()
            print(f"ButtonHandler: Distances initialized - Observation: {distances['observation_distance']*1000:.1f}mm, Touch: {distances['touch_distance']*1000:.1f}mm")
            print(f"ButtonHandler: Search distances - Min: {distances['smallest_working_distance']*1000:.1f}mm, Max: {distances['largest_working_distance']*1000:.1f}mm")
            print(f"ButtonHandler: Movement parameters - v: {movement['v']}, r: {movement['r']}")
            print(f"ButtonHandler: Button offsets - Left: {offsets['left_button_offsets']}, Right: {offsets['right_button_offsets']}")
            print(f"ButtonHandler: Zero pose configured: {self.robot_config.zero_pose}")

            # Initialize camera
            if self.camera_instance is not None:
                # Use shared camera instance
                print("ButtonHandler: Using shared camera instance from pole_processor")
                # self.pipeline = self.camera_instance.pipeline
                # self.config = self.camera_instance.config
                self._shared_camera = self.camera_instance

                # # Get intrinsics from the shared camera
                # try:
                #     frames = self.pipeline.wait_for_frames()
                #     depth_frame = frames.get_depth_frame()
                #     color_frame = frames.get_color_frame()

                #     if depth_frame and color_frame:
                #         self.depth_intrin = depth_frame.profile.as_video_stream_profile().intrinsics
                #         self.color_intrin = color_frame.profile.as_video_stream_profile().intrinsics
                #         print("ButtonHandler: Camera intrinsics obtained from shared camera")
                #     else:
                #         print("Warning: Could not get frames from shared camera")
                # except Exception as frame_error:
                #     print(f"Warning: Could not get intrinsics from shared camera: {frame_error}")
            else:
                # Initialize independent camera
                print("ButtonHandler: Initializing independent camera...")
                self._initialize_camera()
                self._shared_camera = None

            # Initialize DataManager
            try:
                
                # Create a mock controller object for DataManager
                class MockController:
                    def __init__(self, button_handler):
                        self.arm = button_handler.arm
                        # self.pipeline = button_handler.pipeline
                        self.camera_instance = button_handler.camera_instance
                        self.status = button_handler.status
                        self.robot_config = button_handler.robot_config
                        self._robust_depth_estimation = button_handler._robust_depth_estimation
                        self.button_handler = button_handler

                    def camera_to_base_transform(self, coords, pose):
                        # Simple passthrough for now - ButtonHandler doesn't use base coordinates
                        return coords
                    
                    def _auto_save_debug_data(self, prefix="", context=""):
                        """Delegate auto-save debug data to ButtonHandler"""
                        return self.button_handler._auto_save_debug_data(prefix, context)
                
                self.data_manager = DataManager(MockController(self))
                print("ButtonHandler: DataManager initialized")
            except ImportError as e:
                print(f"Warning: Could not import DataManager: {e}")
                self.data_manager = None

            print("ButtonHandler: Hardware initialization completed successfully")

        except Exception as e:
            print(f"Error initializing hardware: {e}")
            print(f"Error details: {traceback.format_exc()}")
            self.arm = None
            # self.pipeline = None
            self.camera_instance = None

    def _initialize_camera(self):
        """Initialize RealSense camera independently and wrap as camera_instance."""
        print('ButtonHandler: Preparing camera pipeline...')
        import pyrealsense2 as rs
        import numpy as np

        class SimpleCameraInstance:
            """A minimal camera instance to mimic Camera in pole_processor.py for ButtonHandler."""
            def __init__(self):
                self.pipeline = rs.pipeline()
                self.config = rs.config()
                self.color_image = None
                self.depth_image = None
                self.display_img = None
                self.intr = None
                self.depth_intrin = None
                self.color_intrin = None

        self.camera_instance = SimpleCameraInstance()
        ci = self.camera_instance

        # Configure streams using robot_config settings
        ci.config.enable_stream(
            rs.stream.depth,
            self.robot_config.camera_width,
            self.robot_config.camera_height,
            rs.format.z16,
            self.robot_config.camera_fps
        )
        ci.config.enable_stream(
            rs.stream.color,
            self.robot_config.camera_width,
            self.robot_config.camera_height,
            rs.format.bgr8,
            self.robot_config.camera_fps
        )

        # Start pipeline
        profile = ci.pipeline.start(ci.config)
        print('ButtonHandler: Camera pipeline ready')

        # Reset device
        device = profile.get_device()
        device.hardware_reset()
        print('ButtonHandler: Camera device reset')

        # Initialize frames
        for _ in range(5):
            frames = ci.pipeline.wait_for_frames()
        print('ButtonHandler: Camera frames initialized')

        # Get intrinsics
        frames = ci.pipeline.wait_for_frames()
        depth_frame = frames.get_depth_frame()
        color_frame = frames.get_color_frame()

        ci.depth_intrin = depth_frame.profile.as_video_stream_profile().intrinsics
        ci.color_intrin = color_frame.profile.as_video_stream_profile().intrinsics
        self.depth_intrin = ci.depth_intrin
        self.color_intrin = ci.color_intrin
        print('ButtonHandler: Camera intrinsics obtained')

        # Optionally, grab initial images
        ci.depth_image = np.asanyarray(depth_frame.get_data()) if depth_frame else None
        ci.color_image = np.asanyarray(color_frame.get_data()) if color_frame else None

    def set_status(self, new_status: str, reason: str = None):
        """
        Set the current status and add it to the status history.

        Args:
            new_status: New status to set
            reason: Optional reason for the status change
        """
        old_status = self.status
        self.status = new_status
        self.states.append(new_status)

        print(f"ButtonHandler Status changed: {old_status} -> {new_status}")
        if reason:
            print(f"Reason: {reason}")

        # Limit status history to prevent memory issues
        if len(self.states) > 100:
            self.states = self.states[-50:]  # Keep last 50 states
            print(f"ButtonHandler: Status history trimmed to {len(self.states)} entries")

    def get_status_history(self) -> list:
        """Get the current status history"""
        return self.states.copy()

    def get_current_status(self) -> str:
        """Get the current status"""
        return self.status

    def _validate_action(self, action: str) -> Optional[Tuple[int, Dict[str, Any]]]:
        """
        Validate if the action can be performed in the current status.

        Args:
            action: Action to validate

        Returns:
            None if valid, error tuple if invalid
        """
        current_status = self.status

        # Define valid actions and their required statuses
        action_requirements = {
            'find_buttons': ['uncertain','visible'],
            'observe_buttons': ['uncertain', 'visible', 'observing'],
            'touch_left_button': ['observing'],
            'touch_right_button': ['observing'],
            'touch_knob': ['observing'],
            'finetune': ['touching_left_button', 'touching_right_button', 'touching_knob_left', 'touching_knob_right', 'touching_knob_center'],
            'click_button': ['ready_left_button', 'ready_right_button'],
            'turn_knob': ['ready_knob_left', 'ready_knob_right', 'ready_knob_center'],
            'restore': []  # Special validation for restore
        }

        if action not in action_requirements:
            error_message = f'Unknown action: {action}'
            return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)

        # Special validation for restore
        if action == 'restore':
            if not self._can_restore():
                error_message = 'Cannot restore: no observing state found after last uncertain/stopped state'
                return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)
        else:
            # Standard validation
            required_statuses = action_requirements[action]
            if current_status not in required_statuses:
                error_message = f'Action {action} requires status {required_statuses}, current status is {current_status}'
                return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)

        return None

    def _robust_depth_estimation(self, depth_image, bx, by, br, debug=False):
        """
        Robust depth estimation method required by DataManager.
        
        Args:
            depth_image: Depth image array
            bx, by: Button center coordinates
            br: Button radius
            debug: Whether to print debug info
            
        Returns:
            Depth value in mm or None if failed
        """
        try:
            from buttonControl.coord_ops import robust_depth_estimation
            return robust_depth_estimation(depth_image, bx, by, br, self.status, debug)
        except:
            # Fallback to simple depth lookup
            if 0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]:
                return depth_image[int(by)][int(bx)]
            return None

    def _can_restore(self) -> bool:
        """
        Check if restore operation is allowed.
        Restore is only allowed if there's an 'observing' status after the last 'uncertain' or 'stopped' status.
        """
        # Find the last occurrence of 'uncertain' or 'stopped'
        last_reset_index = -1
        for i in range(len(self.states) - 1, -1, -1):
            if self.states[i] in ['uncertain', 'stopped']:
                last_reset_index = i
                break

        # If no reset state found, check from beginning
        if last_reset_index == -1:
            last_reset_index = 0

        # Check if there's an 'observing' status after the last reset
        for i in range(last_reset_index + 1, len(self.states)):
            if self.states[i] == 'observing':
                return True

        return False

    def _perform_auto_reset(self, error_code: int, error_message: str) -> None:
        """
        Perform automatic reset when an error occurs.

        Args:
            error_code: The error code that triggered the reset
            error_message: The error message
        """
        if self._in_reset_process:
            print(f"ButtonHandler: Skipping auto-reset (already in reset process) for error {error_code}: {error_message}")
            return

        if error_code not in self._RESET_TRIGGER_ERRORS:
            print(f"ButtonHandler: Skipping auto-reset for error {error_code} (not in trigger list)")
            return
        
        if self.emergency_stopped:
            print(f"ButtonHandler: Skipping auto-reset for emergency stop")
            return

        print(f"ButtonHandler: Auto-reset triggered by error {error_code}: {error_message}")

        # Add delay before reset to allow user to see the error message
        if self._auto_reset_delay > 0:
            print(f"ButtonHandler: Waiting {self._auto_reset_delay} seconds before reset...")
            import time
            time.sleep(self._auto_reset_delay)

            # Check emergency stop again after delay
            if self.emergency_stopped:
                print(f"ButtonHandler: Emergency stop detected during auto-reset delay, cancelling auto-reset")
                return

        try:
            # Set flag to prevent infinite loops
            self._in_reset_process = True

            # Perform reset
            reset_result = self.reset()

            if reset_result == SUCCESS_CODE:
                print("ButtonHandler: Auto-reset completed successfully")
            else:
                print(f"ButtonHandler: Auto-reset failed with code {reset_result}")

        except Exception as reset_error:
            print(f"ButtonHandler: Auto-reset failed with exception: {reset_error}")
            print(f"Reset error details: {traceback.format_exc()}")
        finally:
            # Always clear the flag
            self._in_reset_process = False

    def _return_error_with_reset(self, error_code: int, error_message: str, extra_data: dict = None) -> tuple:
        """
        Unified error return method that handles reset triggering automatically.

        Args:
            error_code: The error code to return
            error_message: The error message
            extra_data: Additional data to include in the return tuple

        Returns:
            Tuple of (error_code, error_message, extra_data)
        """
        # Trigger reset if appropriate
        self._perform_auto_reset(error_code, error_message)

        # Return error tuple
        if extra_data is None:
            extra_data = {}
        extra_data['error_message'] = error_message

        return (error_code, extra_data)

    def _collect_action_data(self, multi_frame: bool = True, frames_count: int = None) -> Optional[Dict[str, Any]]:
        """
        Collect data for action execution using DataManager.

        Args:
            multi_frame: Whether to collect multiple frames
            frames_count: Number of frames to collect. If None, uses value from robot_config.

        Returns:
            Dictionary with collected data or None if failed
        """
        # Get frames_count from config if not provided
        if frames_count is None:
            processing = self.robot_config.get_processing_settings()
            frames_count = processing.get('multi_frame_count', 10)
        try:
            print(f"ButtonHandler: Collecting data (multi_frame={multi_frame}, frames={frames_count})")
            
            # Use DataManager if available
            if self.data_manager is not None:
                # Update the mock controller's status
                self.data_manager.controller.status = self.status
                
                # Collect data using DataManager
                data = self.data_manager.collect_data(multi_frame=multi_frame, frames_count=frames_count)
                
                if data is None:
                    print("ButtonHandler: DataManager failed to collect data")
                    # Auto-save debug data when DataManager fails to collect data
                    self._auto_save_debug_data("datamanager_collect_failed_", "datamanager_collect_data_failed")
                    return None
                
                # Convert DataManager format to ButtonHandler format
                import time
                result = {
                    'pose': data.get('pose'),
                    'joint': data.get('joint'),
                    'button_coords': data.get('button_coords_camera', []),
                    'button_labels': data.get('button_labels', []),
                    'knob_coord': data.get('knob_coord_camera'),
                    'knob_angle': data.get('knob_angle'),
                    'point_cloud': data.get('point_cloud'),
                    'is_multi_frame': data.get('is_multi_frame', multi_frame),
                    'valid_frames': data.get('stability_metrics', {}).get('valid_frames', 1),
                    'timestamp': data.get('timestamp', int(time.time())),  # Add timestamp
                    'color_image': data.get('color_image'),
                    'depth_image': data.get('depth_image'),
                    'button_coords_camera': data.get('button_coords_camera', []),
                    'button_coords_base': data.get('button_coords_base', []),
                    'knob_coord_camera': data.get('knob_coord_camera'),
                    'knob_coord_base': data.get('knob_coord_base'),
                    'stability_metrics': data.get('stability_metrics', {})
                }
                
                # Ensure we have the expected button labels
                if len(result['button_labels']) == 0 and len(result['button_coords']) > 0:
                    result['button_labels'] = ['bottom_left', 'bottom_right', 'top_left', 'top_right'][:len(result['button_coords'])]
                
                return result
                
            else:
                # Fallback to original implementation if DataManager not available
                print("ButtonHandler: DataManager not available, using fallback data collection")
                return self._collect_action_data_fallback(multi_frame, frames_count)
            
        except Exception as e:
            print(f"ButtonHandler: Error collecting data: {e}")
            print(f"Error details: {traceback.format_exc()}")
            return None

    def _collect_action_data_fallback(self, multi_frame: bool = True, frames_count: int = 30) -> Optional[Dict[str, Any]]:
        """
        Fallback data collection method when DataManager is not available.
        This is the original implementation.
        Optimization: Default frames_count reduced from 60 to 30 to improve response speed.
        """
        try:
            import pyrealsense2 as rs
            from buttonControl.button_detection import detect_buttons_by_status
            from buttonControl.coord_ops import robust_depth_estimation
            
            # Get current arm state
            ret_msg = self.arm.rm_get_current_arm_state()
            ret_msg = self.arm.rm_get_current_arm_state()
            if ret_msg[0] != 0:
                print("ButtonHandler: Failed to get current arm state")
                return None
                
            current_state = ret_msg[1]
            pose = current_state['pose']
            joint = current_state['joint']
            
            if multi_frame:
                # Multi-frame collection for better stability
                all_button_coords = []
                all_knob_coords = []
                all_knob_angles = []
                valid_frames = 0
                
                for frame_idx in range(frames_count):
                    if self.emergency_stopped:
                        print("ButtonHandler: Emergency stop during data collection")
                        return None
                        
                    # # Get frames
                    # frames = self.pipeline.wait_for_frames()
                    # align = rs.align(rs.stream.color)
                    # aligned_frames = align.process(frames)
                    
                    # color_frame = aligned_frames.get_color_frame()
                    # depth_frame = aligned_frames.get_depth_frame()
                    
                    # if not color_frame or not depth_frame:
                    #     continue
                    
                    # Convert to numpy arrays
                    color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None # np.asanyarray(color_frame.get_data())
                    depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None # np.asanyarray(depth_frame.get_data())
                    
                    # Detect buttons
                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)
                    
                    if button_data and len(button_data) >= 6:
                        _, top_row, bottom_row, knob, handle_angle, _ = button_data
                        
                        # Process buttons
                        if top_row and bottom_row and len(top_row) >= 2 and len(bottom_row) >= 2:
                            frame_button_coords = []
                            
                            # Order: bottom_left, bottom_right, top_left, top_right
                            for button in [bottom_row[0], bottom_row[1], top_row[0], top_row[1]]:
                                bx, by, br = button
                                depth_val = robust_depth_estimation(depth_image, bx, by, br, self.status)
                                
                                if depth_val is not None and depth_val > 0:
                                    point_3d = rs.rs2_deproject_pixel_to_point(
                                        self.camera_instance.color_intrin, (float(bx), float(by)), depth_val / 1000.0
                                    )
                                    frame_button_coords.append(point_3d)
                            
                            if len(frame_button_coords) == 4:
                                all_button_coords.append(frame_button_coords)
                                valid_frames += 1
                        
                        # Process knob
                        if knob:
                            kx, ky, kr = knob
                            depth_val = robust_depth_estimation(depth_image, kx, ky, kr, self.status)
                            
                            if depth_val is not None and depth_val > 0:
                                knob_3d = rs.rs2_deproject_pixel_to_point(
                                    self.camera_instance.color_intrin, (float(kx), float(ky)), depth_val / 1000.0
                                )
                                all_knob_coords.append(knob_3d)
                                
                                if handle_angle is not None:
                                    all_knob_angles.append(handle_angle)
                
                # Calculate averages
                if valid_frames > 0:
                    # Average button coordinates
                    avg_button_coords = np.mean(all_button_coords, axis=0).tolist()
                    button_coords = avg_button_coords
                    
                    # Average knob coordinates
                    knob_coord = None
                    if all_knob_coords:
                        knob_coord = np.mean(all_knob_coords, axis=0).tolist()
                    
                    # Average knob angle
                    knob_angle = None
                    if all_knob_angles:
                        knob_angle = np.mean(all_knob_angles)
                    
                    print(f"ButtonHandler: Multi-frame collection successful ({valid_frames}/{frames_count} valid frames)")
                else:
                    print("ButtonHandler: No valid frames in multi-frame collection")
                    return None
                    
            else:
                # Single frame collection
                # frames = self.pipeline.wait_for_frames()
                # align = rs.align(rs.stream.color)
                # aligned_frames = align.process(frames)
                
                # color_frame = aligned_frames.get_color_frame()
                # depth_frame = aligned_frames.get_depth_frame()
                
                # if not color_frame or not depth_frame:
                #     return None
                
                # Convert to numpy arrays
                color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None # np.asanyarray(color_frame.get_data())
                depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None # np.asanyarray(depth_frame.get_data())
                
                # 使用5次检测循环提高可靠性
                max_detection_attempts = 5
                detection_results = []
                successful_detections = 0

                for attempt in range(max_detection_attempts):
                    # 获取新的图像帧
                    color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None
                    depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None

                    if color_image is None or depth_image is None:
                        continue

                    # Detect buttons
                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)

                    if button_data and len(button_data) >= 6:
                        _, top_row, bottom_row, knob, handle_angle, _ = button_data

                        # 检查检测质量
                        if top_row and bottom_row and len(top_row) >= 2 and len(bottom_row) >= 2:
                            detection_results.append({
                                'button_data': button_data,
                                'top_row': top_row,
                                'bottom_row': bottom_row,
                                'knob': knob,
                                'handle_angle': handle_angle,
                                'color_image': color_image,
                                'depth_image': depth_image,
                                'quality_score': len(top_row) + len(bottom_row) + (1 if knob else 0)
                            })
                            successful_detections += 1

                    # 短暂延迟获取不同帧
                    if attempt < max_detection_attempts - 1:
                        import time
                        time.sleep(0.05)

                # Judge detection success: at least 1 out of 5 successful
                if successful_detections < 1:
                    print(f"Single frame detection failed: only {successful_detections}/5 successful, below threshold 1")
                    return None

                # Select the highest quality detection result
                best_result = max(detection_results, key=lambda x: x['quality_score'])
                _, top_row, bottom_row, knob, handle_angle, _ = best_result['button_data']
                color_image = best_result['color_image']
                depth_image = best_result['depth_image']

                print(f"Single frame detection successful: {successful_detections}/{max_detection_attempts} successful, selected best result (quality score: {best_result['quality_score']})")
                
                # Process buttons
                button_coords = []
                if top_row and bottom_row and len(top_row) >= 2 and len(bottom_row) >= 2:
                    # Order: bottom_left, bottom_right, top_left, top_right
                    for button in [bottom_row[0], bottom_row[1], top_row[0], top_row[1]]:
                        bx, by, br = button
                        depth_val = robust_depth_estimation(depth_image, bx, by, br, self.status)
                        
                        if depth_val is not None and depth_val > 0:
                            point_3d = rs.rs2_deproject_pixel_to_point(
                                self.camera_instance.color_intrin, (float(bx), float(by)), depth_val / 1000.0
                            )
                            button_coords.append(point_3d)
                
                if len(button_coords) < 4:
                    return None
                
                # Process knob
                knob_coord = None
                knob_angle = None
                if knob:
                    kx, ky, kr = knob
                    depth_val = robust_depth_estimation(depth_image, kx, ky, kr, self.status)
                    
                    if depth_val is not None and depth_val > 0:
                        knob_coord = rs.rs2_deproject_pixel_to_point(
                            self.camera_instance.color_intrin, (float(kx), float(ky)), depth_val / 1000.0
                        )
                        knob_angle = handle_angle
            
            # Get point cloud for robust normal calculation
            point_cloud = None
            if self.robot_config.use_robust_normal:
                try:
                    # Get latest depth frame
                    # frames = self.pipeline.wait_for_frames()
                    # depth_frame = frames.get_depth_frame()
                    depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None # np.asanyarray(depth_frame.get_data())
                    
                    if depth_image is not None:
                        # Convert to point cloud
                        pc = rs.pointcloud()
                        points = pc.calculate(depth_image)
                        vertices = np.asanyarray(points.get_vertices())
                        point_cloud = np.array([[v[0], v[1], v[2]] for v in vertices if v[2] > 0])
                except:
                    print("ButtonHandler: Warning - Could not generate point cloud")
            import time
            return {
                'pose': pose,
                'joint': joint,
                'button_coords': button_coords,
                'button_labels': ['bottom_left', 'bottom_right', 'top_left', 'top_right'],
                'knob_coord': knob_coord,
                'knob_angle': knob_angle,
                'point_cloud': point_cloud,
                'is_multi_frame': multi_frame,
                'valid_frames': valid_frames if multi_frame else 1,
                'timestamp': int(time.time()),  # Add timestamp
                'color_image': None,  # Not available in fallback
                'depth_image': None,  # Not available in fallback
                'button_coords_camera': button_coords,  # Same as button_coords for fallback
                'button_coords_base': [],  # Not available in fallback
                'knob_coord_camera': knob_coord,  # Same as knob_coord for fallback
                'knob_coord_base': None,  # Not available in fallback
                'stability_metrics': {
                    'button_detection_rate': 1.0 if len(button_coords) >= 4 else 0.0,
                    'knob_detection_rate': 1.0 if knob_coord is not None else 0.0,
                    'angle_detection_rate': 1.0 if knob_angle is not None else 0.0,
                    'button_std': [0.0] * len(button_coords) if button_coords else [],
                    'knob_std': 0.0 if knob_coord is not None else float('inf'),
                    'angle_std': 0.0 if knob_angle is not None else float('inf'),
                    'valid_frames': valid_frames if multi_frame else 1
                }
            }
            
        except Exception as e:
            print(f"ButtonHandler: Error collecting data: {e}")
            print(f"Error details: {traceback.format_exc()}")
            return None

    def save_collected_data(self, data, prefix=""):
        """
        Save collected data with optional prefix.
        This method mimics the functionality from rtBC.py.

        Args:
            data: Collected data dictionary
            prefix: Optional prefix for saved files

        Returns:
            The data that was saved
        """
        if self.data_manager is not None:
            # Use DataManager to save data
            return self.data_manager.save_collected_data(data, prefix)
        else:
            # Fallback save method when DataManager is not available
            return self._fallback_save_collected_data(data, prefix)
    
    def _fallback_save_collected_data(self, data, prefix=""):
        """
        Fallback save method when DataManager is not available.
        This provides basic saving functionality similar to data_manager.py.
        
        Args:
            data: Collected data dictionary
            prefix: Optional prefix for saved files
            
        Returns:
            The data that was saved
        """
        try:
            import os
            import time
            
            save_dir = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                
            timestamp = int(time.time())
            
            # Save joint data
            if data.get('joint') is not None:
                import numpy as np
                np.savetxt(f"{save_dir}/{prefix}joint_{timestamp}.txt", data['joint'], fmt='%.6f')
                print(f"ButtonHandler: Saved {prefix}joint_{timestamp}.txt")
            
            # Save pose data
            if data.get('pose') is not None:
                np.savetxt(f"{save_dir}/{prefix}pose_{timestamp}.txt", data['pose'], fmt='%.6f')
                print(f"ButtonHandler: Saved {prefix}pose_{timestamp}.txt")
            
            # Save knob angle data
            if data.get('knob_angle') is not None:
                with open(f"{save_dir}/{prefix}knobAngle_{timestamp}.txt", 'w') as f:
                    f.write(f"{data['knob_angle']:.6f}\n")
                print(f"ButtonHandler: Saved {prefix}knobAngle_{timestamp}.txt")
            
            print(f"ButtonHandler: Data saved with prefix '{prefix}' and timestamp {timestamp}")
            return data
            
        except Exception as e:
            print(f"ButtonHandler: Error saving data: {e}")
            return data

    def _save_data_with_info(self, data, prefix=""):
        """
        Save data and return information about what was actually saved.

        Args:
            data: Collected data dictionary
            prefix: Optional prefix for saved files

        Returns:
            Tuple of (saved_data, save_directory, saved_files_dict)
        """
        try:
            if self.data_manager is not None:
                # Use DataManager to save data - it handles directory creation internally
                saved_data = self.data_manager.save_collected_data(data, prefix)

                # Get actual timestamp from data (DataManager uses data['timestamp'])
                timestamp = data.get('timestamp', 'unknown')
                # DataManager creates directory with this pattern
                save_directory = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H%M%S")}'

                # Build comprehensive saved files list for DataManager
                saved_files = self._build_datamanager_file_list(prefix, timestamp, data)

                return saved_data, save_directory, saved_files

            else:
                import time
                # Use fallback save method - it handles directory creation internally
                saved_data = self._fallback_save_collected_data(data, prefix)

                # Fallback method uses int(time.time()) as timestamp
                timestamp = int(time.time())
                # Fallback method creates directory with this pattern
                save_directory = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H%M%S")}'

                # Build saved files list for fallback method
                saved_files = self._build_fallback_file_list(prefix, timestamp, data)

                return saved_data, save_directory, saved_files

        except Exception as e:
            print(f"ButtonHandler: Error in _save_data_with_info: {e}")
            return None, None, {}

    def _auto_save_debug_data(self, prefix="", context=""):
        """
        Automatically save current images and status analysis data for debugging.
        This function is called at key points during button operations.

        Args:
            prefix: Prefix for saved files
            context: Context description for logging

        Returns:
            bool: True if save was successful, False otherwise
        """
        # 添加递归保护机制
        if hasattr(self, '_auto_save_in_progress') and self._auto_save_in_progress:
            print(f"ButtonHandler: Auto-save already in progress, skipping to prevent recursion (context: {context})")
            return False
        
        self._auto_save_in_progress = True
        
        try:
            print(f"ButtonHandler: Auto-saving debug data with prefix '{prefix}' (context: {context})")

            # 使用更安全的数据收集方法，避免递归
            data = self._collect_safe_debug_data()
            if data is None:
                print(f"ButtonHandler: Failed to collect data for auto-save (context: {context})")
                return False
            
            save_dir = f'./data/button-debug-saves/{datetime.now().strftime("%Y%m%d_%H")}'
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                print(f"ButtonHandler: Created directory {save_dir}")

            # Use current timestamp for unique filenames
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds

            # Save images if available
            if data.get('color_image') is not None:
                import cv2
                color_path = f"{save_dir}/{prefix}colorImage_{timestamp}.png"
                cv2.imwrite(color_path, data['color_image'])
                print(f"ButtonHandler: Saved {color_path}")

            if data.get('depth_image') is not None:
                import cv2
                depth_path = f"{save_dir}/{prefix}depthImage_{timestamp}.png"
                cv2.imwrite(depth_path, data['depth_image'])
                print(f"ButtonHandler: Saved {depth_path}")

            # Save status analysis results
            status_info = {
                'timestamp': timestamp,
                'context': context,
                'current_status': self.status,
                'button_coords_detected': len(data.get('button_coords', [])),
                'knob_detected': data.get('knob_coord') is not None,
                'knob_angle': data.get('knob_angle'),
                'pose': data.get('pose'),
                'joint': data.get('joint'),
                'is_multi_frame': data.get('is_multi_frame', False),
                'valid_frames': data.get('valid_frames', 1)
            }

            # Save status analysis as text file
            status_path = f"{save_dir}/{prefix}status_{timestamp}.txt"
            with open(status_path, 'w') as f:
                f.write(f"Auto-save Debug Data\n")
                f.write(f"===================\n")
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Context: {context}\n")
                f.write(f"Current Status: {status_info['current_status']}\n")
                f.write(f"Button Coordinates Detected: {status_info['button_coords_detected']}\n")
                f.write(f"Knob Detected: {status_info['knob_detected']}\n")
                if status_info['knob_angle'] is not None:
                    f.write(f"Knob Angle: {status_info['knob_angle']:.2f}°\n")
                f.write(f"Is Multi-frame: {status_info['is_multi_frame']}\n")
                f.write(f"Valid Frames: {status_info['valid_frames']}\n")
                f.write(f"\nArm State:\n")
                if status_info['pose'] is not None:
                    f.write(f"Pose: {status_info['pose']}\n")
                if status_info['joint'] is not None:
                    f.write(f"Joint: {status_info['joint']}\n")
            print(f"ButtonHandler: Saved {status_path}")

            # Save coordinate data if available
            if data.get('button_coords'):
                import numpy as np
                coords_path = f"{save_dir}/{prefix}buttonCoords_{timestamp}.csv"
                coords_array = np.array(data['button_coords'])
                np.savetxt(coords_path, coords_array, delimiter=',',
                          header='x,y,z', comments='', fmt='%.6f')
                print(f"ButtonHandler: Saved {coords_path}")

            if data.get('knob_coord') is not None:
                import numpy as np
                knob_path = f"{save_dir}/{prefix}knobCoord_{timestamp}.csv"
                knob_array = np.array(data['knob_coord']).reshape(1, -1)
                np.savetxt(knob_path, knob_array, delimiter=',',
                          header='x,y,z', comments='', fmt='%.6f')
                print(f"ButtonHandler: Saved {knob_path}")

            print(f"ButtonHandler: Auto-save completed successfully for context: {context}")
            return True

        except Exception as e:
            print(f"ButtonHandler: Error in auto-save debug data at {context}: {e}")
            print(f"ButtonHandler: Error details: {traceback.format_exc()}")
            return False
        finally:
            # 确保递归保护标志被重置
            self._auto_save_in_progress = False

    def _collect_safe_debug_data(self):
        """
        安全的数据收集方法，避免递归调用
        """
        try:
            # 直接使用相机获取图像，不经过DataManager
            if self.camera_instance is None:
                return None
            
            # 获取当前图像
            color_image = self.camera_instance.get_color_image()
            depth_image = self.camera_instance.get_depth_image()
            
            if color_image is None or depth_image is None:
                return None
            
            # 获取机械臂状态
            pose = None
            joint = None
            try:
                ret_msg = self.arm.rm_get_current_arm_state()
                if ret_msg[0] == 0:
                    current_state = ret_msg[1]
                    pose = current_state['pose']
                    joint = current_state['joint']
            except:
                pass
            
            return {
                'color_image': color_image,
                'depth_image': depth_image,
                'pose': pose,
                'joint': joint,
                'button_coords': [],
                'knob_coord': None,
                'knob_angle': None,
                'is_multi_frame': False,
                'valid_frames': 1
            }
            
        except Exception as e:
            print(f"ButtonHandler: Error in safe debug data collection: {e}")
            return None

    def _build_datamanager_file_list(self, prefix, timestamp, data):
        """Build file list for DataManager saved files"""
        saved_files = {}

        # Images (if available)
        if data.get('color_image') is not None:
            saved_files['color_image'] = f"{prefix}colorImage_{timestamp}.png"
        if data.get('depth_image') is not None:
            saved_files['depth_image'] = f"{prefix}depthImage_{timestamp}.png"

        # Button coordinates (always saved, even if empty)
        saved_files['button_coords_camera'] = f"{prefix}buttonCoords_camera_{timestamp}.csv"
        saved_files['button_coords_base'] = f"{prefix}buttonCoords_base_{timestamp}.csv"

        # Knob data (always saved, even if empty)
        saved_files['knob_coord_camera'] = f"{prefix}knobCoord_camera_{timestamp}.csv"
        saved_files['knob_coord_base'] = f"{prefix}knobCoord_base_{timestamp}.csv"
        saved_files['knob_angle'] = f"{prefix}knobAngle_{timestamp}.txt"

        # Arm state
        if data.get('pose') is not None:
            saved_files['pose'] = f"{prefix}pose_{timestamp}.txt"
        if data.get('joint') is not None:
            saved_files['joint'] = f"{prefix}joint_{timestamp}.txt"

        # Metadata
        saved_files['coordinate_system_info'] = f"{prefix}coordinate_system_info_{timestamp}.txt"
        if data.get('is_multi_frame', False):
            saved_files['stability_metrics'] = f"{prefix}stability_{timestamp}.txt"

        return saved_files

    def _build_fallback_file_list(self, prefix, timestamp, data):
        """Build file list for fallback saved files"""
        saved_files = {}
        if data.get('joint') is not None:
            saved_files['joint'] = f"{prefix}joint_{timestamp}.txt"
        if data.get('pose') is not None:
            saved_files['pose'] = f"{prefix}pose_{timestamp}.txt"
        if data.get('knob_angle') is not None:
            saved_files['knob_angle'] = f"{prefix}knobAngle_{timestamp}.txt"

        return saved_files

    def debug_capture_current_view(self, params=None):
        """
        Debug function to capture current view and save detection results.
        This function collects data from current camera view without moving the arm,
        performs button/knob detection, and saves the results.

        Returns:
            Tuple of (result_code, result_data) where result_data contains:
            - save_directory: Directory where files were saved
            - timestamp: Timestamp of the capture
            - detection_results: Detection results (buttons, knob, etc.)
            - saved_files: List of saved file names
            - arm_state: Current arm pose and joint angles
        """
        try:
            save_prefix = params.get("save_prefix", "debug_") if params else "debug_"
            print(f"ButtonHandler: Starting debug capture with prefix '{save_prefix}'")

            # Collect current view data (single frame, no multi-frame for debug)
            data = self._collect_action_data(multi_frame=False, frames_count=1)
            if data is None:
                error_message = "Failed to collect current view data - check camera and arm connections"
                print(f"ButtonHandler: {error_message}")
                return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message, {
                    "save_directory": None,
                    "timestamp": None,
                    "detection_results": {},
                    "saved_files": {},
                    "arm_state": {}
                })

            # Save the collected data and get actual save info
            saved_data, actual_save_dir, actual_saved_files = self._save_data_with_info(data, save_prefix)
            if saved_data is None:
                error_message = "Failed to save collected data"
                print(f"ButtonHandler: {error_message}")
                return (MEMORY_ERROR, {
                    "error_message": error_message,
                    "save_directory": None,
                    "timestamp": data.get('timestamp'),
                    "detection_results": {},
                    "saved_files": {},
                    "arm_state": {}
                })

            # Format detection results
            detection_results = {
                "buttons_detected": len(data.get('button_coords', [])),
                "button_coords": data.get('button_coords', []),
                "button_labels": data.get('button_labels', []),
                "knob_detected": data.get('knob_coord') is not None,
                "knob_coord": data.get('knob_coord'),
                "knob_angle": data.get('knob_angle')
            }

            # Format arm state
            arm_state = {
                "pose": data.get('pose'),
                "joint": data.get('joint')
            }
            import time
            # Use actual save directory and files from save operation
            timestamp = data.get('timestamp', int(time.time()))

            result_data = {
                "save_directory": actual_save_dir,
                "timestamp": timestamp,
                "detection_results": detection_results,
                "saved_files": actual_saved_files,
                "arm_state": arm_state
            }

            print(f"ButtonHandler: Debug capture completed successfully")
            print(f"ButtonHandler: Detected {detection_results['buttons_detected']} buttons")
            print(f"ButtonHandler: Knob detected: {detection_results['knob_detected']}")
            if detection_results['knob_detected'] and detection_results['knob_angle'] is not None:
                print(f"ButtonHandler: Knob angle: {detection_results['knob_angle']:.1f}°")

            return (SUCCESS_CODE, result_data)

        except Exception as e:
            import traceback
            error_message = f"Exception during debug capture: {str(e)}"
            print(f"ButtonHandler: {error_message}")
            print(f"ButtonHandler: Error details: {traceback.format_exc()}")
            return self._return_error_with_reset(UNKNOWN_ERROR, error_message, {
                "save_directory": None,
                "timestamp": None,
                "detection_results": {},
                "saved_files": {},
                "arm_state": {}
            })

    def _execute_action(self, action: str, extra_params: dict = None) -> tuple:
        """
        Execute the specific action by calling bottom-level functions directly.

        Args:
            action: Action to execute
            extra_params: dict, additional parameters (such as checkpoint, checkpoint_bool)

        Returns:
            Tuple of (error_code, error_message, extra_data). SUCCESS_CODE if successful.
        """
        # Check emergency stop before executing any action
        if self.emergency_stopped:
            return (EMERGENCY_STOP_ERROR, "Emergency stop active before action execution", {})
        if extra_params is None:
            extra_params = {}
        try:
            print(f"ButtonHandler: Executing action '{action}'")

            if action == 'find_buttons':
                from buttonControl.button_action import search_targets
                print("ButtonHandler: Starting target search...")

                distances = self.robot_config.get_distances()
                search_params = self.robot_config.get_search_parameters()

                # Handle known_orientation based on checkpoint_bool logic
                if extra_params.get('force_search', False):
                    # When checkpoint_bool is True, force re-search by setting known_orientation to None
                    known_orientation = None
                    print("ButtonHandler: Force search mode - setting known_orientation to None")
                else:
                    # When checkpoint_bool is False, use checkpoint if provided, otherwise use config
                    known_orientation = extra_params.get('checkpoint', None)
                    if known_orientation is None:
                        known_orientation = self.config.get_known_orientation()
                    if known_orientation is not None:
                        print(f"ButtonHandler: Using provided/config known_orientation: {known_orientation}")
                    else:
                        print("ButtonHandler: No known_orientation available, will perform search")
                # 调用search_targets
                success, result_dict = search_targets(
                    button_handler=self,
                    smallest_working_distance=distances['smallest_working_distance'],
                    largest_working_distance=distances['largest_working_distance'],
                    joint5_angle=search_params['joint5_angle'],
                    known_orientation=known_orientation
                )
                if success:

                    self.set_status('visible', 'Target search completed successfully')
                    confidence = result_dict.get('detection_confidence', 0.0)
                    distance_camera = result_dict.get('mean_distance_camera', 0.0)
                    distance_base = result_dict.get('mean_distance_base', 0.0)

                    print(f"ButtonHandler: Search successful! Confidence: {confidence:.3f}, Distance to base: {distance_base:.3f}m, Distance to camera: {distance_camera:.3f}m")
                    # 记录自动搜索得到的最优位姿
                    best_joint = result_dict.get('best_joint')
                    if best_joint is not None:
                        self._last_search_checkpoint = list(best_joint)
                    else:
                        self._last_search_checkpoint = None
                    return (SUCCESS_CODE, "Target search completed successfully", {'checkpoint': self._last_search_checkpoint})
                else:
                    self._auto_save_debug_data("find_buttons_failed_", "find_button_failed")
                    self._last_search_checkpoint = None
                    error_message = "Search failed - no targets found"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

            elif action == 'observe_buttons':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button
                import time
                time.sleep(1)
                print("ButtonHandler: Executing observation approach...")

                # Collect data using configured multi_frame_count
                data = self._collect_action_data(multi_frame=True)
                if not data:
                    self._auto_save_debug_data("observe_buttons_data_collection_failed_", "observe_buttons_data_collection_failed")
                    error_message = "Failed to collect data for observation approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

                # Check button count
                if len(data['button_coords']) < 4:
                    self._auto_save_debug_data("observe_buttons_insufficient_buttons_", f"observe_buttons_insufficient_buttons_{len(data['button_coords'])}")
                    error_message = f"Insufficient buttons detected ({len(data['button_coords'])})"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                print('observe_buttons succeed, saving data...')
                self._auto_save_debug_data("observe_buttons_success_", "observe_buttons_success")

                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                
                # Call approach_button with safe error handling
                try:
                    approach_button(
                        self.arm,
                        data['pose'],
                        data['joint'],
                        data['button_coords'],
                        distances['observation_distance'],
                        camera_up=True,
                        robust_normal=processing['use_robust_normal'],
                        point_cloud=data['point_cloud'],
                        method='movej',
                        v=movement['v'],
                        r=movement['r'],
                        connect=movement['connect'],
                        block=movement['block'],
                        target='centers',
                        offset_x=0.0,
                        offset_y=0.0,
                        knob_coord=data['knob_coord'],
                        estimate_knob=True,
                        button_handler=self
                    )
                except Exception as e:
                    # Safe error conversion - maintain original behavior while providing better error codes
                    error_message = str(e)
                    if "Emergency stop" in error_message or "emergency" in error_message.lower():
                        error_code = EMERGENCY_STOP_ERROR
                    elif "detection" in error_message.lower() or "detect" in error_message.lower() or "insufficient" in error_message.lower():
                        error_code = BUTTON_DETECTION_ERROR
                    elif "approach" in error_message.lower() or "movement" in error_message.lower() or "move" in error_message.lower():
                        error_code = BUTTON_APPROACH_ERROR
                    else:
                        # Default to approach error for observation action
                        error_code = BUTTON_APPROACH_ERROR
                        error_message = f"Observation approach failed: {error_message}"

                    return self._return_error_with_reset(error_code, error_message)
                
                # Update status and save coordinate axes
                self.set_status('observing', 'Observation approach completed successfully')
                self._save_coordinate_axes_from_observation()

                return (SUCCESS_CODE, "Observation approach completed successfully", {})

            elif action == 'touch_left_button':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonHandler: Executing touch approach to left button...")

                # Collect data using configured multi_frame_count
                data = self._collect_action_data(multi_frame=True)
                if not data:
                    self._auto_save_debug_data("touch_left_button_failed_", "touch_left_button_failed")
                    error_message = "Failed to collect data for touch approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                print('touch_left_button succeed, saving data...')
                self._auto_save_debug_data("touch_left_button_success_", "touch_left_button_success")

                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get left button offsets
                offset_x, offset_y = offsets['left_button_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='left',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Update status
                self.set_status('touching_left_button', 'Touch approach to left button completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])
                set_knob_checkpoint(data.get('knob_angle'))  # 新增

                return (SUCCESS_CODE, "Touch approach to left button completed", {})

            elif action == 'touch_right_button':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonHandler: Executing touch approach to right button...")

                # Collect data using configured multi_frame_count
                data = self._collect_action_data(multi_frame=True)
                if not data:
                    self._auto_save_debug_data("touch_right_button_failed_", "touch_right_button_failed")
                    error_message = "Failed to collect data for touch approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                print('touch_right_button succeed, saving data...')
                self._auto_save_debug_data("touch_right_button_success_", "touch_right_button_success")
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get right button offsets
                offset_x, offset_y = offsets['right_button_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='right',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Update status
                self.set_status('touching_right_button', 'Touch approach to right button completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])
                set_knob_checkpoint(data.get('knob_angle'))  # 新增

                return (SUCCESS_CODE, "Touch approach to right button completed", {})

            elif action == 'touch_knob':
                # Import approach_button from button_action
                from buttonControl.button_action import approach_button

                print("ButtonHandler: Executing touch approach to knob...")

                # Collect data using configured multi_frame_count
                data = self._collect_action_data(multi_frame=True)
                if not data:
                    self._auto_save_debug_data("touch_knob_data_collection_failed_", "touch_knob_data_collection_failed")
                    error_message = "Failed to collect data for knob approach"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)

                print('touch_knob succeed, saving data...')
                self._auto_save_debug_data("touch_knob_success_", "touch_knob_success")

                # Check if knob is detected
                if data['knob_coord'] is None:
                    error_message = "No knob detected"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                processing = self.robot_config.get_processing_settings()
                offsets = self.robot_config.get_offsets()
                
                # Get knob offsets
                offset_x, offset_y = offsets['knob_offsets']
                
                # Call approach_button
                approach_button(
                    self.arm,
                    data['pose'],
                    data['joint'],
                    data['button_coords'],
                    distances['touch_distance'],
                    camera_up=True,
                    robust_normal=processing['use_robust_normal'],
                    point_cloud=data['point_cloud'],
                    method='movej',
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    target='knob',
                    offset_x=offset_x,
                    offset_y=offset_y,
                    knob_coord=data['knob_coord'],
                    button_handler=self
                )
                
                # Determine knob position based on angle
                knob_angle = data.get('knob_angle')
                knob_position = self._determine_knob_position_from_angle(knob_angle)
                
                # Update status based on knob position
                self.set_status(f'touching_knob_{knob_position}', f'Touch approach to knob ({knob_position}) completed')

                # 设置observation_checkpoint
                set_observation_checkpoint(data['joint'])
                set_knob_checkpoint(data.get('knob_angle'))  # 新增

                return (SUCCESS_CODE, f"Touch approach to knob ({knob_position}) completed", {})

            elif action == 'finetune':
                # Import finetune functions
                from buttonControl.finetune import perform_finetune_for_status
                # import time
                # time.sleep(0.5)

                print("ButtonHandler: Executing finetune operation...")

                # Check if coordinate axes are available
                if self.last_coordinate_axes is None:
                    error_message = "No coordinate axes available for finetune"
                    return self._return_error_with_reset(FINETUNE_ERROR, error_message)
                
                # Create a mock controller object with necessary attributes
                class MockController:
                    def __init__(self, button_handler):
                        self.status = button_handler.status
                        self.last_coordinate_axes = button_handler.last_coordinate_axes
                        self.last_coordinate_origin = button_handler.last_coordinate_origin
                        self.robot_config = button_handler.robot_config
                        self.arm = button_handler.arm
                        # self.pipeline = button_handler.pipeline
                        self.camera_instance = button_handler.camera_instance
                        self.move_arm = button_handler.move_arm
                        self.button_handler = button_handler
                        self._robust_depth_estimation = button_handler._robust_depth_estimation

                        # Optimization: Reuse existing DataManager instance instead of creating new one each time
                        if button_handler.data_manager is not None:
                            print("ButtonHandler: Reusing existing DataManager for finetune")
                            self.data_manager = button_handler.data_manager
                        else:
                            # Use the actual DataManager from buttonControl
                            try:
                                

                                # Create a proper controller object for DataManager
                                class FinetuneController:
                                    def __init__(self, button_handler):
                                        self.arm = button_handler.arm
                                        self.camera_instance = button_handler.camera_instance
                                        self.status = button_handler.status
                                        self.robot_config = button_handler.robot_config
                                        self._robust_depth_estimation = button_handler._robust_depth_estimation
                                        self.button_handler = button_handler

                                    def camera_to_base_transform(self, coords, pose):
                                        # Simple passthrough for now - ButtonHandler doesn't use base coordinates
                                        return coords

                                    def _auto_save_debug_data(self, prefix="", context=""):
                                        """Delegate auto-save debug data to ButtonHandler"""
                                        return self.button_handler._auto_save_debug_data(prefix, context)

                                finetune_controller = FinetuneController(button_handler)
                                self.data_manager = DataManager(finetune_controller)
                                print("ButtonHandler: Created new DataManager for finetune")

                            except ImportError as e:
                                raise ImportError(f"Could not import DataManager: {e}")
                        
                        # Create _robust_depth_estimation method
                        self._robust_depth_estimation = lambda depth_image, bx, by, br, debug=False: self._robust_depth_estimation_impl(depth_image, bx, by, br, debug)

                    def _auto_save_debug_data(self, prefix="", context=""):
                        """Delegate auto-save debug data to ButtonHandler"""
                        return self.button_handler._auto_save_debug_data(prefix, context)

                    def _robust_depth_estimation_impl(self, depth_image, bx, by, br, debug=False):
                        """Simple robust depth estimation"""
                        try:
                            from buttonControl.coord_ops import robust_depth_estimation
                            return robust_depth_estimation(depth_image, bx, by, br, self.status, debug)
                        except:
                            # Fallback to simple depth lookup
                            if 0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]:
                                return depth_image[int(by)][int(bx)]
                            return None
                
                mock_controller = MockController(self)
                
                # Perform finetune with exception handling
                import time
                finetune_start_time = time.time()
                try:
                    success, message = perform_finetune_for_status(mock_controller)
                    finetune_end_time = time.time()
                    finetune_duration = finetune_end_time - finetune_start_time
                    print(f"ButtonHandler: Finetune completed in {finetune_duration:.2f} seconds")
                except Exception as e:
                    finetune_end_time = time.time()
                    finetune_duration = finetune_end_time - finetune_start_time
                    print(f"ButtonHandler: Finetune failed after {finetune_duration:.2f} seconds")
                    print(f"ButtonHandler: Exception during operation 'finetune': {e}")
                    # Report error directly in exception case
                    error_message = f"Finetune failed with exception: {e}"
                    return self._return_error_with_reset(FINETUNE_ERROR, error_message)
                
                # 根据finetune失败的原因决定是报错还是警告
                if success:
                    ready_status = self._get_ready_status_from_touching()
                    self.set_status(ready_status, 'Finetune completed successfully')
                    print(f"ButtonHandler: Finetune successful - {message}")
                    return (SUCCESS_CODE, f"Finetune completed successfully - {message}", {})
                else:
                    # Analyze failure reason, decide whether to report error or warning
                    message_lower = message.lower()

                    # Serious errors: should report error and trigger reset
                    if any(error_keyword in message_lower for error_keyword in [
                        'insufficient detection data',  # Insufficient detection data
                        'not supported for status',     # Status not supported
                        'no coordinate axes',           # Missing coordinate axes
                        'movement failed',              # Movement failed
                        'detection failed',             # Detection failed
                        'failed at iteration'           # Iteration failed (usually serious problem)
                    ]):
                        error_message = f"Finetune failed: {message}"
                        return self._return_error_with_reset(FINETUNE_ERROR, error_message)

                    # Distance accuracy issues: warning but no error
                    else:
                        # Only warn without error when distance accuracy is insufficient
                        ready_status = self._get_ready_status_from_touching()
                        self.set_status(ready_status, f'Finetune completed with warnings - {message}')
                        print(f"ButtonHandler: Finetune completed with warnings - {message}")
                        print("Warning: Finetune not completely successful, but status changed to ready_*")
                        return (SUCCESS_CODE, f"Finetune completed with warnings - {message}", {})

            elif action == 'click_button':
                # Import click_button from button_action
                from buttonControl.button_action import click_button
                
                print("ButtonHandler: Executing button click...")
                # print('before click_button, saving data...')
                # self._auto_save_debug_data("before_click_button_", "before_click_button")
                
                # # Collect data before clicking
                # data = self._collect_action_data(multi_frame=False, frames_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'click_')
                
                # Get click depth from config
                distances = self.robot_config.get_distances()
                
                # Call click_button
                click_button(
                    self.arm,
                    distances['click_depth'],
                    button_handler=self
                )
                
                print("ButtonHandler: Button click completed")
                # Final emergency stop check
                if self.emergency_stopped:
                    return (EMERGENCY_STOP_ERROR, "Emergency stop during button click", {})
                return (SUCCESS_CODE, "Button click completed", {})

            elif action == 'turn_knob':
                # Import turn_knob from button_action
                from buttonControl.button_action import turn_knob
                
                print("ButtonHandler: Executing knob turn...")
                # print('before turn_knob, saving data...')
                # self._auto_save_debug_data("before_turn_knob_", "before_turn_knob")
                # # Collect data before turning
                # data = self._collect_action_data(multi_frame=False, frames_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'turn_')
                
                # Get knob angle from latest data
                knob_angle = self._get_latest_knob_angle()
                if knob_angle is None:
                    error_message = "No knob angle data available"
                    return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_message)
                
                print(f"ButtonHandler: Got knob angle: {knob_angle:.1f}°")
                
                # Get configuration parameters
                distances = self.robot_config.get_distances()
                movement = self.robot_config.get_movement_parameters()
                
                # Call turn_knob
                turn_knob(
                    self.arm,
                    knob_angle,
                    distances['turn_depth'],
                    v=movement['v'],
                    r=movement['r'],
                    connect=movement['connect'],
                    block=movement['block'],
                    button_handler=self
                )
                
                print("ButtonHandler: Knob turn completed")
                # Final emergency stop check
                if self.emergency_stopped:
                    return (EMERGENCY_STOP_ERROR, "Emergency stop during knob turn", {})
                return (SUCCESS_CODE, "Knob turn completed", {})

            elif action == 'restore':
                # Import restore_pose from button_action
                from buttonControl.button_action import restore_pose
                
                print("ButtonHandler: Executing restore to initial position...")
                
                # # Collect data before restoring
                # data = self._collect_action_data(multi_frame=False, frames_count=10)
                # if data:
                #     # Save collected data
                #     self.save_collected_data(data, 'restore_')
                
                try:
                    # Call restore_pose
                    restore_pose(self.arm)
                    
                    # Update status to uncertain
                    self.set_status('uncertain', 'Restored to initial position')
                    
                    print("ButtonHandler: Restore completed successfully")
                    # Final emergency stop check
                    if self.emergency_stopped:
                        return (EMERGENCY_STOP_ERROR, "Emergency stop during restore", {})
                    return (SUCCESS_CODE, "Restore completed successfully", {})

                except ValueError as e:
                    if "observation joint" in str(e):
                        error_message = "No historical position file found"
                        return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)
                    else:
                        error_message = f"Restore failed - {e}"
                        return self._return_error_with_reset(BUTTON_OPERATION_ERROR, error_message)

            else:
                error_message = f"Unknown action '{action}'"
                return self._return_error_with_reset(BUTTON_STATE_ERROR, error_message)

        except Exception as e:
            print(f"ButtonHandler: Error executing action '{action}': {e}")
            print(f"Error details: {traceback.format_exc()}")
            error_message = str(e)
            error_lower = error_message.lower()

            # Determine error code based on error message
            if "emergency stop" in error_lower or "emergency" in error_lower:
                error_code = EMERGENCY_STOP_ERROR
            elif "detection" in error_lower or "detect" in error_lower or "insufficient" in error_lower:
                error_code = BUTTON_DETECTION_ERROR
            elif "approach" in error_lower or "movement" in error_lower or "move" in error_lower:
                error_code = BUTTON_APPROACH_ERROR
            elif "click" in error_lower or "turn" in error_lower:
                error_code = BUTTON_OPERATION_ERROR
            elif "finetune" in error_lower:
                error_code = FINETUNE_ERROR
            elif "state" in error_lower or "status" in error_lower or "invalid" in error_lower:
                error_code = BUTTON_STATE_ERROR
            elif "config" in error_lower:
                error_code = BUTTON_CONFIG_ERROR
            else:
                error_code = BUTTON_OPERATION_ERROR
                error_message = f"Error executing action '{action}': {error_message}"

            # Use unified error return method
            return self._return_error_with_reset(error_code, error_message)

    def _get_current_knob_angle(self) -> float:
        """
        Get the current knob angle from real-time detection.
        Returns None if no angle data is available.
        """
        try:
            if self.camera_instance is None or self.camera_instance.color_image is None:
                print("Warning: No camera pipeline available for knob angle detection")
                return None
            
            # Convert to numpy array
            color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None # np.asanyarray(color_frame.get_data())
            
            # 使用5次快速检测循环提高实时角度检测可靠性
            try:
                from buttonControl.button_detection import detect_buttons_by_status
                import numpy as np

                max_detection_attempts = 5
                detected_angles = []

                for attempt in range(max_detection_attempts):
                    # 获取新的图像帧
                    color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None

                    if color_image is None:
                        continue

                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)

                    if button_data and len(button_data) >= 6:
                        _, _, _, knob, handle_angle, _ = button_data

                        if handle_angle is not None:
                            detected_angles.append(handle_angle)

                    # 快速延迟以保持实时性
                    if attempt < max_detection_attempts - 1:
                        import time
                        time.sleep(0.05)  # 20ms延迟，保持实时性

                if len(detected_angles) >= 1:  # At least 3 out of 5 successful
                    # Outlier filtering: Remove values that differ too much from median
                    median_angle = np.median(detected_angles)
                    filtered_angles = [angle for angle in detected_angles
                                     if abs(angle - median_angle) <= 30]  # 30 degree threshold

                    if filtered_angles:
                        # Use average as final result
                        final_angle = np.mean(filtered_angles)
                        print(f"ButtonHandler: Real-time knob angle detected: {final_angle:.1f}° (based on {len(filtered_angles)}/{len(detected_angles)} valid detections)")
                        return final_angle
                    else:
                        print(f"Warning: All detected angles were outliers (median: {median_angle:.1f}°)")
                        return median_angle  # Return median as fallback
                else:
                    print(f"Warning: Real-time angle detection failed, only {len(detected_angles)}/5 successful")
                    print('try to retrieve knob angle from previous record')
                    knob_angle = self._get_latest_knob_angle()
                    if knob_angle is not None:
                        print(f"ButtonHandler: Got knob angle from global variable: {knob_angle:.1f}°")
                        return knob_angle
                    else:
                        print("Warning: No valid knob angle data in global variable")
                    return None

            except Exception as detection_error:
                print(f"Warning: Could not detect buttons for knob angle: {detection_error}")
                return None
                
        except Exception as e:
            print(f"Warning: Could not get current knob angle: {e}")
            return None

    def _get_latest_knob_angle(self) -> float:
        """
        Get the latest knob angle from global variable.
        Returns None if no angle data is available.
        """
        knob_angle = get_knob_checkpoint()
        if knob_angle is None:
            print("Warning: No knob angle data in global variable")
        else:
            print(f"Read knob angle from global variable: {knob_angle:.1f}°")
        return knob_angle

    def _determine_knob_position_from_angle(self, knob_angle: float) -> str:
        """
        Determine knob position based on angle detection results.
        Uses the same logic as rtBC.py for consistency.
        """
        if knob_angle is None:
            # Default to center if no angle data
            return 'left'

        # Use same logic as rtBC.py
        if knob_angle < -30:
            return 'left'
        elif knob_angle > 30:
            return 'right'
        elif -10 <= knob_angle <= 10:
            return 'center'
        else:
            # For angles between -30 to -10 or 10 to 30, default to center
            return 'left'

    def _determine_knob_position(self) -> str:
        """
        Determine knob position based on detection results.
        This is a fallback method that defaults to center.
        """
        return 'center'

    def _save_coordinate_axes_from_observation(self):
        """
        Save coordinate axes after successful observation approach.
        This simulates the coordinate axes saving that happens in rtBC's main loop.
        """
        try:
            # Get current frames from camera
            if self.camera_instance is not None and self.camera_instance.color_image is not None:
                # frames = self.pipeline.wait_for_frames()
                # depth_frame = frames.get_depth_frame()
                # color_frame = frames.get_color_frame()

                # if depth_frame and color_frame:
                    # Convert to numpy arrays
                depth_image = self.camera_instance.depth_image.copy() if self.camera_instance.depth_image is not None else None # np.asanyarray(depth_frame.get_data())
                color_image = self.camera_instance.color_image.copy() if self.camera_instance.color_image is not None else None # np.asanyarray(color_frame.get_data())

                # Get intrinsics
                color_intrin = self.camera_instance.color_intrin if self.camera_instance.color_intrin is not None else None # color_frame.profile.as_video_stream_profile().intrinsics

                # Detect buttons for coordinate system calculation
                try:
                    # Import button detection
                    from buttonControl.button_detection import detect_buttons_by_status

                    # Detect buttons
                    button_data = detect_buttons_by_status(color_image, status=self.status, verbose=False, display_process=False)

                    if button_data and len(button_data) >= 6:
                        _, top_row, bottom_row, _, _, _ = button_data

                        # Check if we have sufficient buttons
                        if (top_row is not None and bottom_row is not None and
                            len(top_row) >= 2 and len(bottom_row) >= 2):

                            # Calculate coordinate system using the same logic as rtBC
                            self._calculate_coordinate_system(
                                top_row, bottom_row, depth_image, color_intrin
                            )

                            print("ButtonHandler: Coordinate axes saved from observation")
                        else:
                            print("Warning: Insufficient buttons detected for coordinate system calculation")
                    else:
                        print("Warning: Button detection failed during observation")

                except Exception as detection_error:
                    print(f"Warning: Could not detect buttons for coordinate system: {detection_error}")
                # else:
                #     print("Warning: Could not get frames for coordinate system calculation")
            else:
                print("Warning: No camera pipeline available for coordinate system calculation")

        except Exception as e:
            print(f"Warning: Could not save coordinate axes from observation: {e}")

    def _calculate_coordinate_system(self, top_row, bottom_row, depth_image, color_intrin):
        """
        Calculate coordinate system from button positions.
        Uses the same logic as rtBC._save_coordinate_axes_if_observing.
        """
        try:
            # Convert button positions to 3D coordinates
            button_coords_3d = []
            button_labels = []

            # Process detected buttons for coordinate calculation
            for button, label in [(bottom_row[0], "bottom_left"), (bottom_row[1], "bottom_right"),
                                 (top_row[0], "top_left"), (top_row[1], "top_right")]:
                bx, by, br = button
                if 0 <= int(by) < depth_image.shape[0] and 0 <= int(bx) < depth_image.shape[1]:
                    # Use simple depth lookup (could be enhanced with robust estimation)
                    depth_val = depth_image[int(by)][int(bx)]
                    if depth_val is not None and depth_val > 0:
                        point_3d = rs.rs2_deproject_pixel_to_point(
                            color_intrin, (float(bx), float(by)), depth_val / 1000.0
                        )
                        button_coords_3d.append(point_3d)
                        button_labels.append(label)

            # Need exactly 4 buttons to calculate coordinate system
            if len(button_coords_3d) == 4:
                try:
                    # Import button_action for coordinate system calculation
                    from buttonControl.button_action import build_object_coordinate_system

                    # Calculate coordinate system axes
                    x_axis, y_axis, z_axis = build_object_coordinate_system(
                        button_coords_3d[0],  # bottom_left
                        button_coords_3d[1],  # bottom_right
                        button_coords_3d[2],  # top_left
                        button_coords_3d[3],  # top_right
                        robust_normal=True,  # Use robust normal calculation
                        point_cloud=None
                    )

                    # Calculate origin as center of buttons (camera coordinates)
                    origin_camera = np.mean(button_coords_3d, axis=0)

                    # Store the coordinate axes and origin
                    self.last_coordinate_axes = (x_axis, y_axis, z_axis)
                    self.last_coordinate_origin = origin_camera
                    self.last_observing_frame = 0  # Frame counter not available in button_processor

                    print(f"ButtonHandler: Coordinate system calculated successfully")
                    print(f"  Origin: {origin_camera}")

                except Exception as calc_error:
                    print(f"Warning: Could not calculate coordinate system: {calc_error}")
            else:
                print(f"Warning: Need 4 buttons for coordinate system, got {len(button_coords_3d)}")

        except Exception as e:
            print(f"Warning: Error in coordinate system calculation: {e}")

    def _ensure_coordinate_axes_for_finetune(self):
        """
        Ensure that coordinate axes are available for finetune operation.
        In the refactored version, coordinate axes are stored directly in ButtonHandler.
        """
        if self.last_coordinate_axes is not None:
            print("ButtonHandler: Coordinate axes available for finetune")
            print(f"  Axes: X={self.last_coordinate_axes[0]}, Y={self.last_coordinate_axes[1]}, Z={self.last_coordinate_axes[2]}")
            print(f"  Origin: {self.last_coordinate_origin}")
        else:
            print("Warning: No coordinate axes available for finetune")

    def _get_ready_status_from_touching(self) -> str:
        """
        Convert touching_* status to ready_* status after finetune.
        """
        current_status = self.status
        if current_status == 'touching_left_button':
            return 'ready_left_button'
        elif current_status == 'touching_right_button':
            return 'ready_right_button'
        elif current_status.startswith('touching_knob_'):
            knob_position = current_status.replace('touching_knob_', '')
            return f'ready_knob_{knob_position}'
        else:
            # Fallback
            return 'ready'
    
    def run(self, params: dict) -> tuple:
        """
        Execute button task.
        Args:
            params: Task parameters from client.py
        Returns:
            Tuple of (error_code, result_data)
        """
        if not BUTTON_MODULES_AVAILABLE:
            return self._return_error_with_reset(BUTTON_CONFIG_ERROR, 'ButtonControl modules not available')
        if self.arm is None:
            return self._return_error_with_reset(ARM_STATE_ERROR, 'Robotic arm not initialized')
        if self.emergency_stopped:
            # Emergency stop should not trigger reset
            return (EMERGENCY_STOP_ERROR, {
                'error_message': 'Emergency stop active'
            })
        try:
            print(f"ButtonHandler: Starting button task with params: {params}")
            action = params.get('action', '')
            if not action:
                return self._return_error_with_reset(BUTTON_STATE_ERROR, 'No action specified in parameters')
            print(f"ButtonHandler: Executing action '{action}'")
            validation_result = self._validate_action(action)
            if validation_result is not None:
                return validation_result
            self.running = True
            # 处理checkpoint/ checkpoint_bool
            extra_params = {}
            if 'checkpoint' in params:
                extra_params['checkpoint'] = params['checkpoint']
            # checkpoint_bool用于后续返回
            checkpoint_bool = params.get('checkpoint_bool', False)
            # 执行action
            try:
                result_code, result_message, extra_data = self._execute_action(action, extra_params)
                if self.emergency_stopped:
                    return (EMERGENCY_STOP_ERROR, {
                        'error_message': 'Emergency stop during execution'
                    })
                if result_code != SUCCESS_CODE:
                    self.running = False
                    return (result_code, {
                        'error_message': result_message
                    })
                print(f"ButtonHandler: Action {action} execution completed")
            except Exception as e:
                self.running = False
                error_message = f"Button action execution error: {str(e)}"
                print(error_message)
                return (BUTTON_OPERATION_ERROR, {
                    'error_message': error_message
                })
            self.running = False
            # 构造返回内容
            result = {
                'error_message': f'Action {action} completed successfully',
                'action': action,
                'params_used': params,
                'final_status': self.status,
                'status_history': self.states[-5:]  # Last 5 status changes
            }
            # 若checkpoint_bool为True，且有self._last_search_checkpoint，返回
            if checkpoint_bool and self._last_search_checkpoint is not None:
                result['checkpoint'] = self._last_search_checkpoint
            # 若extra_data中有checkpoint，也返回
            if 'checkpoint' in extra_data and extra_data['checkpoint'] is not None:
                result['checkpoint'] = extra_data['checkpoint']
            return (SUCCESS_CODE, result)
        except Exception as e:
            self.running = False
            error_message = f"Button task execution error: {str(e)}\n{traceback.format_exc()}"
            print(error_message)
            return (UNKNOWN_ERROR, {
                'error_message': error_message
            })
    
    def emergency_stop(self):
        """
        Handle emergency stop command.
        Simplified version based on pole_processor.py implementation.

        Immediately stops all button operations and sets status to 'stopped'.
        """
        print("ButtonHandler: Emergency stop activated")
        self.emergency_stopped = True
        self.running = False
        self.set_status('stopped', 'Emergency stop triggered')

        # Immediately stop arm movement
        if self.arm is not None:
            try:
                print("ButtonHandler: Stopping arm movement immediately")
                self.arm.rm_set_arm_stop()
                self.arm.rm_set_arm_delete_trajectory()
            except Exception as arm_stop_error:
                print(f"Error stopping arm during emergency stop: {arm_stop_error}")

        print("ButtonHandler: Emergency stop completed")
    
    def reset_emergency_stop(self):
        """Reset emergency stop state."""
        print("ButtonHandler: Emergency stop reset")
        self.emergency_stopped = False
    

    def smart_wait_for_arm_stable(self, max_wait_time: float = 1.0, check_interval: float = 0.1,
                             position_threshold: float = 0.001) -> bool:
        """
        智能等待机械臂稳定，基于位置变化检查而非固定时间等待

        Args:
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）
            position_threshold: 位置变化阈值（米）

        Returns:
            bool: True表示机械臂已稳定，False表示超时或急停
        """
        import time
        import math

        start_time = time.time()
        last_pose = None
        stable_count = 0
        required_stable_checks = 3  # 需要连续3次检查都稳定

        while time.time() - start_time < max_wait_time:
            # 检查急停
            if self.emergency_stopped:
                return False

            try:
                # 获取当前位姿
                ret_msg = self.arm.rm_get_current_arm_state()
                if ret_msg[0] != 0:
                    time.sleep(check_interval)
                    continue

                current_pose = np.array(ret_msg[1]['pose'])

                if last_pose is not None:
                    # 计算位置变化
                    pos_change = np.linalg.norm(current_pose[:3] - last_pose[:3])

                    if pos_change < position_threshold:
                        stable_count += 1
                        if stable_count >= required_stable_checks:
                            return True
                    else:
                        stable_count = 0

                last_pose = current_pose
                time.sleep(check_interval)

            except Exception as e:
                # 如果状态检查失败，回退到短暂等待
                time.sleep(check_interval)

        return False  # 超时

    def _wait_for_arm_stop(self, timeout=60.0, max_state_retry=160, max_trajectory_retry=240, verbose=True):
        """
        等待机械臂运动结束，支持急停检测。
        Args:
            timeout: 最大等待时间（秒）
            max_state_retry: 获取状态最大重试次数
            max_trajectory_retry: 运动未结束最大重试次数
            verbose: 是否打印日志
        Returns:
            0 表示运动已结束
            ARM_STATE_ERROR 表示超时或状态异常
            EMERGENCY_STOP_ERROR 表示急停
        """
        # print('debug | start waiting')
        import time
        start_time = time.time()
        trajectory_count = 0
        while True:
            if self.emergency_stopped:
                self._error_message = '[EMERGENCY_STOP]'
                if verbose:
                    print('ButtonHandler: Emergency stop detected during wait for arm stop')
                return EMERGENCY_STOP_ERROR
            if trajectory_count > max_trajectory_retry or (time.time() - start_time) > timeout:
                self._error_message = "Timeout error in checking arm state (wait_for_arm_stop)"
                if verbose:
                    print('ButtonHandler: Timeout waiting for arm to stop')
                return ARM_STATE_ERROR
            trajectory_count += 1
            current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            cur_err_count = 0
            while current_trajectory_state['return_code'] != 0:
                if self.emergency_stopped:
                    self._error_message = '[EMERGENCY_STOP]'
                    if verbose:
                        print('ButtonHandler: Emergency stop detected during wait for arm stop (state retry)')
                    return EMERGENCY_STOP_ERROR
                if cur_err_count > max_state_retry:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                    if verbose:
                        print('ButtonHandler: Too many errors getting arm state')
                    return ARM_STATE_ERROR
                cur_err_count += 1
                if verbose and cur_err_count > 80:
                    print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                time.sleep(0.2)
                current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['trajectory_type'] == 0:
                break
            time.sleep(0.25)
            if verbose and trajectory_count > 100:
                print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
        # print('debug | end waiting')
        return 0

    def move_arm(self, move_type, args):
        """
        Unified arm movement method with emergency stop checking.
        Simplified version based on pole_processor.py implementation.

        Args:
            move_type: Type of movement ('rm_movej', 'rm_movej_p', 'rm_movel', 'rm_movej_canfd')
            args: Tuple of arguments for the movement function

        Returns:
            Error code (SUCCESS_CODE if successful, EMERGENCY_STOP_ERROR if stopped)
        """
        if self.emergency_stopped:
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR

        if self.arm is None:
            self._error_message = f'[ARM_NOT_AVAILABLE]'
            return ARM_STATE_ERROR

        # Check if arm is currently moving and stop if necessary
        try:
            current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
            if current_trajectory_state['return_code'] == 0 and current_trajectory_state['trajectory_type'] != 0:
                print("ButtonHandler: Stopping current trajectory before new movement")
                self.arm.rm_set_arm_stop()
                self.arm.rm_set_arm_delete_trajectory()
                import time
                time.sleep(0.2)
        except Exception as e:
            print(f"Warning: Could not check/stop current trajectory: {e}")

        # 等待上一个运动结束
        wait_ret = self._wait_for_arm_stop()
        if wait_ret != 0:
            return wait_ret

        # 等待位姿稳定（前置）
        stable = self.smart_wait_for_arm_stable(max_wait_time=1.0, check_interval=0.1, position_threshold=0.001)
        if not stable:
            self._warn_message = '[Pose not stable before move]'
            print(f'ButtonHandler: {self._warn_message}')
            

        ret_code = 0

        try:
            if move_type == 'rm_movej':
                ret = self.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movej{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movej(args[0], args[1], args[2], args[3], args[4])

            elif move_type == 'rm_movej_p':
                ret = self.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movej_p{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movej_p(args[0], args[1], args[2], args[3], args[4])

            elif move_type == 'rm_movel':
                ret = self.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])
                if ret != 0 and ret != -3:
                    ret_code = ARM_MOVEMENT_ERROR
                    self._error_message = f'[rm_movel{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
                if ret == -3:
                    ret_3_count = 0
                    while ret == -3:
                        if self.emergency_stopped:
                            print('[[EMERGENCY_STOP]]')
                            self._error_message = f'[EMERGENCY_STOP]'
                            return EMERGENCY_STOP_ERROR
                        if ret_3_count > 50:
                            print("-3:", ret_3_count, ret, (args[0], args[1], args[2], args[3], args[4]))
                        if ret_3_count > 100:
                            break
                        import time
                        time.sleep(0.1)
                        ret_3_count += 1
                        ret = self.arm.rm_movel(args[0], args[1], args[2], args[3], args[4])

            # elif move_type == 'rm_movej_canfd':
            #     ret = self.arm.rm_movej_canfd(args[0], args[1], args[2], args[3], args[4])
            #     if ret != 0:
            #         ret_code = ARM_MOVEMENT_ERROR
            #         self._error_message = f'[rm_movej_canfd{(args[0], args[1], args[2], args[3], args[4])}: {ret}]'
            else:
                ret_code = UNKNOWN_ERROR
                self._error_message = '[Unknown move type]'

        except Exception as e:
            ret_code = ARM_MOVEMENT_ERROR
            self._error_message = f'[{move_type} exception: {str(e)}]'

        # 等待本次运动结束
        wait_ret = self._wait_for_arm_stop()
        if wait_ret != 0:
            return wait_ret

        # 等待位姿稳定（后置）
        stable = self.smart_wait_for_arm_stable(max_wait_time=1.0, check_interval=0.1, position_threshold=0.001)
        if not stable:
            self._warn_message = '[Pose not stable after move]'
            print(f'ButtonHandler: {self._warn_message}')

        # Final emergency stop check (like pole_processor)
        if self.emergency_stopped:
            self._error_message = f'[EMERGENCY_STOP]'
            return EMERGENCY_STOP_ERROR

        return ret_code
    
    def reset(self) -> int:
        """
        Reset button handler to initial state.
        Enhanced with trajectory_break_count tolerance, based on pole_processor.py.

        Returns:
            Error code (SUCCESS_CODE if successful)
        """
        try:
            print("ButtonHandler: Starting reset")

            # Reset basic flags
            self.emergency_stopped = False
            self.running = False

            # Reset arm state if available
            if self.arm is not None:
                try:
                    # Stop any current movement first
                    print("ButtonHandler: Stopping current arm movement")
                    self.arm.rm_set_arm_stop()
                    self.arm.rm_set_arm_delete_trajectory()
                    import time
                    time.sleep(0.2)

                    # Clear system errors (like pole_processor.py)
                    current_state = self.arm.rm_get_current_arm_state()
                    current_state = self.arm.rm_get_current_arm_state()
                    reset_count, reset_count_max = 0, 50
                    while current_state[0] != 0 or current_state[1]['err']['err'] != ['0']:
                        if reset_count > reset_count_max:
                            print(f"Warning: Cannot clear system error after {reset_count_max} attempts")
                            self._error_message = f'[Cannot clear system error: {current_state}]'
                            self.set_status('uncertain', 'Reset completed but failed to clear system error')
                            return ARM_STATE_ERROR
                        # Note: We don't check emergency_stopped here because reset should complete
                        # even if emergency stop is triggered during reset process
                        time.sleep(0.1)
                        reset_count += 1
                        ret = self.arm.rm_clear_system_err()
                        if ret != 0:
                            continue
                        time.sleep(0.4)
                        current_state = self.arm.rm_get_current_arm_state()
                        current_state = self.arm.rm_get_current_arm_state()

                    print("ButtonHandler: Arm system errors cleared")

                    # Move to zero pose
                    ret = self.move_to_zero_pose()
                    if ret == 0:
                        self._error_message = ""
                        self.set_status('uncertain', 'Reset completed and moved to zero pose')
                    else:
                        print(f"Warning: move_to_zero_pose failed with code {ret}")
                        self._error_message = f"[move_to_zero_pose failed: {ret}]"
                        self.set_status('uncertain', 'Reset completed but failed to move to zero pose')

                except Exception as e:
                    print(f"Warning: Error during arm reset: {e}")
                    self._error_message = f"[Exception during arm reset: {e}]"
                    self.set_status('uncertain', 'Reset completed but exception during arm reset')
            else:
                # No arm, just reset state
                self.set_status('uncertain', 'Reset completed (no arm)')

            # --- Trajectory break count tolerance (from pole_processor.py) ---
            import time
            if self.arm is not None:
                trajectory_count = 0
                trajectory_break_count = 0
                while True:
                    # Note: We don't check emergency_stopped here because reset should complete
                    # even if emergency stop is triggered during reset process
                    if trajectory_count > 240:
                        self._error_message = "Timeout error in checking arm state"
                        return ARM_TRAJECTORY_TIMEOUT_ERROR
                    trajectory_count += 1
                    current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    cur_err_count = 0
                    while current_trajectory_state['return_code'] != 0:
                        if cur_err_count > 160:
                            self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                            return ARM_STATE_ERROR
                        cur_err_count += 1
                        if cur_err_count > 80:
                            print(f'- [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                        time.sleep(0.2)
                        current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    if current_trajectory_state['trajectory_type'] == 0:
                        trajectory_break_count += 1
                        if trajectory_break_count > 3:
                            break
                        time.sleep(0.1)
                        continue
                    else:
                        trajectory_break_count = 0
                    time.sleep(0.25)
                    if trajectory_count > 100:
                        print(f'[{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
                self.arm.rm_get_current_arm_state()
            # --- End trajectory break count tolerance ---

            # Reset internal state variables
            self.states = []
            self.last_coordinate_axes = None
            self.last_coordinate_origin = None
            self.last_observing_frame = None

            print("ButtonHandler: Reset completed successfully")
            return SUCCESS_CODE

        except Exception as e:
            print(f"Critical reset error: {str(e)}")
            print(f"Error details: {traceback.format_exc()}")
            return UNKNOWN_ERROR
    
    def move_to_zero_pose(self):
        """
        Move the robot arm to the zero position safely.
        Simplified version based on pole_processor.py implementation.

        Returns:
            0 on success, error code on failure
        """
        import time
        try:
            print("ButtonHandler: Starting move_to_zero_pose")

            if self.arm is None:
                self._error_message = '[ARM_NOT_AVAILABLE]'
                print("ButtonHandler: Arm not available for move_to_zero_pose")
                return ARM_STATE_ERROR

            # Check gripper state (like pole_processor.py)
            gripper_ret = self.arm.rm_get_gripper_state()
            if gripper_ret[0] != 0 or gripper_ret[1]['error'] != 0:
                print("ButtonHandler: Error getting gripper state!")
                self._error_message = f'[{gripper_ret}]'
                return GRIPPER_STATE_ERROR
                
            if 400 < gripper_ret[1]['actpos'] < 990:
                # Gripper is grabbing something (not applicable for button operations)
                print("ButtonHandler: Gripper is grabbing - releasing first")
                ret = self.arm.rm_set_gripper_release(150, True, 10)
                if ret != 0:
                    self._error_message = f'[{ret}]'
                    return GRIPPER_CONTROL_ERROR
            else:
                # Gripper is not grabbing or is releasing
                if gripper_ret[1]['actpos'] <= 400:
                    print("ButtonHandler: Gripper is not grabbing")
                    ret = self.arm.rm_set_gripper_release(150, True, 10)
                    if ret != 0:
                        self._error_message = f'[{ret}]'
                        return GRIPPER_CONTROL_ERROR
                else:
                    print("ButtonHandler: Gripper is releasing")

            # Get current arm state
            current_state = self.arm.rm_get_current_arm_state()
            cur_err_count = 0
            while current_state[0] != 0:
                if cur_err_count > 20:
                    self._error_message = f'[Failed to get arm state after multiple attempts: {current_state}]'
                    return ARM_STATE_ERROR
                cur_err_count += 1
                print('ButtonHandler: Error in get current state')
                time.sleep(0.8)
                current_state = self.arm.rm_get_current_arm_state()

            # Get zero pose from configuration (same as pole_processor.py)
            zero_joints = self.robot_config.zero_pose
            print(f"ButtonHandler: Moving to configured zero position: {zero_joints}")

            # Move to zero position with safe speed
            ret = self.move_arm('rm_movej', (zero_joints, 20, 50, 0, 1))
            if ret != SUCCESS_CODE:
                self._error_message = f'[Failed to move to zero position: {ret}]'
                return ret

            # Wait for movement to complete (simplified like pole_processor.py)
            trajectory_count = 0
            while True:
                if trajectory_count > 240:  # Increased timeout like pole_processor.py
                    self._error_message = "Timeout error in checking arm state"
                    return ARM_STATE_ERROR
                if self.emergency_stopped:
                    print("ButtonHandler: Emergency stop detected during trajectory wait")
                    return EMERGENCY_STOP_ERROR
                trajectory_count += 1
                try:
                    current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    cur_err_count = 0
                    while current_trajectory_state['return_code'] != 0:
                        if cur_err_count > 160:  # Increased error count like pole_processor.py
                            self._error_message = f'[Failed to get arm state after multiple attempts: {current_trajectory_state}]'
                            return ARM_STATE_ERROR
                        cur_err_count += 1
                        print(f'ButtonHandler: - [{cur_err_count}]Error in get current state: {current_trajectory_state}')
                        time.sleep(0.2)
                        current_trajectory_state = self.arm.rm_get_arm_current_trajectory()
                    if current_trajectory_state['trajectory_type'] == 0:
                        break
                    time.sleep(0.25)
                    print(f'ButtonHandler: [{trajectory_count}] trajectory not stopped yet', current_trajectory_state)
                except Exception as e:
                    print(f'ButtonHandler: Error checking trajectory state: {e}')
                    time.sleep(0.2)

            print("ButtonHandler: move_to_zero_pose completed successfully")
            return 0

        except Exception as e:
            error_msg = f'move_to_zero_pose error: {str(e)}'
            print(error_msg)
            self._error_message = f'[{error_msg}]'
            return UNKNOWN_ERROR
    
    def is_running(self) -> bool:
        """Check if button task is currently running."""
        return self.running
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of button handler."""
        return {
            'running': self.running,
            'emergency_stopped': self.emergency_stopped,
            'arm_available': self.arm is not None,
            'camera_available': self.camera_instance is not None and self.camera_instance.color_image is not None,
            'modules_available': BUTTON_MODULES_AVAILABLE,
            'current_status': self.status,
            'status_history_length': len(self.states),
            'last_5_states': self.states[-5:] if len(self.states) >= 5 else self.states
        }

    def _map_high_level_action(self, action_name: str) -> list:
        """
        Map high-level action names to low-level action sequences.
        
        Args:
            action_name: High-level action name
            
        Returns:
            List of low-level actions to execute
        """
        action_mappings = {
            "switch_manual": [
                "find_buttons",
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ],
            "turn_on": [
                "observe_buttons",
                "touch_left_button",
                "finetune",
                "click_button",
                "restore"
            ],
            "turn_off": [
                "observe_buttons",
                "touch_right_button",
                "finetune",
                "click_button",
                "restore"
            ],
            "switch_automatic": [
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ]
        }
        
        return action_mappings.get(action_name, [action_name])

    def execute_continuous_sequence(self, params=None) -> Tuple[int, Dict[str, Any]]:
        """
        Execute a continuous sequence of button operations.

        Args:
            params: Parameters dict containing:
                - operating: List of operations to perform (optional)
                - checkpoint: List of floats for known_orientation (optional)
                - checkpoint_bool: Boolean to return checkpoint in response (optional)

        Returns:
            Tuple of (error_code, result_data)
        """

        # Check emergency stop before starting sequence
        if self.emergency_stopped:
            print('ButtonHandler: Cannot start sequence - emergency stop active')
            return (EMERGENCY_STOP_ERROR, {
                'error_message': 'Cannot start sequence - emergency stop active'
            })

        print('Reset before action sequence')
        try:
            reset_code = self.reset()
        except Exception as e:
            print(f"ButtonHandler: Error during reset: {e}")
            return (ARM_STATE_ERROR, {
                'error_message': f'Reset before sequence failed: {e}',
                'init_reset_code': -9999,
                'init_reset_message': f'Reset before sequence failed: {e}'
            })

        # Handle both old and new calling conventions
        if params is None:
            params = {}
        elif isinstance(params, list):
            # Old calling convention: params is actually operating_sequence
            params = {"operating": params}

        # Extract parameters
        operating_sequence = params.get("operating", None)
        checkpoint = params.get("checkpoint", None)
        checkpoint_bool = params.get("checkpoint_bool", False)

        if operating_sequence is None:
            # Default sequence: find_buttons -> observe_buttons -> touch_knob -> finetune -> turn_knob -> restore -> 
            # observe_buttons -> touch_left_button -> finetune -> click_button -> restore -> 
            # observe_buttons -> touch_right_button -> finetune -> click_button -> restore -> 
            # observe_buttons -> touch_knob -> finetune -> turn_knob -> restore
            operating_sequence = [
                "find_buttons",
                "observe_buttons", 
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob", 
                "restore",
                "observe_buttons",
                "touch_left_button",
                "finetune",
                "click_button",
                "restore",
                "observe_buttons",
                "touch_right_button", 
                "finetune",
                "click_button",
                "restore",
                "observe_buttons",
                "touch_knob",
                "finetune",
                "turn_knob",
                "restore"
            ]
        
        # Expand high-level actions to low-level sequences
        expanded_sequence = []
        for action in operating_sequence:
            if action in ["switch_manual", "turn_on", "turn_off", "switch_automatic"]:
                # Map high-level action to low-level sequence
                low_level_actions = self._map_high_level_action(action)
                expanded_sequence.extend(low_level_actions)
                print(f"ButtonHandler: Mapped '{action}' to {low_level_actions}")
            else:
                # Keep low-level action as is
                expanded_sequence.append(action)
        
        print(f"ButtonHandler: Starting continuous sequence with {len(expanded_sequence)} operations (expanded from {len(operating_sequence)} high-level actions)")
        
        # Track progress for reporting
        completed_operations = []
        sequence_checkpoint = None  # Store checkpoint from find_buttons operation
        
        # --- New: Special handling for high-level actions ---

        is_first_knob = True
        skip_next_finetune = False  # Track if we should skip the next finetune operation
        skip_next_turn_knob = False  # Track if we should skip the next turn_knob operation
        skip_next_restore = False  # Track if we should skip the next restore operation
        for i, action in enumerate(expanded_sequence):
            # Check for emergency stop before each operation
            if self.emergency_stopped:
                print("ButtonHandler: Emergency stop detected during continuous sequence")
                return (EMERGENCY_STOP_ERROR, {
                    'error_message': 'Emergency stop during continuous sequence',
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
            print(f"ButtonHandler: Executing operation {i+1}/{len(expanded_sequence)}: {action}")

            # Check if we should skip this specific operation
            if action == "finetune" and skip_next_finetune:
                print(f"ButtonHandler: Skipping finetune after first knob turn was skipped")
                skip_next_finetune = False  # Reset the flag
                completed_operations.append(action)
                continue
            if action == "turn_knob" and skip_next_turn_knob:
                print(f"ButtonHandler: Skipping turn_knob after first knob turn was skipped")
                skip_next_turn_knob = False  # Reset the flag
                completed_operations.append(action)
                continue
            if action == "restore" and skip_next_restore:
                print(f"ButtonHandler: Skipping first restore after first knob turn was skipped")
                skip_next_restore = False  # Reset the flag
                completed_operations.append(action)
                continue
                
            if action == "touch_knob":
                knob_angle = self._get_current_knob_angle()
                if knob_angle is not None:
                    knob_position = self._determine_knob_position_from_angle(knob_angle)
                    print(f"ButtonHandler: Current knob position: {knob_position}, angle: {knob_angle:.1f}°")

                    # For first touch_knob in this sequence, expect right position
                    if is_first_knob:  # First touch_knob operation in this sequence
                        is_first_knob = False
                        # if knob_position != 'right': #and knob_position != 'left':
                            # error_msg = f"Knob position is {knob_position}, expected MANNUAL or AUTOMATIC (angle: {knob_angle:.1f}°)"
                            # print(f"ButtonHandler: {error_msg}")
                            # return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_msg, {
                            #     'completed_operations': completed_operations,
                            #     'failed_operation': action,
                            #     'operation_index': i,
                            #     'knob_angle': knob_angle,
                            #     'knob_position': knob_position,
                            # })
                        if knob_position == 'right':
                            # Set flags to skip the next finetune, turn_knob, and restore operations
                            skip_next_finetune = True
                            skip_next_turn_knob = True
                            skip_next_restore = True
                            print(f"ButtonHandler: Skipping first knob turn (knob already in 'right' position)")
                            completed_operations.append(action)  # Mark as completed since we're skipping it
                            continue
                    else:  # Second touch_knob operation in this sequence
                        if knob_position != 'right':
                            error_msg = f"Knob position is {knob_position}, expected 'MANNUAL' (angle: {knob_angle:.1f}°)"
                            print(f"ButtonHandler: {error_msg}")
                            return self._return_error_with_reset(BUTTON_DETECTION_ERROR, error_msg, {
                                'completed_operations': completed_operations,
                                'failed_operation': action,
                                'operation_index': i,
                                'knob_angle': knob_angle,
                                'knob_position': knob_position,
                            })
                else:
                    print("ButtonHandler: Warning - No real-time knob angle data available for validation")
                    if is_first_knob:
                        is_first_knob = False
            
            # Prepare extra_params for this action
            extra_params = {}
            if action == 'find_buttons':
                if checkpoint_bool:
                    extra_params['force_search'] = True
                else:
                    if checkpoint is not None:
                        extra_params['checkpoint'] = checkpoint

            # Execute the action
            try:
                result_code, result_message, extra_data = self._execute_action(action, extra_params=extra_params)
                if action == 'find_buttons' and extra_data and 'checkpoint' in extra_data:
                    sequence_checkpoint = extra_data['checkpoint']
                if result_code != SUCCESS_CODE:
                    print(f"ButtonHandler: Operation '{action}' failed: {result_message}")
                    return self._return_error_with_reset(result_code, f"Operation '{action}' failed: {result_message}", {
                        'completed_operations': completed_operations,
                        'failed_operation': action,
                        'operation_index': i
                    })
                completed_operations.append(action)
                print(f"ButtonHandler: Operation '{action}' completed successfully")
                if i < len(expanded_sequence) - 1:
                    import time
                    time.sleep(0.2)
            except Exception as e:
                error_msg = f"Exception during operation '{action}': {str(e)}"
                print(f"ButtonHandler: {error_msg}")
                return self._return_error_with_reset(BUTTON_OPERATION_ERROR, error_msg, {
                    'completed_operations': completed_operations,
                    'failed_operation': action,
                    'operation_index': i
                })
        print(f"ButtonHandler: Continuous sequence completed successfully with {len(completed_operations)} operations")
        result_data = {
            'error_message': f'Continuous sequence completed successfully',
            'completed_operations': completed_operations,
            'total_operations': len(expanded_sequence),
            'original_sequence': operating_sequence,
        }
        if checkpoint_bool:
            if sequence_checkpoint is not None:
                result_data['checkpoint'] = sequence_checkpoint
                print(f"ButtonHandler: Adding checkpoint to response: {sequence_checkpoint}")
            else:
                print("ButtonHandler: Warning - checkpoint_bool is True but no checkpoint was found")
        # When checkpoint_bool is False, don't add checkpoint field
        # --- Only reset if not in emergency stop state ---
        if not self.emergency_stopped:
            try:
                reset_code = self.reset()
                result_data['final_reset_code'] = reset_code
                if reset_code == 0:
                    result_data['final_reset_message'] = 'Reset after sequence completed successfully'
                else:
                    result_data['final_reset_message'] = f'Reset after sequence failed with code {reset_code}'
            except Exception as e:
                result_data['final_reset_code'] = -9999
                result_data['final_reset_message'] = f'Reset after sequence raised exception: {e}'
        else:
            result_data['final_reset_code'] = -1
            result_data['final_reset_message'] = 'Reset skipped due to emergency stop state'
            print("ButtonHandler: Skipping final reset due to emergency stop state")
        return (SUCCESS_CODE, result_data)


def create_button_handler(camera_instance=None) -> Optional[ButtonHandler]:
    """
    Factory function to create ButtonHandler instance.

    Args:
        camera_instance: Optional Camera instance from pole_processor.py

    Returns:
        ButtonHandler instance or None if creation fails
    """
    try:
        return ButtonHandler(camera_instance)
    except Exception as e:
        print(f"Failed to create ButtonHandler: {e}")
        return None


# Test function for standalone testing
def test_button_handler():
    """Test function for button handler."""
    print("Testing ButtonHandler...")
    
    handler = create_button_handler()
    if handler is None:
        print("Failed to create ButtonHandler")
        return False
    
    # Test status
    status = handler.get_status()
    print(f"Initial status: {status}")
    
    # Test data collection and saving
    print("Testing data collection and saving...")
    data = handler._collect_action_data(multi_frame=False, frames_count=5)
    if data:
        print("Data collected successfully")
        print(f"Data keys: {list(data.keys())}")
        print(f"Has timestamp: {'timestamp' in data}")
        print(f"Timestamp value: {data.get('timestamp', 'Not found')}")
        # # Test saving
        # result = handler.save_collected_data(data, 'test_')
        # print(f"Save result: {result is not None}")
    else:
        print("Data collection failed")
    
    # Test run with empty params
    result = handler.run({})
    print(f"Run result: {result}")
    
    # Test emergency stop
    handler.emergency_stop()
    status = handler.get_status()
    print(f"After emergency stop: {status}")
    
    # Test reset
    reset_result = handler.reset()
    print(f"Reset result: {reset_result}")
    
    status = handler.get_status()
    print(f"After reset: {status}")
    
    print("ButtonHandler test completed")
    return True


if __name__ == "__main__":
    # Run test when executed directly
    test_button_handler()
