import os.path
import datetime
import numpy as np
import cv2
import time
import pyrealsense2 as rs
from RM_API2.Python.Robotic_Arm.rm_robot_interface import Robotic<PERSON>rm, rm_thread_mode_e, rm_robot_arm_model_e, \
    rm_force_type_e, Algo, rm_inverse_kinematics_params_t
from RmRobotic.rm65 import RM63B
import yaml
import glob

def load_config(config_path="config/pole_params.yaml"):
    """
    Load configuration parameters from YAML file.

    Args:
        config_path: Path to the configuration file

    Returns:
        Dictionary containing configuration parameters
    """
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
            return config  # .get('pole', {})
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return {}

def mouse_callback(event, x, y, flags, param):
    """
    Handle mouse click events for depth camera visualization.

    Displays depth information and 3D coordinates when clicking on image.
    """
    if event == cv2.EVENT_LBUTTONDOWN:
        # Get depth if available
        depth_val = None
        depth_image = param['di']
        depth_intrin = param['depth_intrin']

        if depth_image is not None:
            depth_val = depth_image[y][x]

        # Print the clicked coordinates
        if depth_val is not None and depth_intrin is not None:
            print(f"Clicked at (x={x}, y={y}), depth={depth_val}mm")

            # Convert to 3D point using depth intrinsics
            point_3d = rs.rs2_deproject_pixel_to_point(
                depth_intrin,
                (float(x), float(y)),
                depth_val / 1000.0
            )
            print(f"3D coordinates: ({point_3d[0]:.3f}, {point_3d[1]:.3f}, {point_3d[2]:.3f}) meters")
        else:
            print(f"Clicked at (x={x}, y={y})")


class Camera:
    """
    Camera class for handling RealSense camera operations.

    Provides camera initialization, frame acquisition, and integration with robot arm.
    """
    def __init__(self, visible=False, config_path="config/camera_params.yaml", linux_mode=None):
        """Initialize camera and robot arm connections."""
        # Load configuration parameters
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            config = {}
            # return
        if linux_mode is None:
            import platform
            os_name = platform.system()
            if os_name == "Linux":
                linux_mode = True
                print(f"Current Platform System: {os_name}")
            else:
                linux_mode = False
                print(f"Current Platform System: {os_name}")
        self.linux_mode = linux_mode
        self.visible = visible
        self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
        self.handle = self.arm.rm_create_robot_arm("************", 8080)

        arm_state = self.arm.rm_get_arm_all_state()
        print(f"Arm connection status: {arm_state}")
        current_state = self.arm.rm_get_current_arm_state()
        print(f"Current arm state: {current_state}")
        while current_state[0] != 0:
            print('Error connected to ARM')
            self.arm.rm_delete_robot_arm()
            time.sleep(2.0)
            self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
            self.handle = self.arm.rm_create_robot_arm("************", 8080)
            arm_state = self.arm.rm_get_arm_all_state()
            print(f"Arm connection status: {arm_state}")
            current_state = self.arm.rm_get_current_arm_state()
            print(f"Current arm state: {current_state}")
        arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
        force_type = rm_force_type_e.RM_MODEL_RM_B_E
        self.algo_handle = Algo(arm_model, force_type)
        self.rob = RM63B()
        # confirm connection
        software_info = self.arm.rm_get_arm_software_info()
        if software_info[0] == 0:
            print("\n================== Arm Software Information ==================")
            print("Arm Model: ", software_info[1]['product_version'])
            print("Algorithm Library Version: ", software_info[1]['algorithm_info']['version'])
            print("Control Layer Software Version: ", software_info[1]['ctrl_info']['version'])
            print("Dynamics Version: ", software_info[1]['dynamic_info']['model_version'])
            print("Planning Layer Software Version: ", software_info[1]['plan_info']['version'])
            print("==============================================================\n")
        else:
            print("Failed to get arm software information, error code: ", software_info[0])

        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)

        self.arm.rm_set_timeout(2000)  # 2000ms = 20s

        # Load camera calibration parameters from config or use defaults
        camera_rotation_matrix = config.get('camera_rotation_matrix')
        if camera_rotation_matrix:
            self.rotation_matrix = np.array(camera_rotation_matrix)
        else:
            self.rotation_matrix = np.array([  # Default values
                [-0.03914598, -0.99869253, -0.03287572],
                [0.99923066, -0.0392032, 0.00109761],
                [-0.00238501, -0.03280746, 0.99945884]
            ])

        camera_translation_vector = config.get('camera_translation_vector')
        if camera_translation_vector:
            self.translation_vector = np.array(camera_translation_vector)
        else:
            self.translation_vector = np.array([0.08695189, -0.04231192, 0.00636331])  # Default values

        # if self.visible:
        #     cv2.namedWindow('camera')
        self.l_text_boxes = []
        self.r_text_boxes = []
        self.circle_boxes = []
        self.rect_boxes = []
        self.poly_boxes = []
        self.line_boxes = []
        # Initialize frame storage
        self.depth_image, self.color_image = None, None
        self.display_img = None
        self.intr, self.depth_intrin = None, None
        self.color_intrin = None
        self.profile = None
        self.refresh_flag = False
        self.init_camera()
    def re_connect_arm(self):
        max_try = 10
        try_count = 0
        self.arm.rm_delete_robot_arm()
        time.sleep(1.0)
        arm_state = self.arm.rm_get_arm_all_state()
        print(f"Arm connection status: {arm_state}")
        current_state = self.arm.rm_get_current_arm_state()
        print(f"Current arm state: {current_state}")
        while current_state[0] != 0:
            try_count += 1
            if try_count > max_try:
                print('Failed to re-connect robot arm')
                return -2
            print('Error connected to ARM')
            self.arm.rm_delete_robot_arm()
            time.sleep(1.0)
            self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
            self.handle = self.arm.rm_create_robot_arm("************", 8080)
            arm_state = self.arm.rm_get_arm_all_state()
            print(f"Arm connection status: {arm_state}")
            current_state = self.arm.rm_get_current_arm_state()
            print(f"Current arm state: {current_state}")
        arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
        force_type = rm_force_type_e.RM_MODEL_RM_B_E
        self.algo_handle = Algo(arm_model, force_type)
        self.rob = RM63B()
    def refresh_canvas(self):
        self.refresh_flag = True
    def _refresh_canvas(self):
        self.l_text_boxes = []
        self.r_text_boxes = []
        self.circle_boxes = []
        self.rect_boxes = []
        self.poly_boxes = []
        self.line_boxes = []

    def run(self):
        while True:
            if self.linux_mode:
                video_devices = glob.glob(r'/dev/video*')
                if len(video_devices) < 3:
                    self.color_image, self.depth_image = None, None
                    assert len(video_devices) != 0, "No camera found"
                    assert len(video_devices) != 1, f"Only camera {video_devices} found"
                    assert len(video_devices) >= 3, f"Only cameras {video_devices} found"
                pass
            time.sleep(0.01)
            frames = self.pipeline.wait_for_frames()
            align = rs.align(rs.stream.color)
            aligned_frames = align.process(frames)
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()
            if not color_frame:
                continue
            if self.intr is None:
                self.intr = color_frame.profile.as_video_stream_profile().intrinsics  # 获取相机内参
                self.depth_intrin = depth_frame.profile.as_video_stream_profile(
                ).intrinsics
                self.color_intrin = self.intr
            self.depth_image = np.asanyarray(depth_frame.get_data())
            self.color_image = np.asanyarray(color_frame.get_data())
            if self.visible:

                cv2.namedWindow('camera_rgb')
                cv2.setMouseCallback('camera_rgb', mouse_callback,
                                     {'di': self.depth_image, 'depth_intrin': self.depth_intrin})
                display_img = self.color_image.copy()
                for box_idx, box in enumerate(self.l_text_boxes):
                    if box is None or len(box) == 0:
                        continue
                    text = box[0]
                    color = box[1] if len(box) >= 2 else (255, 255, 0)
                    font_size = box[2] if len(box) >= 3 else 0.5
                    location = box[3] if len(box) >= 4 else (20, 30*box_idx+20)
                    cv2.putText(display_img, text, location, cv2.FONT_HERSHEY_SIMPLEX, font_size, color, 2)
                for box_idx, box in enumerate(self.r_text_boxes):
                    if box is None or len(box) == 0:
                        continue
                    text = box[0]
                    color = box[1] if len(box) >= 2 else (255, 255, 0)
                    font_size = box[2] if len(box) >= 3 else 0.5
                    location = box[3] if len(box) >= 4 else (470, 30*box_idx+20)
                    cv2.putText(display_img, text, location, cv2.FONT_HERSHEY_SIMPLEX, font_size, color, 2)
                for box_idx, box in enumerate(self.circle_boxes):
                    if box is None or len(box) == 0:
                        continue
                    center, radius, color, filled = box
                    cv2.circle(display_img, center, radius, color, filled)
                for box_idx, box in enumerate(self.poly_boxes):
                    if box is None or len(box) == 0:
                        continue
                    pts, filled, color, mode = box
                    cv2.drawContours(display_img, pts, filled, color, mode)
                for box_idx, box in enumerate(self.line_boxes):
                    if box is None or len(box) == 0:
                        continue
                    start_pt, end_pt, color, thickness = box
                    cv2.line(display_img, start_pt, end_pt, color, thickness)
                if self.refresh_flag:
                    self._refresh_canvas()
                    self.refresh_flag = False
                cv2.imshow('camera_rgb', display_img)
                k = cv2.waitKey(1) & 0xFF
                self._handle_key_input(k)
                # self._handle_key_input(k)
                # cv2.imshow('camera', self.color_image)
                # cv2.waitKey(1)

    def init_camera(self):
        wait_count = 0
        if self.linux_mode:
            print('Waiting for camera initialization')
            video_devices = glob.glob(r'/dev/video*')
            while len(video_devices) < 3:
                wait_count += 1
                video_devices = glob.glob(r'/dev/video*')
                time.sleep(1)
                if wait_count % 60 == 0:
                    print(f'Wait for camera initialization: {wait_count//60} minutes. Detected devices: {video_devices}')
            pass
        print('prepare pipeline')
        self.profile = self.pipeline.start(self.config)
        print('ready to convey')
        # device = self.profile.get_device()
        # device.hardware_reset()
        # print('device reset')
        for _ in range(15):
            frames = self.pipeline.wait_for_frames()
        print('camera initialized')

    def _handle_key_input(self, k):
        if k == ord('z'):  # Save image
            _runtime = datetime.datetime.now().strftime("%m%d%H%M%S")
            os.makedirs('data/shot-images/', exist_ok=True)
            if self.color_image is not None:
                cv2.imwrite(f'data/shot-images/imc_{_runtime}.jpg', self.color_image)
            else:
                print('color image is none')
            if self.depth_image is not None:
                np.save(f'data/shot-images/imd_{_runtime}.npy', self.depth_image)
            else:
                print('depth image is none')
            print(f'save imc_{_runtime}.jpg, imd_{_runtime}.npy')

    def reload_config(self, config_path="config/pole_params.yaml"):
        """
        Reload camera calibration parameters from the configuration file.

        Args:
            config_path: Path to the configuration file

        Returns:
            Dictionary of loaded configuration parameters
        """
        try:
            config = load_config(config_path)
        except Exception as e:
            print(f"Error loading configuration: {e}, using default configuration")
            # config = {}
            return

        # Update camera calibration parameters
        camera_rotation_matrix = config.get('camera_rotation_matrix')
        if camera_rotation_matrix:
            self.rotation_matrix = np.array(camera_rotation_matrix)

        camera_translation_vector = config.get('camera_translation_vector')
        if camera_translation_vector:
            self.translation_vector = np.array(camera_translation_vector)

        print(f"Camera configuration reloaded from {config_path}")
        return config