import os
import cv2
import numpy as np

# Enable debug mode
os.environ['BUTTON_DEBUG_SAVE'] = 'true'

# Import after setting environment variable
from buttonControl.debug_saver import debug_saver

# Test the debug system
debug_saver.start_session("test_session")

# Create a test image
test_image = np.zeros((100, 100, 3), dtype=np.uint8)
test_image[:, :] = [255, 0, 0]  # Red image

# Save test images
debug_saver.save_intermediate(test_image, "test_original")
debug_saver.save_mask(test_image[:,:,0], "test_mask")
debug_saver.save_processed(test_image, "test_final")

# Save test data
debug_saver.save_data({"test": "data", "value": 123}, "test_results")

debug_saver.end_session()

print("Debug test completed. Check debug_results/ directory.")