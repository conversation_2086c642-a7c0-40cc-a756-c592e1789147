import cv2
import numpy as np
import sys
import os

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status, convert_coordinates_to_original

def test_detection_protocol(image_path, verbose=True, display_process=True):
    """
    Test the complete detection protocol with different statuses.
    
    Args:
        image_path (str): Path to the input image
        verbose (bool): Whether to print detailed information
        display_process (bool): Whether to show processing steps
    
    Returns:
        dict: Comprehensive test results
    """
    # Read the image
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not read image: {image_path}")
    
    results = {}
    
    # Test different detection modes
    test_statuses = [
        'uncertain',  # Full detection
        'touching_left_button',
        'touching_right_button', 
        'touching_knob_left',
        'touching_knob_right',
        'touching_knob_center'
    ]
    
    print(f"\n{'='*60}")
    print(f"Testing Detection Protocol for: {os.path.basename(image_path)}")
    print(f"{'='*60}")
    
    for status in test_statuses:
        print(f"\n--- Testing Status: {status} ---")
        
        try:
            detection_result = detect_buttons_by_status(
                image,
                status=status,
                verbose=verbose,
                display_process=display_process
            )
            
            results[status] = detection_result
            
            if isinstance(detection_result, dict):
                # Single target detection result
                target = detection_result.get('target')
                confidence = detection_result.get('confidence', 0.0)
                target_type = detection_result.get('target_type', 'unknown')
                
                print(f"  Target Type: {target_type}")
                print(f"  Target Found: {target is not None}")
                if target:
                    print(f"  Target: {target}")
                    print(f"  Confidence: {confidence:.3f}")
                else:
                    print("  No target detected")
                    
            elif isinstance(detection_result, tuple):
                # Full detection result
                display_img, top_row, bottom_row, knob, handle_angle, mode_code = detection_result
                
                print(f"  Full Detection Results:")
                print(f"    Top Row Buttons: {len([b for b in top_row if b]) if top_row else 0}")
                print(f"    Bottom Row Buttons: {len([b for b in bottom_row if b]) if bottom_row else 0}")
                print(f"    Knob Detected: {knob is not None}")
                if handle_angle is not None:
                    print(f"    Handle Angle: {handle_angle:.2f}°")
                if mode_code is not None:
                    mode_map = {1: "Manual", 0: "Stop", -1: "Auto"}
                    print(f"    Mode: {mode_map.get(mode_code, 'Unknown')}")
            else:
                print(f"  Unexpected result format: {type(detection_result)}")
                
        except Exception as e:
            print(f"  Error during detection: {str(e)}")
            results[status] = None
    
    return results


def analyze_results(results):
    """
    Analyze the detection results and provide insights.
    
    Args:
        results (dict): Detection results for different statuses
    
    Returns:
        dict: Analysis summary
    """
    analysis = {
        'successful_detections': 0,
        'failed_detections': 0,
        'best_confidence': 0.0,
        'best_target': None,
        'best_status': None,
        'full_detection_success': False
    }
    
    for status, result in results.items():
        if result is None:
            analysis['failed_detections'] += 1
            continue
            
        if isinstance(result, dict):
            # Single target detection
            target = result.get('target')
            confidence = result.get('confidence', 0.0)
            
            if target is not None:
                analysis['successful_detections'] += 1
                if confidence > analysis['best_confidence']:
                    analysis['best_confidence'] = confidence
                    analysis['best_target'] = target
                    analysis['best_status'] = status
                    
        elif isinstance(result, tuple):
            # Full detection
            display_img, top_row, bottom_row, knob, handle_angle, mode_code = result
            
            if (top_row and any(top_row)) or (bottom_row and any(bottom_row)) or knob:
                analysis['successful_detections'] += 1
                analysis['full_detection_success'] = True
            else:
                analysis['failed_detections'] += 1
    
    return analysis


if __name__ == "__main__":
    # Test images
    test_images = [
        'less_than_4_buttons_colorImage_20250730_094620_886',
        # Add more test images as needed
    ]
    
    for img_name in test_images:
        img_path = f'./tmp/{img_name}.png'
        
        if not os.path.exists(img_path):
            print(f"Warning: Test image not found: {img_path}")
            continue
            
        # Run comprehensive test
        results = test_detection_protocol(img_path, verbose=True, display_process=True)
        
        # Analyze results
        analysis = analyze_results(results)
        
        # Print analysis summary
        print(f"\n{'='*60}")
        print(f"Analysis Summary for: {img_name}")
        print(f"{'='*60}")
        print(f"Successful Detections: {analysis['successful_detections']}")
        print(f"Failed Detections: {analysis['failed_detections']}")
        print(f"Full Detection Success: {analysis['full_detection_success']}")
        
        if analysis['best_target']:
            print(f"Best Detection:")
            print(f"  Status: {analysis['best_status']}")
            print(f"  Target: {analysis['best_target']}")
            print(f"  Confidence: {analysis['best_confidence']:.3f}")
        
        print(f"\nProtocol test completed for {img_name}") 