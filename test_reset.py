#!/usr/bin/env python3
"""
Test script for button_processor reset functionality.
"""

import sys
import os
import time

# Add task directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'task'))

def test_button_handler_reset():
    """Test ButtonHandler reset functionality."""
    print("=== Testing ButtonHandler Reset Functionality ===")
    
    try:
        from task.button_processor import create_button_handler
        print("✓ Successfully imported button_processor")
        
        # Create button handler without camera (for testing)
        print("\n1. Creating ButtonHandler...")
        handler = create_button_handler(camera_instance=None)
        
        if handler is None:
            print("✗ Failed to create <PERSON><PERSON><PERSON><PERSON><PERSON>")
            return False
        
        print("✓ <PERSON><PERSON><PERSON><PERSON>ler created successfully")
        
        # Check initial status
        print("\n2. Checking initial status...")
        initial_status = handler.get_status()
        print(f"Initial status: {initial_status}")
        
        # Simulate some state changes
        print("\n3. Simulating state changes...")
        handler.set_status('visible', 'Test status change 1')
        handler.set_status('observing', 'Test status change 2')
        handler.set_status('touching_left_button', 'Test status change 3')
        
        print(f"Status after changes: {handler.status}")
        print(f"Status history length: {len(handler.states)}")
        print(f"Last few states: {handler.states[-3:]}")
        
        # Simulate emergency stop
        print("\n4. Simulating emergency stop...")
        handler.emergency_stop()
        print(f"Status after emergency stop: {handler.status}")
        print(f"Emergency stopped: {handler.emergency_stopped}")
        
        # Test reset
        print("\n5. Testing reset...")
        reset_result = handler.reset()
        
        print(f"Reset result code: {reset_result}")
        print(f"Status after reset: {handler.status}")
        print(f"Emergency stopped after reset: {handler.emergency_stopped}")
        print(f"Running after reset: {handler.running}")
        print(f"Status history length after reset: {len(handler.states)}")
        print(f"Status history after reset: {handler.states}")
        
        # Verify reset success
        if (reset_result == 0 and 
            handler.status == 'uncertain' and 
            not handler.emergency_stopped and 
            not handler.running and
            len(handler.states) == 1 and
            handler.states[0] == 'uncertain'):
            print("\n✓ Reset test PASSED - All conditions met")
            return True
        else:
            print("\n✗ Reset test FAILED - Some conditions not met")
            print(f"  Expected: reset_result=0, status='uncertain', emergency_stopped=False, running=False")
            print(f"  Actual: reset_result={reset_result}, status='{handler.status}', emergency_stopped={handler.emergency_stopped}, running={handler.running}")
            return False
            
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Test error: {e}")
        import traceback
        print(f"Error details: {traceback.format_exc()}")
        return False

def test_status_synchronization():
    """Test status synchronization between ButtonHandler and button_controller."""
    print("\n=== Testing Status Synchronization ===")
    
    try:
        from task.button_processor import create_button_handler
        
        # Create button handler
        handler = create_button_handler(camera_instance=None)
        if handler is None:
            print("✗ Failed to create ButtonHandler for sync test")
            return False
        
        # Initialize button controller if possible
        if hasattr(handler, '_initialize_button_controller'):
            try:
                handler._initialize_button_controller()
                if handler.button_controller is not None:
                    print("✓ Button controller initialized")
                    
                    # Test status sync
                    handler.set_status('visible', 'Testing sync', sync_to_controller=True)
                    
                    if hasattr(handler.button_controller, 'status'):
                        controller_status = handler.button_controller.status
                        print(f"Handler status: {handler.status}")
                        print(f"Controller status: {controller_status}")
                        
                        if handler.status == controller_status:
                            print("✓ Status synchronization working")
                            return True
                        else:
                            print("✗ Status synchronization failed")
                            return False
                    else:
                        print("? Controller has no status attribute - sync not applicable")
                        return True
                else:
                    print("? Button controller not available - sync test skipped")
                    return True
            except Exception as e:
                print(f"? Button controller initialization failed: {e}")
                print("? Sync test skipped due to controller unavailability")
                return True
        else:
            print("? No button controller initialization method found")
            return True
            
    except Exception as e:
        print(f"✗ Sync test error: {e}")
        return False

def main():
    """Run all tests."""
    print("Starting button_processor reset tests...\n")
    
    test1_passed = test_button_handler_reset()
    test2_passed = test_status_synchronization()
    
    print("\n=== Test Results ===")
    print(f"Reset functionality test: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Status synchronization test: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n✓ All tests PASSED")
        return 0
    else:
        print("\n✗ Some tests FAILED")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
