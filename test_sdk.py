from RM_API2.Python.Robotic_Arm.rm_robot_interface import *
import pyrealsense2 as rs
import math
import cv2
from RmRobotic.rm65 import RM63B
import numpy as np
import time
class Camera:
    def __init__(self, visible=False):
        self.visible = visible
        # self.arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
        # self.handle = self.arm.rm_create_robot_arm("************", 8080)
        # arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
        # force_type = rm_force_type_e.RM_MODEL_RM_B_E
        # self.algo_handle = Algo(arm_model, force_type)
        # self.rob = RM63B()
        self.pipeline = rs.pipeline()
        self.config = rs.config()
        self.config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
        self.config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)

        # self.arm.rm_set_timeout(1000)  # 1000ms = 10s
        self.rotation_matrix = np.array([  # 0701-13-20
            [-0.04198893, -0.99767839, -0.05361675],
            [0.99905672, -0.04252059, 0.00881333],
            [-0.01107268, -0.05319612, 0.99852269]
        ])
        self.translation_vector = np.array([0.08515154, -0.04080627, 0.01190365])
        print('prepare pipeline')
        self.profile = self.pipeline.start(self.config)
        print('ready to convey')
        # rs.log_to_console(rs.log_severity.debug)
        device = self.profile.get_device()
        device.hardware_reset()
        print('device reset')
        for _ in range(5):
            frames = self.pipeline.wait_for_frames()
        if self.visible:
            cv2.namedWindow('camera')
            # cv2.setMouseCallback('camera', mouse_callback)
        self.depth_image, self.color_image = None, None
        self.display_img = None
        self.intr, self.depth_intrin = None, None
        pass

    def run(self, max_num=10):
        count_num = 0
        while count_num < max_num:
            count_num += 1
            time.sleep(0.5)
            frames = self.pipeline.wait_for_frames()
            align = rs.align(rs.stream.color)
            aligned_frames = align.process(frames)
            color_frame = aligned_frames.get_color_frame()
            depth_frame = aligned_frames.get_depth_frame()
            if not color_frame:
                continue
            if self.intr is None:
                self.intr = color_frame.profile.as_video_stream_profile().intrinsics  # 获取相机内参
                self.depth_intrin = depth_frame.profile.as_video_stream_profile(
                ).intrinsics
            self.depth_image = np.asanyarray(depth_frame.get_data())
            self.color_image = np.asanyarray(color_frame.get_data())
            if not os.path.exists('data/camera_test'):
                os.makedirs('data/camera_test')
            ret = cv2.imwrite(f'data/camera_test/color_{count_num:02d}.jpg', self.color_image)
            print(ret, f'data/camera_test/color_{count_num:02d}.jpg')


def circle_yoz(arm, r_axis=0.1, max_c=2, step_len=45, x_axis=0.523, y_axis=0.03, z_axis=0.6, spd=10, rotate_plane='yoz', rx=0,ry=-1.57,rz=3.14):
    arm_model = rm_robot_arm_model_e.RM_MODEL_RM_63_III_E  # RML63III-BI
    force_type = rm_force_type_e.RM_MODEL_RM_B_E
    algo_handle = Algo(arm_model, force_type)
    count_c = 0
    while count_c < max_c:
        count_c += 1
        for angle in range(0, 360+step_len, step_len):
            if rotate_plane == 'xoz':
                target_coord = [x_axis + math.cos(math.radians(angle)) * r_axis, y_axis,
                                      z_axis + math.sin(math.radians(angle)) * r_axis, rx, ry, rz]
            elif rotate_plane == 'xoy':
                target_coord = [x_axis + math.sin(math.radians(angle)) * r_axis, y_axis + math.cos(math.radians(angle)) * r_axis,
                                z_axis, rx, ry, rz]
            else:
                target_coord = [x_axis, y_axis + math.cos(math.radians(angle)) * r_axis,
                                z_axis + math.sin(math.radians(angle)) * r_axis, rx, ry, rz]

            current_pose = arm.rm_get_current_arm_state()[1]['pose']
            current_joint = arm.rm_get_current_arm_state()[1]['joint']

            params = rm_inverse_kinematics_params_t(current_joint,
                                                    target_coord, 1)
            q_out = algo_handle.rm_algo_inverse_kinematics(params)
            # print(q_out)
            if q_out[0] != 0:
                print("could invert: ", angle, target_coord, q_out)
                break
            ret = arm.rm_movej_p(target_coord, min(80,spd), 0, 0, 1)
            if ret != 0:
                print("could not move: ", target_coord, ret)
                break

def arm_sdk():
    arm = RoboticArm(rm_thread_mode_e.RM_TRIPLE_MODE_E)
    handle = arm.rm_create_robot_arm("************", 8080)
    print("机械臂ID：", handle.id)

    software_info = arm.rm_get_arm_software_info()
    if software_info[0] == 0:
        print("\n================== Arm Software Information ==================")
        print("Arm Model: ", software_info[1]['product_version'])
        print("Algorithm Library Version: ", software_info[1]['algorithm_info']['version'])
        print("Control Layer Software Version: ", software_info[1]['ctrl_info']['version'])
        print("Dynamics Version: ", software_info[1]['dynamic_info']['model_version'])
        print("Planning Layer Software Version: ", software_info[1]['plan_info']['version'])
        print("==============================================================\n")
    else:
        print("\nFailed to get arm software information, Error code: ", software_info[0], "\n")
    print(arm.rm_get_arm_all_state())
    print(arm.rm_get_current_arm_state())
    print('arm test pass')

def camera_sdk():
    camera = Camera()
    camera.run(max_num=10)
    print('camera test pass')

if __name__ == "__main__":
    # arm_sdk()
    camera_sdk()
    exit(1)
