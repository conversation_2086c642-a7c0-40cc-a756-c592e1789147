import cv2
import numpy as np
import sys
import os
from pathlib import Path

# Add the current directory to the Python path to import from buttonControl
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from buttonControl.button_detection import detect_buttons_by_status

def test_single_image(image_path):
    """Test detection on a single image"""
    print(f"Testing image: {image_path}")
    
    # Read the image
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return False
    
    image = cv2.imread(image_path)
    if image is None:
        print(f"Could not read image: {image_path}")
        return False
    
    try:
        # Detect all buttons in uncertain state
        detection_result = detect_buttons_by_status(
            image,
            status='uncertain',
            verbose=True,
            display_process=True
        )
        
        print(f"Detection result type: {type(detection_result)}")
        
        if isinstance(detection_result, dict):
            target = detection_result.get('target')
            print(f"Target detected: {target is not None}")
            return target is not None
        elif isinstance(detection_result, tuple):
            print(f"Tuple result length: {len(detection_result)}")
            if len(detection_result) >= 6:
                display_img, top_row, bottom_row, knob, handle_angle, mode_code = detection_result
                success = (top_row and any(top_row)) and (bottom_row and any(bottom_row))
                print(f"Top row: {top_row}")
                print(f"Bottom row: {bottom_row}")
                print(f"Success: {success}")
                return success
        
        print("Unexpected result format")
        return False
        
    except Exception as e:
        print(f"Error during detection: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test a few images from each category
    mustPass_dir = Path('./data/button-debug-saves/allButtons/mustPass')
    toPass_dir = Path('./data/button-debug-saves/allButtons/toPass')
    
    print("=== Testing mustPass images ===")
    mustPass_images = list(mustPass_dir.glob('*.jpg')) + list(mustPass_dir.glob('*.png'))
    mustPass_success = 0
    
    for i, img_path in enumerate(mustPass_images[:3]):  # Test first 3 images
        print(f"\n--- Test {i+1}/3 ---")
        if test_single_image(str(img_path)):
            mustPass_success += 1
    
    print(f"\nmustPass success rate: {mustPass_success}/3")
    
    print("\n=== Testing toPass images ===")
    toPass_images = list(toPass_dir.glob('*.jpg')) + list(toPass_dir.glob('*.png'))
    toPass_success = 0
    
    for i, img_path in enumerate(toPass_images[:3]):  # Test first 3 images
        print(f"\n--- Test {i+1}/3 ---")
        if test_single_image(str(img_path)):
            toPass_success += 1
    
    print(f"\ntoPass success rate: {toPass_success}/3")
    
    print(f"\nOverall baseline:")
    print(f"mustPass: {mustPass_success}/3 ({mustPass_success/3*100:.1f}%)")
    print(f"toPass: {toPass_success}/3 ({toPass_success/3*100:.1f}%)")
